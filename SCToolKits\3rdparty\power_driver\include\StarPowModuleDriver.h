﻿/********************************************************************************

 **** Copyright (C), 2020,Star Charge

 ********************************************************************************
 * File Name     : StarPowModuleDriver.h
 * Author        : Zhu.XY 朱兴银
 * Date          : 2023-06-07
 * Description   : 星充控制器与模块间通信协议
 * Version       : 初稿
 * Function List :
 *
 * Record        :
 * 1.Date        : 2023-08-02
 *   Author      : Zhu.XY 朱兴银
 *   Modification: Created file


*************************************************************************************************************/



#ifndef __STARPOWER_H__
#define __STARPOWER_H__


#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */
/*----------------------------------------------*
 * 包含头文件                                   *
 *----------------------------------------------*/
#include <stdint.h>
#include "SystemInterface.h"
#include "SimulateInterface.h"
/*----------------------------------------------*
 * 宏定义                                       *
 *----------------------------------------------*/



/*----------------------------------------------*
 * 常量定义                                     *
 *----------------------------------------------*/

/*----------------------------------------------*
 * 外部变量说明                                 *
 *----------------------------------------------*/

/*----------------------------------------------*
 * 外部函数原型说明                             *
 *----------------------------------------------*/

/*----------------------------------------------*
 * 内部函数原型说明                             *
 *----------------------------------------------*/

/*----------------------------------------------*
 * 全局变量                                     *
 *----------------------------------------------*/

/*----------------------------------------------*
 * 模块级变量                                   *
 *----------------------------------------------*/

/*----------------------------------------------*
 * 外部函数原型说明                             *
 *----------------------------------------------*/
void StarPowModule_Init(void);
uint8_t StarPowModule_IsOnline(uint8_t modAddr);

unsigned StarPowModule_GetModuleVol(uint8_t modAddr);
unsigned StarPowModule_GetModuleCur(uint8_t modAddr);
unsigned StarPowModule_GetModuleOnOff(uint8_t modAddr);
unsigned StarPowModule_GetModuleWorkTime(uint8_t modAddr);
unsigned StarPowModule_GetModuleStandbyTime(uint8_t modAddr);
unsigned StarPowModule_GetModuleGrpAddr(uint8_t modAddr);
unsigned StarPowModule_GetModuleCurlimitd(uint8_t modAddr);
int StarPowModule_GetModuleInletTEMP(uint8_t modAddr);
int StarPowModule_GetModulePfcTEMP2(uint8_t modAddr);
int StarPowModule_GetModulePfcTEMP3(uint8_t modAddr);
int StarPowModule_GetModuleInTemp(uint8_t modAddr);
int StarPowModule_GetModuleOutTemp(uint8_t modAddr);
unsigned StarPowModule_GetModuleVab(uint8_t modAddr);
unsigned StarPowModule_GetModuleVbc(uint8_t modAddr);
unsigned StarPowModule_GetModuleVca(uint8_t modAddr);
unsigned StarPowModule_GetModuleOutsideVol(uint8_t modAddr);
unsigned StarPowModule_GetModuleOutVolMode(uint8_t modAddr);
unsigned StarPowModule_GetModuleOutVolStatus(uint8_t modAddr);
unsigned StarPowModule_GetModuleStatus(uint8_t modAddr);
uint8_t* StarPowModule_GetModuleSN(uint8_t modAddr);
unsigned StarPowModule_GetModuleHwVision(uint8_t modAddr);
unsigned StarPowModule_GetModuleMuteState(uint8_t modAddr);
unsigned StarPowModule_GetModuleVolSet(uint8_t modAddr);

void StarPowModule_SetModuleModeHL(uint8_t grpAddr,uint8_t modAddr,unsigned modeHL,uint8_t isRightNow);
void StarPowModule_SetModuleOnOff(uint8_t grpAddr,uint8_t modAddr,unsigned OnOff,uint8_t isRightNow);
void StarPowModule_SetModuleVol(uint8_t grpAddr,uint8_t modAddr,unsigned vol,uint8_t isRightNow);
void StarPowModule_SetModuleCur(uint8_t grpAddr,uint8_t modAddr,unsigned cur,uint8_t isRightNow);
void StarPowModule_SetModuleMuteMode(uint8_t grpAddr,uint8_t modAddr,unsigned mute,uint8_t isRightNow);
void StarPowModule_SetAllData(uint8_t grpAddr,uint8_t modAddr,unsigned mode,unsigned cur,unsigned cellVol,unsigned outVol,uint8_t isRightNow);
void StarPowModule_SetModuleGrp(uint8_t modAddr,uint8_t grpAddr,uint8_t isRightNow);

void StarPowModule_ReadModuleVol(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleCur(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleVolSet(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleStatus(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleModeHL(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleTrueHL(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleMuteMode(uint8_t modAddr,uint8_t grpAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleGrp(uint8_t modAddr,uint8_t grpAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleSN(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void Pro03_StarPowModule_ReadModuleVol(uint8_t modAddr);
void Pro03_StarPowModule_ReadModuleSN(uint8_t modAddr);
void Pro03_StarPowModule_SetModuleTestOut(uint8_t TestOut,uint8_t *pSN);//??????
void Pro03_StarPowModule_SetModuleAddr(uint8_t modAddr,uint8_t *pSN);
void StarPowModule_ReadModuleWorkTime(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleStandbyTime(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleInTemp(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleOutTemp(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleFanRate(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleHwVersion(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleCurCurlimitd(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleCurlimitd(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleHLstatus(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleOutsideVol(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleInletTEMP(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModulePfcTEMP2(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModulePfcTEMP3(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleVab(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleVbc(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
void StarPowModule_ReadModuleVca(uint8_t grpAddr,uint8_t modAddr,uint8_t isRightNow);
int Check_Module_List(MODULEINFO_TO_C *pInit,MODULEINFO_TO_C *pRead);
void Module_RESET(MODULEINFO_TO_C *pInit);
int moduleGetInit(MODULEINFO_TO_C *pInit);
int moduleGetAdjust(MODULEINFO_TO_C *Adjust);
int moduleAdjustState(void);
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __STARPOWER_H__ */
