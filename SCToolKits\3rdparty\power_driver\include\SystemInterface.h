﻿#ifndef systemInterface_H
#define systemInterface_H

#ifdef __cplusplus
#if __cplusplus
  extern "C" {
#endif
#endif /* __cplusplus */
#include <stdint.h>
#include <string.h>
#include <stdio.h>
typedef struct
{
    int moduleNum;
    char SNNum[18][18];
    unsigned char moduleAdd[18];
    unsigned char module<PERSON><PERSON>Ver[18];
}MODULEINFO_TO_C;

void system_ms_change(void);
unsigned system_ms_get(void);
void module_init(MODULEINFO_TO_C *);

void RM_MFuncRegister(void (*mFunc)(void));
void RM_MFuncRun(void);

int fputc(int ch, FILE *f);
int read_output( unsigned char *ptr, unsigned len );
int write_output( unsigned char *ptr );


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif

