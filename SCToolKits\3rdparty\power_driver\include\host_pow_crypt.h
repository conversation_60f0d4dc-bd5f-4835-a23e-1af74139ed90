#ifndef host_pow_crypt_H
#define host_pow_crypt_H

#ifdef __cplusplus
#if __cplusplus
  extern "C" {
#endif
#endif /* __cplusplus */
#include "star_pow_crypt.h"


void host_key_agreement( uint8_t addr, uint8_t *pSN, T_KEY *p_key);
void host_generate_k1(uint8_t addr,T_KEY *);
void host_generate_authentication(uint8_t addr,T_KEY *);
void  host_decrpt_data(uint8_t addr,uint8_t *data, uint16_t len);
void  host_encrpt_data(uint8_t addr,uint8_t *data, uint16_t len);


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif
