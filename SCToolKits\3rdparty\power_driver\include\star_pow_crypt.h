#ifndef star_pow_crypt_H
#define star_pow_crypt_H

#ifdef __cplusplus
#if __cplusplus
  extern "C" {
#endif
#endif /* __cplusplus */
#include <stdint.h>
typedef union _T_KEY
{
    uint64_t key_64;
    uint32_t key_32[2];
    uint16_t key_16[4];
    uint8_t key_8[8];
} T_KEY;

void key_agreement(T_KEY *,T_KEY *);
void star_authentication(T_KEY *,T_KEY *);
void decrpt_data(uint8_t *data, uint16_t len);
void encrpt_data(uint8_t *data, uint16_t len);



#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif
