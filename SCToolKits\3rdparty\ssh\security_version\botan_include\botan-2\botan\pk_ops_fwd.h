/*
* PK Operation Types Forward Decls
* (C) 2016 <PERSON>
*
* <PERSON><PERSON> is released under the Simplified BSD License (see license.txt)
*/

#ifndef BOTAN_PK_OPERATIONS_FWD_H_
#define BOTAN_PK_OPERATIONS_FWD_H_

namespace Botan {

namespace PK_Ops {

class Encryption;
class Decryption;
class Verification;
class Signature;
class Key_Agreement;
class KEM_Encryption;
class KEM_Decryption;

}

}

#endif
