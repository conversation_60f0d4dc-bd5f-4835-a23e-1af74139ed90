import webview
import threading
import time
import re
import sys

# 配置参数
SUCCESS_URL_PATTERNS = [
    r"https://omc.starcharge.com/station/chargingStation/list", # 仪表盘页面
    r"https://omc.starcharge.com/home"
]
TIMEOUT_SECONDS = 120  # 最大等待时间（秒）
POLL_INTERVAL = 0.1   # URL检查间隔（秒）

class LoginResult:
    def __init__(self):
        self.success = False
        self.content = None
        self.final_url = None
        self.error = None

def is_window_active(window):
    """检查窗口是否仍然活动"""
    try:
        # 尝试获取窗口标题 - 如果窗口已关闭会抛出异常
        _ = window.title
        return True
    except Exception:
        return False

def monitor_url(window, result):
    """
    监控URL变化，检测登录成功状态
    """
    try:
        start_time = time.time()
        last_url = ""
        window_closed_by_user = False
        
        while (time.time() - start_time) < TIMEOUT_SECONDS:
            # 检查窗口是否仍然活动
            if not is_window_active(window):
                window_closed_by_user = True
                break
                
            try:
                current_url = window.get_current_url()
                
                # 处理可能的None值（窗口关闭时可能返回None）
                if current_url is None:
                    window_closed_by_user = True
                    break
                
                # 检测URL变化或首次记录
                if current_url != last_url:
                    print(f"当前URL: {current_url}")
                    last_url = current_url
                    
                    # 检查是否符合登录成功模式
                    if any(re.search(pattern, current_url) for pattern in SUCCESS_URL_PATTERNS):
                        result.success = True
                        result.final_url = current_url
                        print(f"检测到登录成功URL: {current_url}")
                        
                        # 在关闭窗口前获取页面内容
                        try:
                            result.content = window.get_html()
                            print("成功获取登录后页面内容")
                        except Exception as e:
                            result.error = f"获取页面内容失败: {str(e)}"
                        
                        # 关闭窗口并退出监控
                        window.destroy()
                        return
                
                time.sleep(POLL_INTERVAL)
                
            except Exception as e:
                # 处理窗口关闭导致的异常
                if "destroyed" in str(e).lower() or "none" in str(e).lower():
                    window_closed_by_user = True
                    break
                print(f"URL监控错误: {str(e)}")
                time.sleep(1)
        
        # 窗口被用户关闭的处理
        if window_closed_by_user and not result.success:
            result.error = "用户关闭了登录窗口"
            print(result.error)
        
        # 超时处理
        elif not result.success:
            result.error = f"错误：{TIMEOUT_SECONDS}秒内未检测到登录成功状态"
            print(result.error)
            
        # 确保关闭窗口
        if is_window_active(window):
            try:
                window.destroy()
            except:
                pass
    
    except Exception as e:
        result.error = f"监控线程内部错误: {str(e)}"
        print(result.error)
        # 确保关闭窗口
        if window and is_window_active(window):
            try:
                window.destroy()
            except:
                pass

def perform_login():
    """
    执行登录流程并返回结果
    """
    result = LoginResult()
    window = None
    
    try:
        # 创建窗口并加载登录页面
        window = webview.create_window(
            '星星身份认证',
            'https://omc.starcharge.com/login',
            width=1000,
            height=700,
            on_top=True
        )
        
        # 启动URL监控线程
        monitor_thread = threading.Thread(
            target=monitor_url, 
            args=(window, result),
            daemon=True
        )
        monitor_thread.start()
        
        print("正在打开登录窗口，请完成登录操作...")
        webview.start()
        
        # 等待监控线程结束或超时
        monitor_thread.join(TIMEOUT_SECONDS + 5)
        
        # 检查监控线程是否还在运行
        if monitor_thread.is_alive():
            print("监控线程仍在运行，尝试终止...")
            # 设置错误信息
            if not result.error:
                result.error = "监控线程超时未退出"
    
    except Exception as e:
        result.error = f"登录流程异常: {str(e)}"
        print(result.error)
    
    finally:
        # 确保关闭窗口（如果仍然活动）
        if window and is_window_active(window):
            try:
                window.destroy()
            except Exception as e:
                print(f"关闭窗口时出错: {str(e)}")
    
    return result

if __name__ == "__main__":
    # 执行登录流程
    login_result = perform_login()
    
    # 输出结果
    print("\n登录结果:")
    print(f"成功: {login_result.success}")
    
    if login_result.success:
        print(f"最终URL: {login_result.final_url}")
        print("\n页面内容预览:")
        print(login_result.content[:1000] + "..." if login_result.content else "无内容")
    elif login_result.error:
        print(f"错误: {login_result.error}")
    else:
        print("未知错误导致登录失败")
    
    # 退出并返回状态码 (1=成功, 0=失败)
    sys.exit(1 if login_result.success else 0)