QT       += core gui network serialport sql printsupport texttospeech serialbus

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets
QMAKE_CXXFLAGS += -Wno-unused-parameter
CONFIG += c++11
# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS
DEFINES +=QT_MESSAGELOGCONTEXT
include(ssh_version_config.pri)
include(complie_config.pri);

equals(SSH_SECURITY_VERSION,1){
    #仅支持mingw 32位版本，不支持64位，且不支持msv版本
    DEFINES += SSH_SECURITY_VERSION
    INCLUDEPATH += $$PWD/3rdparty/ssh/security_version/include
    INCLUDEPATH += $$PWD/3rdparty/ssh/security_version/botan_include/botan-2
    LIBS += -L$$PWD/3rdparty/ssh/security_version/mingw32  -lQSshd
} else {
    INCLUDEPATH += $$PWD/3rdparty/ssh/include
    LIBS += -L$$PWD/3rdparty/ssh/lib -lBotan -lQSsh
}
LIBS +=$$quote(-L$$PWD/3rdparty/openssl_1.1.1/lib/ -lcrypto-1_1)
LIBS +=$$quote(-L$$PWD/3rdparty/openssl_1.1.1/lib/ -lssl-1_1)
INCLUDEPATH +=$$quote(.\3rdparty\openssl_1.1.1\include)


LIBS +=-L$$PWD/3rdparty/guangcheng_can_drivers -lECanVci
INCLUDEPATH +=$$PWD/3rdparty/guangcheng_can_drivers

equals(ZLG_CAN_DRIVER,1){
    LIBS +=-L$$PWD/3rdparty/zlg_can_drivers/zlgcan_x86 -lzlgcan
    INCLUDEPATH +=$$PWD/3rdparty/zlg_can_drivers/zlgcan_x86
}

equals(CURL_ENABLE,1){
    INCLUDEPATH +=$$PWD/3rdparty/curl/include
}
INCLUDEPATH += $$PWD/3rdparty/poppler/include
LIBS += -L$$PWD/3rdparty/ld_share_lib -lcurl -lpoppler-qt5

LIBS +=-L$$PWD/3rdparty/power_driver/lib  -lPowModDrvLib
INCLUDEPATH +=$$PWD/3rdparty/power_driver/include

INCLUDEPATH +=$$PWD/3rdparty/qrcode/include
LIBS +=-L$$PWD/3rdparty/qrcode/lib  -lqrcode

equals(ENALBE_CUSTOM_PLOT,1){
    INCLUDEPATH += $$PWD/3rdparty/customplot/include
    LIBS += -L$$PWD/3rdparty/ld_share_lib -lcustomplot
}

#git lib
INCLUDEPATH +=$$PWD/3rdparty/git_lab/include
LIBS += -L$$PWD/3rdparty/git_lab/lib/ -lgit2

#xlsx lib
equals(ENABLE_XLSX,1){
INCLUDEPATH +=$$PWD/3rdparty/xlsx/include
LIBS +=$$PWD/3rdparty/xlsx/lib/Qt5Xlsx.dll
}

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    3rd_aux/aes_encryption.cpp \
    3rd_aux/hmac_md5.cpp \
    3rd_aux/password_helper.cpp \
    app_info/app_authority_manager.cpp \
    app_info/app_info.cpp \
    app_start/app_config.cpp \
    app_start/main.cpp \
    base_frame/toolkits_manager.cpp \
    business/ageing_check/ageing_worker.cpp \
    business/ageing_check/pcs_power.cpp \
    business/apn_switch_over/apn_switch_over_worker.cpp \
    business/bess_cabinet/bess_cabinet_worker.cpp \
    business/bmw/bmw_config_data.cpp \
    business/bmw/bmw_config_worker.cpp \
    business/builders/fct_board_builder.cpp \
    business/builders/ftp_test_object_builder.cpp \
    business/builders/ssh_operate_builder.cpp \
    business/builders/test_object_builder.cpp \
    business/circuit_board/ac_meter/ac_meter_board_builder.cpp \
    business/circuit_board/arc_uk/arc_circuit_board_builder.cpp \
    business/circuit_board/arc_uk/relay_board_builder.cpp \
    business/circuit_board/bluetooth_interface_board/bluetooth_board_builder.cpp \
    business/circuit_board/ccu_board/ionchi_ccu_board_builder.cpp \
    business/circuit_board/ccu_board/xgb_ccu_board_builder.cpp \
    business/circuit_board/dc_control_board/dc_control_board_builder.cpp \
    business/circuit_board/dc_control_board/dc_top_main_board_builder.cpp \
    business/circuit_board/dc_pre_charge_board/dc_pre_charge_board_builder.cpp \
    business/circuit_board/dpau_control_board/dpau_control_board_builder.cpp \
    business/circuit_board/environmental_board/environmental_board_builder.cpp \
    business/circuit_board/insulation_board/insulation_board_builder.cpp \
    business/circuit_board/mcc_board/mcc_board_builder.cpp \
    business/circuit_board/pdu_control_board/pdu_control_board_builder.cpp \
    business/circuit_board/schneider_board/schneider_ctrl_board_builder.cpp \
    business/circuit_board/schneider_board/schneider_new_resi_ctrl_board_builder.cpp \
    business/circuit_board/schneider_board/schneider_new_resi_power_board_builder.cpp \
    business/circuit_board/schneider_board/schneider_power_board_builder.cpp \
    business/circuit_board/secc_board/secc_board_builder.cpp \
    business/circuit_board/liquid_cool/liquid_cool_board_builder.cpp \
    business/circuit_board/stepping_motor/stepping_motor_board_builder.cpp \
    business/circuit_board/tuxing/tuxing_board_builder.cpp \
    business/common_share/device_mes_worker.cpp \
    business/ecc/ecc_multi_fun_worker.cpp \
    business/ecc/iecc_worker.cpp \
    business/ecc/worker/ecc_fct_test_worker.cpp \
    business/evcc/evcc_charge_fct.cpp \
    business/evcc/evcc_data_context.cpp \
    business/flash_writer/JLink/unified_writer.cpp \
    business/flash_writer/flash_writer_worker.cpp \
    business/flash_writer/nu_writer/nu_security_a35_writer.cpp \
    business/flash_writer/nu_writer/nu_writer.cpp \
    business/gb_pdu_check/gb_pdu_check_worker.cpp \
    business/log_analyze/intl_data.cpp \
    business/log_analyze/log_analyzer.cpp \
    business/log_analyze/log_file_worker.cpp \
    business/log_analyze/log_info_tree_intl.cpp \
    business/meter_verify_tool/ac_meter/ac_meter_material_data.cpp \
    business/meter_verify_tool/ac_meter/ac_meter_verify_worker.cpp \
    business/meter_verify_tool/charge_cable_loss/charge_cable_material_data.cpp \
    business/meter_verify_tool/charge_cable_loss/i_meter_worker.cpp \
    business/meter_verify_tool/charge_cable_loss/meter_loss_config_builder.cpp \
    business/meter_verify_tool/charge_cable_loss/meter_loss_config_worker.cpp \
    business/meter_verify_tool/charge_cable_loss/meter_workers/acrel_meter_worker.cpp \
    business/meter_verify_tool/charge_cable_loss/meter_workers/reallin_meter_worker.cpp \
    business/meter_verify_tool/meterage_module/meterage_module_verify_worker.cpp \
    business/network_check/bluetooth/bluetooth_check.cpp \
    business/network_check/dual_network/dualnetwork_check.cpp \
    business/network_check/ethernet/ethernet_check.cpp \
    business/network_config/Language/language_config.cpp \
    business/one_click_config/bmw_config/bmw_config.cpp \
    business/one_click_config/gb/gb_config.cpp \
    business/one_click_config/international_dc/authority_key_config.cpp \
    business/one_click_config/international_dc/interantional_dc.cpp \
    business/one_click_config/iso15118/iso15118_config.cpp \
    business/one_click_config/test_tool_barcode/test_tool_barcode_config.cpp \
    business/pin_code/pin_code_worker.cpp \
    business/printer/bwm_printer/bwm_printer_context.cpp \
    business/printer/pin_printrer/pin_printer_context.cpp \
    business/printer/printer_worker.cpp \
    business/printer/wifi_printer/wifi_printer_context.cpp \
    business/rules/material_rules.cpp \
    business/rules/random_seed.cpp \
    business/rules/sn_rules.cpp \
    business/secc/upgrade_evcc_plc/upgrade_evcc_plc_worker.cpp \
    business/security/certificate_worker.cpp \
    business/security/nxp_edge_lock_go/nxp_edge_go_server.cpp \
    business/security/se05x/se05x.cpp \
    business/workers/file_worker.cpp \
    business/workers/ssh_query_worker.cpp \
    business/workers/process_worker.cpp \
    common_base/test_manage/test_info.cpp \
    communication/can/can_client.cpp \
    communication/can/gc_can/gc_can_client.cpp \
    communication/can/zlg_can/zlg_can_client.cpp \
    communication/modbus/modbus_tcp_client.cpp \
    controller/ageing_check/ageing_controller.cpp \
    controller/bess_cabinet/bess_cabinet_controller.cpp \
    controller/bmw/bmw_config_controller.cpp \
    controller/ecc/ecc_multi_fun_controller.cpp \
    controller/evcc/evcc_integrate_controller.cpp \
    controller/gb_pdu_check/gb_pdu_check_controller.cpp \
    controller/menu/config_controller.cpp \
    controller/menu/debug_controller.cpp \
    controller/multi_actions/multi_actions_controller.cpp \
    controller/one_click_burn/nu_handler.cpp \
    controller/one_click_config/intl_dc_config_handler.cpp \
    controller/print/print_controller.cpp \
    data/devices/device_context.cpp \
    business/circuit_board/artemis/artemis_circuit_board_builder.cpp \
    business/flash_writer/JLink/jlink_write_builder.cpp \
    business/flash_writer/JLink/jlink_write_worker.cpp \
    business/card_config/card_config_operate_builder.cpp \
    business/card_config/card_config_worker.cpp \
    business/circuit_board/am62/circuit_board_builder.cpp \
    business/common_share/factory_test_policy.cpp \
    business/flash_writer/usb/usb_write_worker.cpp \
    business/mass_enery_store/mass_enery_store_worker.cpp \
    business/mistake_proofing/mistake_proofing_builder.cpp \
    business/mistake_proofing/mistake_proofing_worker.cpp \
    business/workers/curl_test_worker.cpp \
    business/workers/flow_test_worker.cpp \
    business/web_config/web_config_worker.cpp \
    business/common_share/interface_business.cpp \
    business/common_share/serial_port_selector.cpp \
    business/common_share/test_manager.cpp \
    business/common_share/test_result.cpp \
    business/workers/test_worker.cpp \
    business/workers/ssh_test_worker.cpp \
    business/equipments/equipment_add_worker.cpp \
    business/equipments/equipment_search_worker.cpp \
    business/flash_writer/light_board_upgrade/light_board_upgrade_worker.cpp \
    business/hardware/sdcard.cpp \
    business/hardware/sdcard_builder.cpp \
    business/infrared_module/infrared_module_tool_message_worker.cpp \
    business/light_color_check/light_color_check_worker.cpp \
    business/network_check/4G/forth_g_check.cpp \
    business/network_check/wifi/wifi_check.cpp \
    business/network_config/DNS/dns_config.cpp \
    business/network_config/4G/forth_g_config.cpp \
    business/network_config/network_config_builder.cpp \
    business/network_config/OCPP/ocpp_config.cpp \
    business/network_config/Restart/restart_config.cpp \
    business/one_click_config/one_click_config_builder.cpp \
    business/one_click_config/exec_script/script_config.cpp \
    business/power_comm/power_comm_extfunrunthread.cpp \
    business/power_comm/power_comm_manager.cpp \
#旧版本电源模块模拟器，暂时保留，但不编译，后续考虑删除
#    business/power_simulator/cpmodule_manager.cpp \
#    business/power_simulator/module_details_manager.cpp \
#    business/power_simulator/set_activation_manager.cpp \
    business/power_simulator/power_simulator_manager.cpp \
    business/secc/secc_log_analyze/secc_log_analyzer.cpp \
    business/secc/secc_mac_config/secc_mac_config_worker.cpp \
    business/secc/rkn_secc/rkn_protocol_worker.cpp \
    business/trace_recorder.cpp \
    business/voltage_check/voltage_check_worker.cpp \
    business/one_click_config/pileId/pileId_config.cpp \
    business/units_aux/tip_speaker.cpp \
    common_base/log/log_output_manager.cpp \
    common_base/database/sqlite_manager.cpp \
    communication/can/can_worker.cpp \
    communication/ethernet/ethernet_client.cpp \
    communication/http/http_client.cpp \
    communication/http/https_client_v2.cpp \
    communication/link_manager.cpp \
    communication/safety_version_manager.cpp \
    communication/serial/serial_worker.cpp \
    communication/ssh/ssh_client.cpp \
    communication/ssh/ftp_client.cpp \
    communication/web/web_client.cpp \
    controller/controller_manager.cpp \
    controller/light_color_check/light_color_check_controller.cpp \
    controller/mainwindow_controller.cpp \
    controller/mass_enery_store/mass_energy_store_controller.cpp \
    controller/menu/equipment_menu_controller.cpp \
    controller/menu/help_controller.cpp \
    controller/menu/setting_menu_controller.cpp \
    controller/meter_verify/meterverify_tool_controller.cpp \
    controller/network_check/network_check_controller.cpp \
    controller/network_config/network_config_controller.cpp \
    controller/one_click_burn/one_click_burn_controller.cpp \
    controller/one_click_config/one_click_config_controller.cpp \
    controller/random_generator/random_generator_controller.cpp \
    controller/power_comm/power_comm_controller.cpp \
    controller/power_simulator/power_simulator_controller.cpp \
    controller/power_upgrade/power_upgrade_controller.cpp \
    controller/secc/rkn_secc/rkn_tool_controller.cpp \
    controller/secc/secc_log_analyze/secc_log_analyze_controller.cpp \
    controller/secc/secc_mac/secc_controller.cpp \
    controller/infrared_module/infrared_module_tool_controller.cpp \
    controller/tip_controller.cpp \
    controller/voltage_check/voltage_check_controller.cpp \
    controller/single_board_check/single_board_check_controller.cpp \
    controller/card_write_system/card_write_system_controller.cpp \
    controller/log_analyze/log_analyzer_controller.cpp \
    controller/web_config/web_config_controller.cpp \
    data/can_config/canconfiginfo_datacenter.cpp \
    data/factory_line/factory_line_context.cpp \
    data/interface_data/interface_data.cpp \
    data/light_color_check/light_color_check_data.cpp \
    data/material_base/material_base.cpp \
    data/pcs/pcs_data.cpp \
    data/power_comm/power_comm_data.cpp \
    data/power_simulator/power_simulator_datacenter.cpp \
    data/protocol/modbus_protocol.cpp \
    data/protocol/protocol_maintainer.cpp \
    data/protocol/third_party_protocol.cpp \
    data/test_objects/pin_ciruit.cpp \
    data/test_objects/pin_serial_port.cpp \
    data/test_objects/shang_liang_voltage.cpp \
    data/test_objects/test_object.cpp \
    data/test_objects/test_operate.cpp \
    gui/ageing/ageing_window.cpp \
    gui/bess_cabinet/bess_cabinet_window.cpp \
    gui/bmw/bmw_config_window.cpp \
    gui/can_config/can_config_window.cpp \
    gui/card_write_system/menu_window.cpp \
    gui/card_write_system/shearch_workeroder_window.cpp \
    gui/ecc/ecc_log_window.cpp \
    gui/ecc/ecc_multi_functional_window.cpp \
    gui/evcc/evcc_integrate_window.cpp \
    gui/gb_pdu_check/gb_pdu_check_window.cpp \
    gui/light_color_check/light_color_check_window.cpp \
    gui/log_analyze/aux_log_analyzer_window.cpp \
    gui/log_analyze/log_analyzer_window.cpp \
    gui/mainwindow.cpp \
    gui/mass_enery_store/mass_energy_store_window.cpp \
    gui/menus/debug/debugging_window.cpp \
    gui/menus/helps/about_app_info.cpp \
    gui/menus/helps/instructions_for_use.cpp \
    gui/menus/helps/pdf_page_window.cpp \
    gui/menus/settings/authority.cpp \
    gui/menus/settings/config_window.cpp \
    gui/menus/settings/test_object_generate_window.cpp \
    gui/menus/equipments/equipment_add_window.cpp \
    gui/menus/equipments/equipment_search.cpp \
    gui/mes_login/mes_login_window.cpp \
    gui/multi_actions/multi_actions_window.cpp \
    gui/network_check/network_check_window.cpp \
    gui/network_check/wifi_conf_window.cpp \
    gui/network_config/network_cfg_menu_window.cpp \
    gui/network_config/network_config_window.cpp \
    gui/inputbox.cpp \
    gui/meter_verify/meterverify_tool_window.cpp \
    gui/one_click_burn/one_click_burn_window.cpp \
    gui/one_click_config/one_click_config_window.cpp \
    business/one_click_config/schneider_config/schneider_config.cpp \
    gui/power_comm/cpmodulecomm_widget.cpp \
#旧版本电源模块模拟器，暂时保留，但不编译，后续考虑删除
#    gui/power_simulator/cpmodule_window.cpp \
#    gui/power_simulator/module_details_window.cpp \
#    gui/power_simulator/set_activation_window.cpp \
    gui/power_simulator/power_simulator_window.cpp \
    gui/power_upgrade/can_control_window.cpp \
    gui/power_upgrade/debug_window.cpp \
    gui/power_upgrade/set_read_module.cpp \
    gui/printer/printer_window.cpp \
    gui/random_generator/random_generator_window.cpp \
    gui/secc/rkn_secc/rkntoolsmainwindow.cpp \
    gui/secc/secc_log_analyze/secc_log_analyze_mainwindow.cpp \
    gui/secc/secc_mac/login_window.cpp \
    gui/secc/secc_mac/mac_window.cpp \
    gui/infrared_module/infrared_module_tool_window.cpp \
    gui/secc/secc_mac/mousehover.cpp \
    gui/secc/secc_mac/pet_image.cpp \
    gui/link_window/serial_port_dialog.cpp \
    gui/link_window/ip_context.cpp \
    gui/single_board_check/test_case_choice_window.cpp \
    gui/tip_window.cpp \
    gui/voltage_check/voltage_check_window.cpp \
    gui/single_board_check/single_board_check_window.cpp \
    gui/card_write_system/card_write_system_window.cpp \
    gui/web_config/web_config_window.cpp \
    mes/mes_manager.cpp \
    mes/mes_worker.cpp \
    data/infrared_module/infrared_module_test_data.cpp \
    data/secc/evcc_information_centre.cpp \
    data/secc/secc_data_centre.cpp \
    data/secc_mac/secc_mac_data.cpp \
    3rd_aux/qcustomplot.cpp \
    3rd_aux/bigdata.cpp \
    3rd_aux/crc.cpp \
    mes/tools_mes_info.cpp \
    simple_funs/characters/character_format.cpp \
    simple_funs/git/git_client.cpp \
    simple_funs/xlsx/xlsx_client.cpp \
    simple_funs/xml/xml_parser.cpp

HEADERS += \
    3rd_aux/aes_encryption.h \
    3rd_aux/hmac_md5.h \
    3rd_aux/password_helper.h \
    app_info/app_authority_level.h \
    app_info/app_authority_manager.h \
    app_info/app_info.h \
    app_start/app_config.h \
    app_start/global.h \
    base_frame/functions_def.h \
    base_frame/toolkits_manager.h \
    business/ageing_check/ageing_worker.h \
    business/ageing_check/pcs_power.h \
    business/apn_switch_over/apn_switch_over_worker.h \
    business/bess_cabinet/bess_cabinet_worker.h \
    business/bmw/bmw_config_data.h \
    business/bmw/bmw_config_worker.h \
    business/builders/fct_board_builder.h \
    business/builders/ftp_test_object_builder.h \
    business/builders/ssh_operate_builder.h \
    business/builders/test_object_builder.h \
    business/circuit_board/ac_meter/ac_meter_board_builder.h \
    business/circuit_board/arc_uk/arc_circuit_board_builder.h \
    business/circuit_board/arc_uk/relay_board_builder.h \
    business/circuit_board/bluetooth_interface_board/bluetooth_board_builder.h \
    business/circuit_board/ccu_board/ionchi_ccu_board_builder.h \
    business/circuit_board/ccu_board/xgb_ccu_board_builder.h \
    business/circuit_board/dc_control_board/dc_control_board_builder.h \
    business/circuit_board/dc_control_board/dc_top_main_board_builder.h \
    business/circuit_board/dc_pre_charge_board/dc_pre_charge_board_builder.h \
    business/circuit_board/dpau_control_board/dpau_control_board_builder.h \
    business/circuit_board/environmental_board/environmental_board_builder.h \
    business/circuit_board/insulation_board/insulation_board_builder.h \
    business/circuit_board/mcc_board/mcc_board_builder.h \
    business/circuit_board/pdu_control_board/pdu_control_board_builder.h \
    business/circuit_board/schneider_board/schneider_ctrl_board_builder.h \
    business/circuit_board/schneider_board/schneider_new_resi_ctrl_board_builder.h \
    business/circuit_board/schneider_board/schneider_new_resi_power_board_builder.h \
    business/circuit_board/schneider_board/schneider_power_board_builder.h \
    business/circuit_board/secc_board/secc_board_builder.h \
    business/circuit_board/liquid_cool/liquid_cool_board_builder.h \
    business/circuit_board/stepping_motor/stepping_motor_board_builder.h \
    business/circuit_board/tuxing/tuxing_board_builder.h \
    business/common_share/device_mes_worker.h \
    business/ecc/ecc_multi_fun_worker.h \
    business/ecc/iecc_worker.h \
    business/ecc/worker/ecc_fct_test_worker.h \
    business/evcc/evcc_charge_fct.h \
    business/evcc/evcc_data_context.h \
    business/flash_writer/JLink/jlink_data.h \
    business/flash_writer/JLink/unified_writer.h \
    business/flash_writer/flash_writer_worker.h \
    business/flash_writer/nu_writer/nu_security_a35_writer.h \
    business/flash_writer/nu_writer/nu_writer.h \
    business/flash_writer/writer_executor.h \
    business/gb_pdu_check/gb_pdu_check_worker.h \
    business/log_analyze/intl_data.h \
    business/log_analyze/log_analyzer.h \
    business/log_analyze/log_file_worker.h \
    business/log_analyze/log_info_tree_intl.h \
    business/meter_verify_tool/ac_meter/ac_meter_material_data.h \
    business/meter_verify_tool/ac_meter/ac_meter_verify_worker.h \
    business/meter_verify_tool/charge_cable_loss/charge_cable_material_data.h \
    business/meter_verify_tool/charge_cable_loss/i_meter_worker.h \
    business/meter_verify_tool/charge_cable_loss/meter_loss_config_builder.h \
    business/meter_verify_tool/charge_cable_loss/meter_loss_config_worker.h \
    business/meter_verify_tool/charge_cable_loss/meter_workers/acrel_meter_worker.h \
    business/meter_verify_tool/charge_cable_loss/meter_workers/reallin_meter_worker.h \
    business/meter_verify_tool/meterage_module/meterage_module_verify_worker.h \
    business/network_check/bluetooth/bluetooth_check.h \
    business/network_check/dual_network/dualnetwork_check.h \
    business/network_check/ethernet/ethernet_check.h \
    business/network_config/Language/language_config.h \
    business/one_click_config/bmw_config/bmw_config.h \
    business/one_click_config/gb/gb_config.h \
    business/one_click_config/international_dc/authority_key_config.h \
    business/one_click_config/international_dc/interantional_dc.h \
    business/one_click_config/iso15118/iso15118_config.h \
    business/one_click_config/test_tool_barcode/test_tool_barcode_config.h \
    business/pin_code/pin_code_worker.h \
    business/printer/bwm_printer/bwm_printer_context.h \
    business/printer/i_printer_context.h \
    business/printer/pin_printrer/pin_printer_context.h \
    business/printer/printer_worker.h \
    business/printer/wifi_printer/wifi_printer_context.h \
    business/rules/material_rules.h \
    business/rules/random_seed.h \
    business/rules/sn_rules.h \
    business/secc/upgrade_evcc_plc/upgrade_evcc_plc_worker.h \
    business/security/certificate_worker.h \
    business/security/nxp_edge_lock_go/nxp_edge_go_server.h \
    business/security/se05x/se05x.h \
    business/workers/file_worker.h \
    business/workers/ssh_query_worker.h \
    common_base/test_manage/test_info.h \
    communication/can/can_client.h \
    communication/can/gc_can/gc_can_client.h \
    communication/can/zlg_can/zlg_can_client.h \
    communication/modbus/modbus_tcp_client.h \
    controller/ageing_check/ageing_controller.h \
    controller/bess_cabinet/bess_cabinet_controller.h \
    controller/bmw/bmw_config_controller.h \
    controller/ecc/ecc_multi_fun_controller.h \
    controller/evcc/evcc_integrate_controller.h \
    controller/gb_pdu_check/gb_pdu_check_controller.h \
    controller/infos_def.h \
    controller/interface_handler.h \
    controller/menu/config_controller.h \
    controller/menu/debug_controller.h \
    controller/multi_actions/multi_actions_controller.h \
    controller/one_click_burn/interface_burn_handler.h \
    controller/one_click_burn/nu_handler.h \
    controller/one_click_config/interface_one_click_config_handler.h \
    controller/one_click_config/intl_dc_config_handler.h \
    controller/print/print_controller.h \
    data/devices/device_context.h \
    business/circuit_board/artemis/artemis_circuit_board_builder.h \
    business/flash_writer/JLink/jlink_write_builder.h \
    business/flash_writer/JLink/jlink_write_worker.h \
    business/card_config/card_config_operate_builder.h \
    business/circuit_board/am62/circuit_board_builder.h \
    business/card_config/card_config_worker.h \
    business/common_share/factory_test_policy.h \
    business/flash_writer/usb/usb_write_worker.h \
    business/mass_enery_store/mass_enery_store_worker.h \
    business/mistake_proofing/mistake_proofing_builder.h \
    business/mistake_proofing/mistake_proofing_worker.h \
    business/workers/curl_test_worker.h \
    business/workers/flow_test_worker.h \
    business/workers/process_worker.h \
    business/web_config/web_config_worker.h \
    business/common_share/interface_business.h \
    business/common_share/serial_port_selector.h \
    business/common_share/test_manager.h \
    business/common_share/test_result.h \
    business/workers/test_worker.h \
    business/workers/ssh_test_worker.h \
    business/equipments/equipment_add_worker.h \
    business/equipments/equipment_search_worker.h \
    business/flash_writer/light_board_upgrade/light_board_upgrade_worker.h \
    business/hardware/sdcard.h \
    business/hardware/sdcard_builder.h \
    business/infrared_module/infrared_module_tool_message_worker.h \
    business/interface_test_policy.h \
    business/light_color_check/light_color_check_worker.h \
    business/network_check/4G/forth_g_check.h \
    business/network_check/wifi/wifi_check.h \
    business/network_config/DNS/dns_config.h \
    business/network_config/4G/forth_g_config.h \
    business/network_config/network_config_builder.h \
    business/network_config/OCPP/ocpp_config.h \
    business/network_config/Restart/restart_config.h \
    business/one_click_config/one_click_config_builder.h \
    business/one_click_config/schneider_config/schneider_config.h \
    business/one_click_config/exec_script/script_config.h \
    business/power_comm/power_comm_extfunrunthread.h \
    business/power_comm/power_comm_extlibheadfile.h \
    business/power_comm/power_comm_manager.h \
#旧版本电源模块模拟器，暂时保留，但不编译，后续考虑删除
#    business/power_simulator/cpmodule_manager.h \
#    business/power_simulator/module_details_manager.h \
#    business/power_simulator/set_activation_manager.h \
    business/power_simulator/power_simulator_extlibheadfile.h \
    business/power_simulator/power_simulator_manager.h \
    business/secc/rkn_secc/rkn_protocol_worker.h \
    business/secc/secc_log_analyze/secc_logan_alyzer.h \
    business/secc/secc_mac_config/secc_mac_config_worker.h \
    business/trace_recorder.h \
    business/voltage_check/voltage_check_worker.h \
    business/one_click_config/pileId/pileId_config.h \
    business/units_aux/tip_speaker.h \
    common_base/log/log_output_manager.h \
    common_base/database/sqlite_manager.h \
    communication/can/can_worker.h \
    communication/ethernet/ethernet_client.h \
    communication/http/http_client.h \
    communication/http/https_client_v2.h \
    communication/link_manager.h \
    communication/safety_version_manager.h \
    communication/serial/serial_worker.h \
    communication/ssh/ssh_client.h \
    communication/ssh/ftp_client.h \
    communication/web/web_client.h \
    controller/controller_manager.h \
    controller/interface_controller.h \
    controller/light_color_check/light_color_check_controller.h \
    controller/mainwindow_controller.h \
    controller/mass_enery_store/mass_energy_store_controller.h \
    controller/menu/equipment_menu_controller.h \
    controller/menu/help_controller.h \
    controller/menu/menu_action_def.h \
    controller/menu/setting_menu_controller.h \
    controller/meter_verify/meterverify_tool_controller.h \
    controller/network_check/network_check_controller.h \
    controller/network_config/network_config_controller.h \
    controller/one_click_burn/one_click_burn_controller.h \
    controller/one_click_config/one_click_config_controller.h \
    controller/random_generator/random_generator_controller.h \
    controller/power_comm/power_comm_controller.h \
    controller/power_simulator/power_simulator_controller.h \
    controller/power_upgrade/power_upgrade_controller.h \
    controller/secc/rkn_secc/rkn_tool_controller.h \
    controller/secc/secc_log_analyze/secc_log_analyze_controller.h \
    controller/secc/secc_mac/secc_controller.h \
    controller/infrared_module/infrared_module_tool_controller.h \
    controller/tip_controller.h \
    controller/voltage_check/voltage_check_controller.h \
    controller/single_board_check/single_board_check_controller.h \
    controller/card_write_system/card_write_system_controller.h \
    controller/log_analyze/log_analyzer_controller.h \
    controller/web_config/web_config_controller.h \
    data/can_config/canconfiginfo_datacenter.h \
    data/ecc/ecc_data.h \
    data/factory_line/factory_line_context.h \
    data/interface_data/gui_data.h \
    data/interface_data/interface_data.h \
    data/light_color_check/light_color_check_data.h \
    data/material_base/material_base.h \
    data/pcs/pcs_data.h \
    data/power_comm/power_comm_data.h \
    data/power_module/power_common_data_type.h \
    data/power_simulator/power_simulator_datacenter.h \
    data/protocol/modbus_protocol.h \
    data/protocol/protocol_maintainer.h \
    data/protocol/third_party_protocol.h \
    data/test_objects/pin_ciruit.h \
    data/test_objects/pin_serial_port.h \
    data/test_objects/shang_liang_voltage.h \
    data/test_objects/support_test_define.h \
    data/test_objects/test_object.h \
    data/test_objects/test_operate.h \
    gui/ageing/ageing_window.h \
    gui/bess_cabinet/bess_cabinet_window.h \
    gui/bmw/bmw_config_window.h \
    gui/can_config/can_config_window.h \
    gui/card_write_system/menu_window.h \
    gui/card_write_system/shearch_workeroder_window.h \
    gui/ecc/ecc_log_window.h \
    gui/ecc/ecc_multi_functional_window.h \
    gui/evcc/evcc_integrate_window.h \
    gui/gb_pdu_check/gb_pdu_check_window.h \
    gui/light_color_check/light_color_check_window.h \
    gui/log_analyze/aux_log_analyzer_window.h \
    gui/log_analyze/log_analyzer_window.h \
    gui/mainwindow.h \
    gui/mass_enery_store/mass_energy_store_window.h \
    gui/menus/debug/debugging_window.h \
    gui/menus/helps/about_app_info.h \
    gui/menus/helps/instructions_for_use.h \
    gui/menus/helps/pdf_page_window.h \
    gui/menus/settings/authority.h \
    gui/menus/settings/config_window.h \
    gui/menus/settings/test_object_generate_window.h \
    gui/menus/equipments/equipment_search.h \
    gui/menus/equipments/equipment_add_window.h \
    gui/mes_login/mes_login_window.h \
    gui/inputbox.h \
    gui/meter_verify/meterverify_tool_window.h \
    gui/multi_actions/multi_actions_window.h \
    gui/network_check/network_check_window.h \
    gui/network_check/wifi_conf_window.h \
    gui/network_config/network_cfg_menu_window.h \
    gui/network_config/network_config_window.h \
    gui/one_click_burn/one_click_burn_window.h \
    gui/one_click_config/one_click_config_window.h \
    gui/power_comm/cpmodulecomm_widget.h \
#旧版本电源模块模拟器，暂时保留，但不编译，后续考虑删除
#    gui/power_simulator/cpmodule_window.h \
#    gui/power_simulator/module_details_window.h \
#    gui/power_simulator/set_activation_window.h \
    gui/power_simulator/power_simulator_window.h \
    gui/power_upgrade/can_control_window.h \
    gui/power_upgrade/debug_window.h \
    gui/printer/printer_window.h \
    gui/random_generator/random_generator_window.h \
    gui/secc/rkn_secc/rkntoolsmainwindow.h \
    gui/secc/secc_log_analyze/secc_log_analyze_mainwindow.h \
    gui/secc/secc_mac/login_window.h \
    gui/secc/secc_mac/mac_window.h \
    gui/infrared_module/infrared_module_tool_window.h \
    gui/secc/secc_mac/mousehover.h \
    gui/secc/secc_mac/pet_image.h \
    gui/link_window/serial_port_dialog.h \
    gui/link_window/ip_context.h \
    gui/single_board_check/test_case_choice_window.h \
    gui/tip_window.h \
    gui/voltage_check/voltage_check_window.h \
    gui/single_board_check/single_board_check_window.h \
    gui/card_write_system/card_write_system_window.h \
    gui/web_config/web_config_window.h \
    mes/mes_manager.h \
    mes/mes_worker.h \
    data/infrared_module/infrared_module_test_data.h \
    data/secc/evcc_information_centre.h \
    data/secc/secc_data_centre.h \
    data/secc_mac/secc_mac_data.h \
    mes/tools_mes_info.h \
    3rd_aux/bigdata.h \
    3rd_aux/crc.h \
    3rd_aux/stringfwd.h \
    3rd_aux/qcustomplot.h \ \
    simple_funs/characters/character_format.h \
    simple_funs/git/git_client.h \
    simple_funs/xlsx/xlsx_client.h \
    simple_funs/xml/xml_parser.h

FORMS += \
    gui/ageing/ageing_window.ui \
    gui/bess_cabinet/bess_cabinet_window.ui \
    gui/bmw/bmw_config_window.ui \
    gui/can_config/can_config_window.ui \
    gui/card_write_system/menu_window.ui \
    gui/card_write_system/shearch_workeroder_window.ui \
    gui/ecc/ecc_log_window.ui \
    gui/ecc/ecc_multi_functional_window.ui \
    gui/evcc/evcc_integrate_window.ui \
    gui/gb_pdu_check/gb_pdu_check_window.ui \
    gui/light_color_check/light_color_check_window.ui \
    gui/log_analyze/aux_log_analyzer_window.ui \
    gui/log_analyze/log_analyzer_window.ui \
    gui/mass_enery_store/mass_energy_store_window.ui \
    gui/menus/debug/debugging_window.ui \
    gui/menus/helps/about_app_info.ui \
    gui/menus/helps/instructions_for_use.ui \
    gui/menus/settings/authority.ui \
    gui/menus/settings/config_window.ui \
    gui/menus/settings/test_object_generate_window.ui \
    gui/menus/equipments/equipment_add_window.ui \
    gui/menus/equipments/equipment_search.ui \
    gui/mes_login/mes_login_window.ui \
    gui/inputbox.ui \
    gui/meter_verify/meterverify_tool_window.ui \
    gui/multi_actions/multi_actions_window.ui \
    gui/network_check/network_check_window.ui \
    gui/network_check/wifi_conf_window.ui \
    gui/network_config/network_cfg_menu_window.ui \
    gui/network_config/network_config_window.ui \
    gui/one_click_burn/one_click_burn_window.ui \
    gui/one_click_config/one_click_config_window.ui \
    gui/power_comm/cpmodulecomm_widget.ui \
#旧版本电源模块模拟器，暂时保留，但不编译，后续考虑删除
#    gui/power_simulator/cpmodule_window.ui \
#    gui/power_simulator/module_details_window.ui \
#    gui/power_simulator/set_activation_window.ui \
    gui/power_simulator/power_simulator_window.ui \
    gui/power_upgrade/can_control_window.ui \
    gui/power_upgrade/debug_window.ui \
    gui/printer/printer_window.ui \
    gui/random_generator/random_generator_window.ui \
    gui/secc/rkn_secc/rkntoolsmainwindow.ui \
    gui/secc/secc_log_analyze/secc_log_analyze_mainwindow.ui \
    gui/secc/secc_mac/mac_window.ui \
    gui/secc/secc_mac/mesloginwindow.ui \
    gui/infrared_module/infrared_module_tool_window.ui \
    gui/secc/secc_mac/pet_image.ui \
    gui/link_window/serial_port_dialog.ui \
    gui/single_board_check/test_case_choice_window.ui \
    gui/tip_window.ui \
    gui/voltage_check/voltage_check_window.ui \
    gui/single_board_check/single_board_check_window.ui \
    gui/card_write_system/card_write_system_window.ui \
    gui/mainwindow.ui \
    gui/web_config/web_config_window.ui \

INCLUDEPATH +=$$PWD/controller  $$PWD/common_base $$PWD/gui $$PWD/communication $$PWD/business $$PWD/data $$PWD/app_info $$PWD/3rd_aux $$PWD/app_start $$PWD/base_frame $$PWD/simple_funs $$PWD/3rdparty
# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
RC_ICONS = toolKits.ico

TRANSLATIONS += \
    gui_english.ts \
    gui_chinese.ts
RESOURCES += \
    py_builtin_utils.qrc \
    src.qrc
