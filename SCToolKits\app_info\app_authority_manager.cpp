#include <QDebug>
#include "app_authority_manager.h"
#include "app_info.h"
AppAuthorityManager * AppAuthorityManager::instance = nullptr;

class AppAuthorityImpl
{
public:
    const QString rootAccout {"Admin"};
    const QString rootPassword {"sc123456"};
    const QString suAccout {"su"};
    QMap<QString,QString>customAuthority;
};

AppAuthorityManager::AppAuthorityManager():impl(new AppAuthorityImpl)
{

}
bool AppAuthorityManager::applyForGuiAuthority(const QString & acc,const QString &pwd)
{
    if(acc.isEmpty() || pwd.isEmpty())
    {
        return false;
    }
    auto iter = impl->customAuthority.find(acc);
    if(iter != impl->customAuthority.end())
    {
        qDebug()<< "repeat apply";
        return false;
    }
    impl->customAuthority[acc]=pwd;
    return true;
}
AuthorityLevel AppAuthorityManager::getAuthority(const QString & acc,const QString & password)
{
    if(acc==impl->rootAccout && password == impl->rootPassword)
    {
        return AuthorityLevel::ROOT_AUTHORITY_E;
    }
    else
    {
        QString seed = AppInfo::get()->getCoreVersion();
        int convert = seed.toUInt()*2+1;
        QString real(QString("Wb123456%1").arg(convert));
        if(acc ==impl->suAccout && password == real)
        {
            return AuthorityLevel::SUPER_USER_AUTHORITY_E;
        }

        auto iter = impl->customAuthority.find(acc);
        if(iter != impl->customAuthority.end())
        {
            if(iter.value() == password)
            {
                return AuthorityLevel::GUI_AUTHORIRTY_E;
            }
        }
    }

    return AuthorityLevel::MAX_UNKONW_AUTHORITY_E;
}
