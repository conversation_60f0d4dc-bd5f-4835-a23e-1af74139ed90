#ifndef APPAUTHORITYMANAGER_H
#define APPAUTHORITYMANAGER_H
#include<QString>
#include "app_authority_level.h"
class AppAuthorityImpl;
class AppAuthorityManager
{
public:
    static AppAuthorityManager* get()
    {
        if(instance==nullptr)
        {
            instance = new AppAuthorityManager();
        }
        return instance;
    }
    AuthorityLevel getAuthority(const QString & account,const QString & pass);
    bool applyForGuiAuthority(const QString & acc,const QString &pwd);//用于申请某些界面需要密码和账号才能使用的情况
private:
    AppAuthorityManager();
    static AppAuthorityManager * instance;
private:
    AppAuthorityImpl * impl {nullptr};

};

#endif // APPAUTHORITYMANAGER_H
