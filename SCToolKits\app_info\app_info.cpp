#include "app_info.h"


AppInfo AppInfo::instance;
/*  第一位：大版本号。当前是1.有大的变动可以更新此号。MAJOR_VERSION表示
 *  第二位变更是ToolFunctionType的值。CORE_VERSION表示
 *  第三位是对应第二的bug修复和简单重构。ITERATION_VERSION表示
 */
// eg:MAJOR_VERSION.CORE_VERSION.INTERAT_VERSION
#define MAJOR_VERSION "1"
#define CORE_VERSION "40"
#define ITERATION_VERSION "1.beta"

/*每次发版，这个内部版本，需要不变更。
 *每增加一个工具，左边第一个数字，表示当前工具的集合。即当前工具的个数
 *第二个数字，表示当前支持的功能合计。即ToolFunctionType的最大值
 *从左第三个数字，是功能特性，当前的功能，具体的ToolFunctionType的值。
 *从左最后一位，表示bug修复和简单重构。
 * (考虑自动生成)
 */
#define APP_INNER_VERSION "22.49.65.0"

AppInfo::AppInfo()
{

}
AppInfo::~AppInfo()
{

}
QString AppInfo::getInnerVersion()
{
    return APP_INNER_VERSION;
}
QString AppInfo::getCoreVersion()
{
    return CORE_VERSION;
}
QString AppInfo::getAppVersion()
{
    return QString("%1.%2.%3").arg(MAJOR_VERSION).arg(CORE_VERSION).arg(ITERATION_VERSION);
}
QString AppInfo::getAppBuildTime()
{
    QString buildTime;


    buildTime += __DATE__;

    buildTime += " ";

    buildTime += __TIME__;

    return buildTime;
}
