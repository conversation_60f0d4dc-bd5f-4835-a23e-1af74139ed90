#ifndef FUNCTIONS_DEF_H
#define FUNCTIONS_DEF_H

//一个功能对应一个特定的worker。
//一个功能也和一个唯一的机种对应。
//一个工具可能是几个功能的组合。
//因为要进数据，不允许从中间新增枚举数据，只能从后面添加
typedef enum
{
    AM62_CORE_BOARD_E = 0x1,//AM62核心板
    GD32_CORE_BOARD_E,//GD32核心板
    DC_LIGHT_BOARD_E,//直流一字灯板
    GD32_AC_LIGHT_BOARD_E,//GD32灯板
    GD32_AC_DELAY_BOARD_E,//GD32交流继电板
    POWER_UPGRADE_E,//电源升级
    VIN_FAST_CONFIGE_E,//VIN fast 配置
    VIN_FAST_NETWORK_CHECK_E,//VIN fast 通信检测
    WAN_YUE_JI_GUAN_CONFIG_E, //弯月极光配置
    STEPPING_MOTOR_BOARD_E, //步进电机板
    UNIFIED_FLASH_WRITE_E,  //统一烧录
    SCHNEIDER_CONFIG_E,//施耐德生产配置
    SCHNEIDER_FANCTORY_CONFIG_E,//施耐德出厂配置
    SCHNEIDER_FLASH_WRITE_E,   //施耐德烧录
    SCHNEIDER_NETWORK_CHECK_E,   //施耐德通信检测
    EUROPEAN_MENISCUS_NETWORK_CHECK_E, //欧标弯月非网安
    LIGHT_BOARD_FLASH_WRITE_E, //灯板烧录
    ENEL_NETWORK_CONFIG_E, //Enel网络配置
    ATLANTE_NETWORK_CONFIG_E, //Atlante网络配置
    US_WAN_YUE_CORE_BOARD_E,  //美标弯月核心板
    ENVIRONMENTAL_BOARD_E,     //通用环境检测板

    //
    SCHNEIDER_PRINTER_E,//施耐德打印二维码
    //
    MASS_ENERY_STORE_E,//大储调试工具
    HUIHONG_NETWORK_CHECK_E, //慧鸿通信检测
    MID_NETWORK_CHECK_E, //mid通信检测
    GB_ONE_KEY_CONFIGURE_E,//国标一键配置。
    HUIHONG_USA_NETWORK_CHECK_E, //慧鸿美标非网安版
    EUROPEAN_SAFE_NETWORK_CHECK_E, //欧标弯月网安
    PIN_PRINTER_E,//pin code 打印
    PIN_CODE_GENNRATOR_E, //美标弯月PIN码生成
    LIQUID_COOL_BOARD_E, //液冷控制板
    AC_METER_CTRL_BOARD_E, //交流电表控制板
    SECC_BOARD_E,          //SECC板
    BMW_CONFIG_E,          //BMW配置
    GB_PDU_E,      //国标PDU

    ISO15118_ENABLE_E,//ISO15118使能功能

    METER_LOSS_SET_E,//设置DC电表损耗功能。
    METERING_BOARD_VERIFY_E,  //计量板校表
    AC_METER_BOARD_VERIFY_E,  //交流电表校表

    PCS_M215_AGEING_E,//pcs m215老化

    //
    WEB_PASSWORD_MODIFY_E,//WEB 密码修改

    COMMON_IC_WRITER_E,//IC卡写卡

    UPS_CHECK_E,//UPS 检测
    SCHNEIDER_POWER_BOARD_E, //施耐德功率板

    DC_CONTROL_BOTTOM_BOARD_E,  //直流4.0控制下板
    SCHNEIDER_CTRL_BOARD_E, //施耐德控制板

    COMMON_NU_WRITER_E,//NU wirter for a35;用于直接调用供应商的程序
    //
    INTERNAL_DC_LOG_ANALYSE_E,//国际日志分析
    DC_TOP_MAIN_BOARD_E, //直流控制上板

    INTERNAL_DC_CONFIGE_E,//国际直流一键配置
    BLUETOOTH_BOARD_E,  //蓝牙接口板
    INDIAN_MENISCUS_NETWORK_CHECK_E, //印度弯月通讯测试
    SCHNEIDER_NEW_RESI_CTRL_BOARD_E,//施耐德NewRise控制板
    SCHNEIDER_NEW_RESI_POWER_BOARD_E,//施耐德NewRise功率板
    UPGRADE_EVCC_PLC_FIRMWARE_E,     //升级evcc_plc固件
    APN_SWITCH_OVER_E,//APN切换
    DPAU_CONTROL_BOARD_E,//DPAU控制板
    INSULATION_BOARD_E,//绝缘板测试
    DC_NETWORK_CHECK_E,//直流通讯测试
    ECC_MULTI_FUNCTIONAL_E, //ECC多能工具
    BWM_PRINTER_E,//宝马二维码打印
    PDU_CONTROL_BOARD_E,                //PDU控制板
    SCHNEIDER_BENZ_CONFIG_E, //施耐德奔驰
    EVCC_CHARGE_FCT_E,//evcc 充电工装
    UNIFY_COMMUNICATION_CHECK_E,//通用的通信检测。
    IONCHI_CCU_BOARD_E,//逸安启CCU板检测
    XGB_CCU_BOARD_E,//新国标CCU板检测
    TU_XING_BORAD_CHECK_E, //土星工装板检测
    TEST_TOOL_BARCODE_CONFIG_E,//测试工装条码配置
    VIN_FAST_DC_CONFIG_E,  //VINFAST直流配置
    RKN_SECC_CHARGE_E,//瑞凯若SECC充电
    DC_PRE_CHARGE_BOARD_E,//直流预充板
    A35_NU_WRITER_SECURITY_E,//A35 nuvoton 安全烧录--->区别通用的NU_WRITE_E
    BESS_SECURE_CORRESPONDENCE_E, //柜控通信加密
    MCC_BOARD_CHECK_E, // MCC工装测试

    UNKOWN_TOOL_FUNCTION_E

}ToolFunctionType;

//和上面的ToolFunctionType 按顺序对应
//出了显示是中文，代码里使用的统一使用英文来比较和处理。

#define AM62_CORE_BOARD_FUN "am62核心板"
#define GD32_CORE_BOARD_FUN "gd32核心板"
#define DC_Light_BOARD_FUN "直流一字灯板"
#define GD32_Light_BOARD_FUN "gd32交流灯板"
#define GD32_AC_DELAY_BOARD_FUN "gd32交流继电板"
#define POWER_UPGRADE_FUN "电源升级"
#define VIN_FAST_CONFIG_FUN "vinFast配置"
#define VIN_FAST_NETWORK_CHECK_FUN "vinFast通信检测"
#define WAN_YUE_JI_GUANG_CONFIG_FUN "弯月极光配置"
#define SCHNEIDER_CONFIG_FUN "施耐德生产配置"
#define SCHNEIDER_FANCTORY_CONFIG_FUN "施耐德出厂配置"
#define STEPPING_MOTOR_BOARD_FUN "步进电机板"
#define MASS_ENERY_STORE_DEBUG_FUN "大储系统调试"
#define UNIFIED_FLASH_WRITE_FUN "统一烧录"
#define SCHNEIDER_FLASH_WRITE_FUN "施耐德烧录"
#define SCHNEIDER_NETWORK_CHECK_FUN "施耐德通信检测"
#define EUROPEAN_MENISCUS_NETWORK_CHECK_FUN "欧标弯月通信检测"
#define LIGHT_BOARD_FLASH_WRITE_FUN "灯板烧录"
#define ENEL_NETWORK_CONFIG_FUN "Enel配置"
#define ATLANTE_NETWORK_CONFIG_FUN "Atlante配置"
#define US_WAN_YUE_CORE_BOARD_FUN "美标弯月核心板"
#define SCHNEIDER_PRINTER_FUN "施耐德Pro打印"
#define HUIHONG_NETWORK_CHECK_FUN "慧鸿通信检测"
#define MID_NETWORK_CHECK_FUN "mid通信检测"
#define GB_ONE_KEY_CONFIGURE_FUN "国标一键配置"
#define HUIHONG_USA_NETWORK_CHECK_FUN "慧鸿美标非网安"
#define EUROPEAN_SAFE_NETWORK_CHECK_FUN "欧标弯月网安版"
#define PIN_CODE_PRINT_FUN "pin码打印"
#define PIN_CODE_GENNRATOR_FUN "pin码生成"
#define ENVIRONMENTAL_BOARD_FUN "通用环境检测板"
#define LIQUID_COOL_BOARD_FUN "液冷控制板"
#define AC_METER_CTRL_BOARD_FUN "交流电表控制板"
#define SECC_BOARD_FUN "SECC板"
#define BMW_CONFIG_FUN "BMW配置"
#define GB_PDU_FUN "国标PDU检测"
#define ISO_15118_FUN "ISO15118配置"
#define METER_LOSS_SET_FUN "电表损耗配置"
#define METERING_BOARD_VERIFY_FUN  "计量板校表"
#define AC_METER_BOARD_VERIFY_FUN  "交流电表校表"
#define PCS_M215_AGEING_FUN  "pcsM215老化"
#define WEB_PASSWORD_MODIFY_FUN "web密码修改"
#define COMMON_IC_WRITER_FUN "通用IC卡写卡"
#define UPS_CHECK_FUN "UPS检测"
#define SCHNEIDER_POWER_BOARD_FUN "施耐德功率板"
#define DC_CONTROL_BOTTOM_BOARD_FUN "直流4.0控制下板"
#define SCHNEIDER_CTRL_BOARD_FUN "施耐德控制板"
#define NU_WRITE_FUN "nuvoton烧录"
#define INTER_DC_LOG_ANALYSE_FUN "国际日志"
#define DC_TOP_MAIN_BOARD_FUN "直流上控制板"
#define INTERNAL_DC_CONFIGE_FUN "国际直流配置"
#define BLUETOOTH_BOARD_FUN "蓝牙接口板检测"
#define INDIAN_MENISCUS_NETWORK_CHECK_FUN "印度弯月通讯测试"
#define SCHNEIDER_NEW_RESI_CTRL_BOARD_FUN "施耐德NewResi控制板"
#define SCHNEIDER_NEW_RESI_POWER_BOARD_FUN "施耐德NewResi功率板"
#define UPGRADE_EVCC_PLC_FIRMWARE_FUN "升级evcc_plc固件"
#define APN_SWITCH_OVER_FUN "APN切换"
#define DPAU_CONTROL_BOARD_FUN "DPAU控制板"
#define INSULATION_BOARD_FUN "绝缘板"
#define ECC_MULTI_FUNCTIONAL_FUN "ECC多能工具"
#define BWM_PRINTER_FUN "宝马打印"
#define PDU_CONTROL_BOARD_FUN "PDU控制板"
#define SCHNEIDER_BENZ_CONFIG_FUN "施耐德奔驰配置"
#define EVCC_CHARGE_FCT_FUN "evcc充电FCT"
#define DC_NETWORK_CHECK_FUN "直流通讯测试"
#define IONCHI_CCU_BOARD_FUN "逸安启ccu板检测"
#define XGB_CCU_BOARD_FUN "新国标ccu板检测"
#define UNIFY_COMMUNICATION_CHECK_FUN "通用通信检测"
#define TU_XING_BORAD_CHECK_FUN "土星工装板检测"
#define TEST_TOOL_BARCODE_CONFIG_FUN "测试工装条码配置"
#define VIN_FAST_DC_CONFIG_FUN "VinFast直流配置"
#define RKN_SECC_CHARGE_CHECK_FUN "瑞凯诺充电"
#define DC_PRE_CHARGE_BOARD_FUN "直流预充板"
#define A35_NU_WRITER_SECURITY_FUN "A35安全烧录"
#define BESS_SECURE_CORRESPONDENCE_FUN "通信加密"
#define MCC_BOARD_CHECK_FUN "MCC测试"
#endif // FUNCTIONS_DEF_H
