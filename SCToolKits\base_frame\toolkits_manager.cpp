#include <QDebug>
#include "toolkits_manager.h"
#include "link_manager.h"

class FunctionsMapper
{
public:
    static void getAllFuns(QStringList &);
    static ToolFunctionType getFunType(const QString & );
    static bool getFunName(ToolFunctionType,QString &);
private:
    static QMap<ToolFunctionType,QString>funsTypes;
};
QMap<ToolFunctionType,QString>FunctionsMapper::funsTypes =
{
    {AM62_CORE_BOARD_E,          AM62_CORE_BOARD_FUN},
    {GD32_CORE_BOARD_E,          GD32_CORE_BOARD_FUN},
    {DC_LIGHT_BOARD_E,           DC_Light_BOARD_FUN},
    {GD32_AC_LIGHT_BOARD_E,      GD32_Light_BOARD_FUN},
    {GD32_AC_DELAY_BOARD_E,      GD32_AC_DELAY_BOARD_FUN},
    {POWER_UPGRADE_E,            POWER_UPGRADE_FUN},
    {VIN_FAST_CONFIGE_E,         VIN_FAST_CONFIG_FUN},
    {VIN_FAST_NETWORK_CHECK_E,   VIN_FAST_NETWORK_CHECK_FUN},
    {WAN_YUE_JI_GUAN_CONFIG_E,   WAN_YUE_JI_GUANG_CONFIG_FUN},
    {US_WAN_YUE_CORE_BOARD_E,    US_WAN_YUE_CORE_BOARD_FUN},
    {STEPPING_MOTOR_BOARD_E,     STEPPING_MOTOR_BOARD_FUN},
    {SCHNEIDER_CONFIG_E,         SCHNEIDER_CONFIG_FUN},
    {SCHNEIDER_FANCTORY_CONFIG_E,SCHNEIDER_FANCTORY_CONFIG_FUN},
    {MASS_ENERY_STORE_E,         MASS_ENERY_STORE_DEBUG_FUN},
    {UNIFIED_FLASH_WRITE_E,      UNIFIED_FLASH_WRITE_FUN},
    {SCHNEIDER_FLASH_WRITE_E,    SCHNEIDER_FLASH_WRITE_FUN},
    {SCHNEIDER_NETWORK_CHECK_E,  SCHNEIDER_NETWORK_CHECK_FUN},
    {LIGHT_BOARD_FLASH_WRITE_E,  LIGHT_BOARD_FLASH_WRITE_FUN},
    {ENEL_NETWORK_CONFIG_E,      ENEL_NETWORK_CONFIG_FUN},
    {ATLANTE_NETWORK_CONFIG_E,   ATLANTE_NETWORK_CONFIG_FUN},
    {EUROPEAN_MENISCUS_NETWORK_CHECK_E,EUROPEAN_MENISCUS_NETWORK_CHECK_FUN},
    {HUIHONG_NETWORK_CHECK_E,    HUIHONG_NETWORK_CHECK_FUN},
    {EUROPEAN_SAFE_NETWORK_CHECK_E,EUROPEAN_SAFE_NETWORK_CHECK_FUN},
    {HUIHONG_USA_NETWORK_CHECK_E,HUIHONG_USA_NETWORK_CHECK_FUN},
    {MID_NETWORK_CHECK_E,        MID_NETWORK_CHECK_FUN},
    {SCHNEIDER_PRINTER_E,        SCHNEIDER_PRINTER_FUN},
    {GB_ONE_KEY_CONFIGURE_E,     GB_ONE_KEY_CONFIGURE_FUN},
    {PIN_PRINTER_E,              PIN_CODE_PRINT_FUN},
    {PIN_CODE_GENNRATOR_E,       PIN_CODE_GENNRATOR_FUN},
    {ENVIRONMENTAL_BOARD_E,      ENVIRONMENTAL_BOARD_FUN},
    {LIQUID_COOL_BOARD_E,        LIQUID_COOL_BOARD_FUN},
    {AC_METER_CTRL_BOARD_E,      AC_METER_CTRL_BOARD_FUN},
    {SECC_BOARD_E,               SECC_BOARD_FUN},
    {BMW_CONFIG_E,               BMW_CONFIG_FUN},
    {GB_PDU_E,                   GB_PDU_FUN},
    {ISO15118_ENABLE_E,          ISO_15118_FUN},
    {METER_LOSS_SET_E,           METER_LOSS_SET_FUN},
    {METERING_BOARD_VERIFY_E,    METERING_BOARD_VERIFY_FUN},
    {AC_METER_BOARD_VERIFY_E,    AC_METER_BOARD_VERIFY_FUN},
    {PCS_M215_AGEING_E,          PCS_M215_AGEING_FUN},
    {WEB_PASSWORD_MODIFY_E,      WEB_PASSWORD_MODIFY_FUN},
    {COMMON_IC_WRITER_E,         COMMON_IC_WRITER_FUN},
    {UPS_CHECK_E,                UPS_CHECK_FUN},
    {SCHNEIDER_POWER_BOARD_E,    SCHNEIDER_POWER_BOARD_FUN},
    {DC_CONTROL_BOTTOM_BOARD_E,  DC_CONTROL_BOTTOM_BOARD_FUN},
    {SCHNEIDER_CTRL_BOARD_E,     SCHNEIDER_CTRL_BOARD_FUN},
    {COMMON_NU_WRITER_E,          NU_WRITE_FUN},
    {INTERNAL_DC_LOG_ANALYSE_E,  INTER_DC_LOG_ANALYSE_FUN},
    {DC_TOP_MAIN_BOARD_E,        DC_TOP_MAIN_BOARD_FUN},
    {INTERNAL_DC_CONFIGE_E,      INTERNAL_DC_CONFIGE_FUN},
    {BLUETOOTH_BOARD_E,          BLUETOOTH_BOARD_FUN},
    {INDIAN_MENISCUS_NETWORK_CHECK_E,INDIAN_MENISCUS_NETWORK_CHECK_FUN},
    {SCHNEIDER_NEW_RESI_CTRL_BOARD_E,SCHNEIDER_NEW_RESI_CTRL_BOARD_FUN},
    {SCHNEIDER_NEW_RESI_POWER_BOARD_E,SCHNEIDER_NEW_RESI_POWER_BOARD_FUN},
    {UPGRADE_EVCC_PLC_FIRMWARE_E,UPGRADE_EVCC_PLC_FIRMWARE_FUN},
    {APN_SWITCH_OVER_E,          APN_SWITCH_OVER_FUN},
    {DPAU_CONTROL_BOARD_E,       DPAU_CONTROL_BOARD_FUN},
    {INSULATION_BOARD_E,         INSULATION_BOARD_FUN},
    {ECC_MULTI_FUNCTIONAL_E,     ECC_MULTI_FUNCTIONAL_FUN},
    {BWM_PRINTER_E,              BWM_PRINTER_FUN},
    {PDU_CONTROL_BOARD_E,        PDU_CONTROL_BOARD_FUN},
    {SCHNEIDER_BENZ_CONFIG_E,    SCHNEIDER_BENZ_CONFIG_FUN},
    {EVCC_CHARGE_FCT_E,          EVCC_CHARGE_FCT_FUN},
    {DC_NETWORK_CHECK_E,         DC_NETWORK_CHECK_FUN},
    {IONCHI_CCU_BOARD_E,         IONCHI_CCU_BOARD_FUN},
    {XGB_CCU_BOARD_E,            XGB_CCU_BOARD_FUN},
    {UNIFY_COMMUNICATION_CHECK_E,         UNIFY_COMMUNICATION_CHECK_FUN},
    {TU_XING_BORAD_CHECK_E,      TU_XING_BORAD_CHECK_FUN},
    {TEST_TOOL_BARCODE_CONFIG_E,   TEST_TOOL_BARCODE_CONFIG_FUN},
    {VIN_FAST_DC_CONFIG_E,       VIN_FAST_DC_CONFIG_FUN},
    {RKN_SECC_CHARGE_E,       RKN_SECC_CHARGE_CHECK_FUN},
    {DC_PRE_CHARGE_BOARD_E,   DC_PRE_CHARGE_BOARD_FUN},
    {A35_NU_WRITER_SECURITY_E,   A35_NU_WRITER_SECURITY_FUN},
    {BESS_SECURE_CORRESPONDENCE_E,  BESS_SECURE_CORRESPONDENCE_FUN},
    {MCC_BOARD_CHECK_E,             MCC_BOARD_CHECK_FUN}
};

ToolFunctionType FunctionsMapper::getFunType(const QString & name)
{
    for(auto iter = funsTypes.begin(); iter != funsTypes.end();iter++)
    {
        if(iter.value() == name)
        {
            return iter.key();
        }
    }
    return UNKOWN_TOOL_FUNCTION_E;
}
bool FunctionsMapper::getFunName(ToolFunctionType type,QString &funName)
{
    auto iter = funsTypes.find(type);
    if(iter != funsTypes.end())
    {
        funName = iter.value();
        return true;
    }
    return false;
}
void FunctionsMapper::getAllFuns(QStringList & list)
{

}
class LinkDeviceAttr
{
public:
    LinkDeviceType linkType;
    int sshAuthModel;
    int serialBaund=9600;//TODO,使用继承的方式。
};

class ToolKitsManagerImpl
{
public:
    QMap<QString,LinkDeviceType> linkDeviceInfo;
    QMap<ToolFunctionType,LinkDeviceAttr>linkDeviceCtx;
};

ToolKitsManager ToolKitsManager::instance;
ToolKitsManager::ToolKitsManager():impl(new ToolKitsManagerImpl())
{
    defaultAvalibleTools[RKN_CHARGE_TOOL] = QString("瑞凯诺充电工具");
    defaultAvalibleTools[METER_VERIFY_TOOL] = QString("电表工具");
    defaultAvalibleTools[SECC_MAC_CONFIGURE_TOOL] = QString("seccMac配置工具");
    defaultAvalibleTools[INFRARED_MODEULE_TOOL] = QString("红外模块测试工具");
    defaultAvalibleTools[POWER_UPGRADE_TOOL] = QString("电源升级工具");
    defaultAvalibleTools[POWER_SIMULATOR_TOOL] = QString("电源模拟工具");
    defaultAvalibleTools[LIGHT_COLOR_CHECK_TOOL] = QString("灯效验证工具");
    defaultAvalibleTools[POWER_COMM_TOOL] = QString("电源模块通信工具");
    defaultAvalibleTools[SECC_LOG_ANALYZER_TOOL] = QString("secc日志分析工具");
    defaultAvalibleTools[VOLTAGE_CHECK_TOOL] = QString("电压验证工具");
    defaultAvalibleTools[SINGLE_BOARD_CHECK_TOOL] = QString("单板检测工具");
    defaultAvalibleTools[NETWORK_CONFIG_TOOL] = QString("网络配置工具");
    defaultAvalibleTools[CARD_WRITE_SYSTEM_TOOL] = QString("万帮卡片写入工具");
    defaultAvalibleTools[NETWORK_CHECK_TOOL] = QString("通信检测工具");
    defaultAvalibleTools[ONE_CLICK_CONFIG_TOOL] = QString("一键配置工具");
    defaultAvalibleTools[ONE_CLICK_BURN_TOOL] = QString("一键烧录工具");
    defaultAvalibleTools[MASS_ENERGY_STORE_TOOL] = QString("大储调试工具");
    defaultAvalibleTools[PRINTER_TOOL] = QString("通用打印工具");
    defaultAvalibleTools[RANDOM_GENNRATOR_TOOL] = QString("随机数生成工具");
    defaultAvalibleTools[LOG_ANALYZER_TOOL] = QString("日志分析工具");
    defaultAvalibleTools[WEB_CONFIG_TOOL] = QString("Web配置工具");
    defaultAvalibleTools[GB_PDU_CHECK_TOOL] = QString("国标PDU检测工具");
    defaultAvalibleTools[AGEING_CHECK_TOOL] = QString("老化工具");
    defaultAvalibleTools[BMW_CONFIG_TOOL] = QString("宝马配置工具");
    defaultAvalibleTools[MULTI_ACTIONS_TOOL] = QString("多操作工具");
    defaultAvalibleTools[ECC_MULTI_FUNCTIONAL_TOOL] = QString("ECC多能工具");
    defaultAvalibleTools[EVCC_INTEGRATION_TOOL] = QString("EVCC集成工具");
    defaultAvalibleTools[BESS_CABINET_TOOL] = QString("柜控工具");
    //真正支持的工具
    supportTools = defaultAvalibleTools;

    bindToolWithFuns(defaultAvalibleTools);

    defaultTool = supportTools.firstKey();
    currentEquipmentCode = "DGRKRCD01";

    //todo:如何组织数据和实现。
    //从数据库读取。
    EquipmentBindToolCtx toolCtx;
    toolCtx.toolName = SINGLE_BOARD_CHECK_TOOL;
    toolCtx.user = FCT_Line_Tester;
    toolCtx.funType = AM62_CORE_BOARD_E;
    equipmentContexts["ADQCPN1005"]=toolCtx;

    toolCtx.funType = GD32_CORE_BOARD_E;
    equipmentContexts["ADQCPN1219"] = toolCtx;

    toolCtx.funType = GD32_AC_DELAY_BOARD_E;
    equipmentContexts["ADQCPN1274"] = toolCtx;

//    toolCtx.toolName = LIGHT_COLOR_CHECK_TOOL;
//    toolCtx.user = FCT_Line_Tester;
//    toolCtx.funType = GD32_AC_LIGHT_BOARD_E;
//    equipmentContexts["ADQSGB0222"] = toolCtx;

    //没有的机种号的，使用DG开头，加工具中文名称的缩写+两位数字，从01开始。例如电源升级工具
    //DGDYSJ01
    toolCtx.toolName = POWER_UPGRADE_TOOL;
    toolCtx.funType = POWER_UPGRADE_E;
    toolCtx.user = Tester;
    equipmentContexts["DGDYSJ01"] = toolCtx;

    toolCtx.toolName = PRINTER_TOOL;
    toolCtx.funType = SCHNEIDER_PRINTER_E;
    toolCtx.user = Tester;
    equipmentContexts["DGCP01"] = toolCtx;

    toolCtx.toolName = RANDOM_GENNRATOR_TOOL;
    toolCtx.funType = PIN_CODE_GENNRATOR_E;
    toolCtx.user = AC_Line_Tester;
    equipmentContexts["DGPIN01"] = toolCtx;

    toolCtx.toolName = GB_PDU_CHECK_TOOL;
    toolCtx.funType = GB_PDU_E;
    toolCtx.user = DC_Line_Tester;
    equipmentContexts["GBPDU01"] = toolCtx;

    toolCtx.toolName = BMW_CONFIG_TOOL;
    toolCtx.funType = BMW_CONFIG_E;
    toolCtx.user = DC_Line_Tester;
    equipmentContexts["BMPZ01"] = toolCtx;

    //ToolKitsManagerImpl
    LinkDeviceAttr linkAttr;
    linkAttr.linkType = SSH_LINKE;
    linkAttr.sshAuthModel = 0;
    impl->linkDeviceCtx[EUROPEAN_MENISCUS_NETWORK_CHECK_E] = linkAttr;
    impl->linkDeviceCtx[SCHNEIDER_NETWORK_CHECK_E] = linkAttr;
    impl->linkDeviceCtx[HUIHONG_USA_NETWORK_CHECK_E] = linkAttr;
    impl->linkDeviceCtx[VIN_FAST_NETWORK_CHECK_E] = linkAttr;
    impl->linkDeviceCtx[ATLANTE_NETWORK_CONFIG_E] = linkAttr;
    impl->linkDeviceCtx[SCHNEIDER_BENZ_CONFIG_E] = linkAttr;
    linkAttr.sshAuthModel = 1;
    impl->linkDeviceCtx[EUROPEAN_SAFE_NETWORK_CHECK_E] = linkAttr;
    impl->linkDeviceCtx[HUIHONG_NETWORK_CHECK_E] = linkAttr;
    impl->linkDeviceCtx[MID_NETWORK_CHECK_E] = linkAttr;
    impl->linkDeviceCtx[PIN_CODE_GENNRATOR_E] = linkAttr;

    linkAttr.sshAuthModel = 0;
    impl->linkDeviceCtx[SCHNEIDER_CONFIG_E] = linkAttr;

    linkAttr.sshAuthModel = 0;
    impl->linkDeviceCtx[SCHNEIDER_FANCTORY_CONFIG_E] = linkAttr;

    linkAttr.sshAuthModel = 0;
    impl->linkDeviceCtx[GB_ONE_KEY_CONFIGURE_E] = linkAttr;

    linkAttr.sshAuthModel = 0;
    impl->linkDeviceCtx[INTERNAL_DC_CONFIGE_E] = linkAttr;

    linkAttr.sshAuthModel = 0;
    impl->linkDeviceCtx[BESS_SECURE_CORRESPONDENCE_E] = linkAttr;

    //todo:使用函数封装。
    linkAttr.linkType =SERIAL_PORT_LINK;
    linkAttr.sshAuthModel = -1;
    linkAttr.serialBaund = 115200;
    impl->linkDeviceCtx[METER_LOSS_SET_E]=linkAttr;
    impl->linkDeviceCtx[COMMON_IC_WRITER_E]=linkAttr; //设置写卡默认波特率为115200
    impl->linkDeviceCtx[A35_NU_WRITER_SECURITY_E]=linkAttr; //设置写卡默认波特率为115200

    linkAttr.linkType = ETHERNET_LINK;
    impl->linkDeviceCtx[MCC_BOARD_CHECK_E] = linkAttr;

    //不要在linkDeviceInfo中新增连接方式，在linkDeviceCtx中加。
    impl->linkDeviceInfo[ONE_CLICK_CONFIG_TOOL] = SSH_LINKE;
    impl->linkDeviceInfo[NETWORK_CHECK_TOOL] = SSH_LINKE;
    impl->linkDeviceInfo[RANDOM_GENNRATOR_TOOL] = SSH_LINKE;
    impl->linkDeviceInfo[MASS_ENERGY_STORE_TOOL] = ETHERNET_LINK;
    impl->linkDeviceInfo[SINGLE_BOARD_CHECK_TOOL] = SERIAL_PORT_LINK;
}
ToolKitsManager::~ToolKitsManager()
{


}
void ToolKitsManager::init()
{
    //从数据库中读取信息
    SQLiteManager::get()->select(equipmentContexts);

    return;
}
void ToolKitsManager::bindToolWithFuns(QMap<QString,QString>&tool)
{
    QMap<QString,addFuns>caseHookList;
    caseHookList[SINGLE_BOARD_CHECK_TOOL]  = &ToolKitsManager::addSingleBoardCheckToolFuns;
    caseHookList[ONE_CLICK_BURN_TOOL] = &ToolKitsManager::addBrunToolFuns;
    caseHookList[NETWORK_CONFIG_TOOL] = &ToolKitsManager::addNetworkConfigToolFuns;
    caseHookList[ONE_CLICK_CONFIG_TOOL] = &ToolKitsManager::addOneClickConfigToolFuns;
    caseHookList[NETWORK_CHECK_TOOL] = &ToolKitsManager::addNetworkCheckToolFuns;
    caseHookList[PRINTER_TOOL] = &ToolKitsManager::addPrinterToolFuns;;
    caseHookList[MASS_ENERGY_STORE_TOOL] = &ToolKitsManager::addMassStoreFuns;
    caseHookList[PRINTER_TOOL] = &ToolKitsManager::addPrinterToolFuns;
    caseHookList[METER_VERIFY_TOOL] = &ToolKitsManager::addMeterToolFuns;
    caseHookList[AGEING_CHECK_TOOL] = &ToolKitsManager::addAgeingToolFuns;;
    caseHookList[WEB_CONFIG_TOOL] = &ToolKitsManager::addWebToolFuns;
    caseHookList[CARD_WRITE_SYSTEM_TOOL] = &ToolKitsManager::addCarWriterFuns;
    caseHookList[VOLTAGE_CHECK_TOOL] = &ToolKitsManager::addVoltageCheckFuns;
    caseHookList[LOG_ANALYZER_TOOL] = &ToolKitsManager::addlogAnalyzerFuns;
    caseHookList[MULTI_ACTIONS_TOOL] = &ToolKitsManager::addMultiActionsFuns;
    caseHookList[ECC_MULTI_FUNCTIONAL_TOOL] = &ToolKitsManager::addEccMultiFunctionalFuns;
    caseHookList[LIGHT_COLOR_CHECK_TOOL] = &ToolKitsManager::addLightCheckFuns;
    caseHookList[EVCC_INTEGRATION_TOOL] = &ToolKitsManager::addEVCCFuns;
    caseHookList[RKN_CHARGE_TOOL] = &ToolKitsManager::addRKNFuns;
    caseHookList[BESS_CABINET_TOOL] = &ToolKitsManager::addBESSCabinetFuns;

    foreach(auto &var,tool.keys())
    {
        auto buildIter = caseHookList.find(var);
        if(buildIter != caseHookList.end())
        {
            (this->*buildIter.value())();
        }
    }
    return;
}
void ToolKitsManager::setEquipment(const QString & code)
{
    if(code.isEmpty())
    {
        return;
    }
    currentEquipmentCode = code;
    //更新currentTool

}
void ToolKitsManager::getEquipment(QString & code)
{
    code = currentEquipmentCode ;
    return;
}
int ToolKitsManager::getUserId(const QString & equipmentCode)
{
    auto iter = equipmentContexts.find(equipmentCode);
    if(iter != equipmentContexts.end())
    {
        return iter.value().user;
    }
    return -1;
}
void ToolKitsManager::setTools(const QString & toolList)
{
    if(toolList.isEmpty())
    {
        return;
    }
    QStringList tools = toolList.split(" ",Qt::SkipEmptyParts);
    QStringList rightConfigureTools;
    foreach(QString val,tools)
    {
        auto iter = defaultAvalibleTools.find(val);
        if(iter !=  defaultAvalibleTools.end())
        {
            rightConfigureTools.append(val);
        }
        else
        {
            qDebug() <<"exclude erro " << val;
        }
    }

    for(auto iter = supportTools.begin();iter != supportTools.end();)
    {
        if(!rightConfigureTools.contains(iter.key()))
        {
            iter = supportTools.erase(iter);
        }
        else
        {
            qDebug() << "config tools =" <<  iter.key();
            ++iter;
        }
    }

    defaultTool = supportTools.firstKey();
    //todo:是否更新添加机种的地方
    // bindToolWithFuns(supportTools);
    //TODO更新 equipmentCode;
    return;
}
QString  ToolKitsManager::getToolChineseName(const QString & toolName)
{
    auto iter = supportTools.find(toolName);
    if(iter != supportTools.end())
    {
        return iter.value();
    }
    return "";
}
void ToolKitsManager::getToolsChineseName(QStringList & toolList)
{
    foreach (auto var, supportTools)
    {
        toolList.append(var);
    }
}
void ToolKitsManager::getTools(QStringList & toolList)
{
    foreach(auto var,supportTools.keys())
    {
        toolList.append(var);
    }
}
QString  ToolKitsManager::getToolEnglishName(const QString & chineseName)
{
    for(auto iter = supportTools.begin(); iter != supportTools.end();iter++ )
    {
        if(iter.value() == chineseName)
        {
            return iter.key();
        }
    }
    return "";
}
QString ToolKitsManager::getDefaultTool()
{
    return defaultTool;
}
bool ToolKitsManager::isSupportTool(const QString &tool )
{
    if(tool.isEmpty())
    {
        return false;
    }
    auto iter = supportTools.find(tool);
    if(iter != supportTools.end())
    {
        return true;
    }
    else
    {
        return false;
    }
}
void ToolKitsManager::getToolKitsEquipments(QStringList & equipList)
{
    for(auto iter = equipmentContexts.begin();iter != equipmentContexts.end();iter++)
    {
            equipList<<iter.key();
    }
    return;
}
void ToolKitsManager::getFunName(ToolFunctionType type, QString& name)
{
    FunctionsMapper::getFunName(type,name);
    return;
}
ToolFunctionType ToolKitsManager::getFunType(const QString & equipmentCode)
{
    auto iter = equipmentContexts.find(equipmentCode);
    if(iter != equipmentContexts.end())
    {
        return static_cast<ToolFunctionType>(iter.value().funType);
    }
    return UNKOWN_TOOL_FUNCTION_E;
}

void ToolKitsManager::getFunsList(QStringList &list)
{
    FunctionsMapper::getAllFuns(list);
    return;
}
bool ToolKitsManager::getToolAndFuns(const QString &equipmentCode,QString & tool,QStringList & funs)
{
    auto iter = equipmentContexts.find(equipmentCode);
    if(iter != equipmentContexts.end())
    {
        tool = iter.value().toolName;
        QString fun;
        FunctionsMapper::getFunName(static_cast<ToolFunctionType>(iter.value().funType),fun);
        funs << fun;
        return true;
    }
    return false;
}
void  ToolKitsManager::getToolAndFuns(QMap<QString ,QStringList>&toolContainFun)
{
    toolContainFun = ToolBindFunsCtx;
    return;
}
ToolFunctionType ToolKitsManager::getCurrentToolFunction()
{
    auto iter = equipmentContexts.find(currentEquipmentCode);
    if(iter != equipmentContexts.end())
    {
        return static_cast<ToolFunctionType>(iter.value().funType);
    }
    return UNKOWN_TOOL_FUNCTION_E;
}
bool ToolKitsManager::updateToolFuns(const QString &equipmentCode ,const QString & tools,const QStringList &funs ,int catery)
{
    if(equipmentCode.isEmpty() || tools.isEmpty() || funs.isEmpty())
    {
        return false;
    }

    //TODO 重复判定
    if(isSupportTool(tools))
    {
         EquipmentBindToolCtx toolCtx;
         toolCtx.toolName = tools;
         toolCtx.user = catery;
         ToolFunctionType  funType = FunctionsMapper::getFunType(funs[0]);
         if(funType == UNKOWN_TOOL_FUNCTION_E)
         {
             qDebug() << "not found funtype";
             return false;
         }
         toolCtx.funType = funType;

         equipmentContexts[equipmentCode] = toolCtx;
         return true;
    }
    return false;
}

void ToolKitsManager::addSingleBoardCheckToolFuns()
{
   auto iter =  defaultAvalibleTools.find(SINGLE_BOARD_CHECK_TOOL);
   if(iter != defaultAvalibleTools.end())
   {
       QStringList funs;
       QString fun;
       FunctionsMapper::getFunName(AM62_CORE_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(GD32_CORE_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(GD32_AC_DELAY_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(STEPPING_MOTOR_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(US_WAN_YUE_CORE_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(ENVIRONMENTAL_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(LIQUID_COOL_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(AC_METER_CTRL_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(SECC_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(SCHNEIDER_POWER_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(DC_CONTROL_BOTTOM_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(SCHNEIDER_CTRL_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(DC_TOP_MAIN_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(BLUETOOTH_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(SCHNEIDER_NEW_RESI_CTRL_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(SCHNEIDER_NEW_RESI_POWER_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(DPAU_CONTROL_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(INSULATION_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(PDU_CONTROL_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(IONCHI_CCU_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(XGB_CCU_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(TU_XING_BORAD_CHECK_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(DC_PRE_CHARGE_BOARD_E,fun);
       funs << fun;
       FunctionsMapper::getFunName(MCC_BOARD_CHECK_E,fun);
       funs << fun;
       ToolBindFunsCtx[iter.value()] = funs;
   }
}

void ToolKitsManager::addBrunToolFuns()
{
    auto iter =  defaultAvalibleTools.find(ONE_CLICK_BURN_TOOL);
    if(iter != defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(UNIFIED_FLASH_WRITE_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(LIGHT_BOARD_FLASH_WRITE_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(SCHNEIDER_FLASH_WRITE_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(COMMON_NU_WRITER_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(UPGRADE_EVCC_PLC_FIRMWARE_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(A35_NU_WRITER_SECURITY_E,fun);
        funs << fun;

        ToolBindFunsCtx[iter.value()] = funs;
    }
}

void ToolKitsManager::addNetworkConfigToolFuns()
{
    auto iter =  defaultAvalibleTools.find(NETWORK_CONFIG_TOOL);
    if(iter != defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(ENEL_NETWORK_CONFIG_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(ATLANTE_NETWORK_CONFIG_E,fun);
        funs << fun;
        ToolBindFunsCtx[iter.value()] = funs;
    }
}

void ToolKitsManager::addOneClickConfigToolFuns()
{
    auto iter =  defaultAvalibleTools.find(ONE_CLICK_CONFIG_TOOL);
    if(iter != defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(SCHNEIDER_CONFIG_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(SCHNEIDER_FANCTORY_CONFIG_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(VIN_FAST_CONFIGE_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(WAN_YUE_JI_GUAN_CONFIG_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(GB_ONE_KEY_CONFIGURE_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(BMW_CONFIG_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(ISO15118_ENABLE_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(INTERNAL_DC_CONFIGE_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(SCHNEIDER_BENZ_CONFIG_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(TEST_TOOL_BARCODE_CONFIG_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(VIN_FAST_DC_CONFIG_E,fun);
        funs << fun;
        ToolBindFunsCtx[iter.value()] = funs;
    }
}

void ToolKitsManager::addNetworkCheckToolFuns()
{
    auto iter =  defaultAvalibleTools.find(NETWORK_CHECK_TOOL);
    if(iter != defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(SCHNEIDER_NETWORK_CHECK_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(EUROPEAN_MENISCUS_NETWORK_CHECK_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(INDIAN_MENISCUS_NETWORK_CHECK_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(UNIFY_COMMUNICATION_CHECK_E,fun);
        funs << fun;
        ToolBindFunsCtx[iter.value()] = funs;
    }
}
void ToolKitsManager::addPrinterToolFuns()
{
    auto iter = defaultAvalibleTools.find(PRINTER_TOOL);
    if(iter !=defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(SCHNEIDER_PRINTER_E,fun);
        funs << fun;

        FunctionsMapper::getFunName(PIN_PRINTER_E,fun);
        funs << fun;

        FunctionsMapper::getFunName(BWM_PRINTER_E,fun);
        funs << fun;

         ToolBindFunsCtx[iter.value()] = funs;
    }
}
void ToolKitsManager::addMassStoreFuns()
{
    auto iter = defaultAvalibleTools.find(MASS_ENERGY_STORE_TOOL);
    if(iter !=defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(MASS_ENERY_STORE_E,fun);
        funs << fun;
        ToolBindFunsCtx[iter.value()] = funs;
    }
}
void ToolKitsManager::addMeterToolFuns()
{
    auto iter = defaultAvalibleTools.find(METER_VERIFY_TOOL);
    if(iter !=defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(METER_LOSS_SET_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(METERING_BOARD_VERIFY_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(AC_METER_BOARD_VERIFY_E,fun);
        funs << fun;

        ToolBindFunsCtx[iter.value()] = funs;
    }
}
void ToolKitsManager::addAgeingToolFuns()
{
    auto iter = defaultAvalibleTools.find(AGEING_CHECK_TOOL);
    if(iter !=defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(PCS_M215_AGEING_E,fun);
        funs << fun;
        ToolBindFunsCtx[iter.value()] = funs;
    }
}
void ToolKitsManager::addWebToolFuns()
{
    auto iter = defaultAvalibleTools.find(WEB_CONFIG_TOOL);
    if(iter !=defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(WEB_PASSWORD_MODIFY_E,fun);
        funs << fun;
        ToolBindFunsCtx[iter.value()] = funs;
    }
}
void ToolKitsManager::addCarWriterFuns()
{
    auto iter = defaultAvalibleTools.find(CARD_WRITE_SYSTEM_TOOL);
    if(iter !=defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(COMMON_IC_WRITER_E,fun);
        funs << fun;
        ToolBindFunsCtx[iter.value()] = funs;
    }
}
void ToolKitsManager::addVoltageCheckFuns()
{
    auto iter = defaultAvalibleTools.find(VOLTAGE_CHECK_TOOL);
    if(iter !=defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(UPS_CHECK_E,fun);
        funs << fun;
        ToolBindFunsCtx[iter.value()] = funs;
    }
}
void ToolKitsManager::addlogAnalyzerFuns()
{
    auto iter = defaultAvalibleTools.find(LOG_ANALYZER_TOOL);
    if(iter !=defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(INTERNAL_DC_LOG_ANALYSE_E,fun);
        funs << fun;
        ToolBindFunsCtx[iter.value()] = funs;
    }
}

void ToolKitsManager::addMultiActionsFuns()
{
    auto iter = defaultAvalibleTools.find(MULTI_ACTIONS_TOOL);
    if(iter !=defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(APN_SWITCH_OVER_E,fun);
        funs << fun;
        ToolBindFunsCtx[iter.value()] = funs;
    }
}

void  ToolKitsManager::addLightCheckFuns()
{
    auto iter = defaultAvalibleTools.find(LIGHT_COLOR_CHECK_TOOL);
    if(iter !=defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(DC_LIGHT_BOARD_E,fun);
        funs << fun;
        FunctionsMapper::getFunName(GD32_AC_LIGHT_BOARD_E,fun);
        funs << fun;
        ToolBindFunsCtx[iter.value()] = funs;
    }
}

void ToolKitsManager::addEccMultiFunctionalFuns()
{
    auto iter = defaultAvalibleTools.find(ECC_MULTI_FUNCTIONAL_TOOL);
    if(iter !=defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(ECC_MULTI_FUNCTIONAL_E,fun);
        funs << fun;
        ToolBindFunsCtx[iter.value()] = funs;
    }
}
void ToolKitsManager::addEVCCFuns()
{
    auto iter = defaultAvalibleTools.find(EVCC_INTEGRATION_TOOL);
    if(iter !=defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(EVCC_CHARGE_FCT_E,fun);
        funs << fun;
        ToolBindFunsCtx[iter.value()] = funs;
    }
}
void ToolKitsManager::addRKNFuns()
{
    auto iter = defaultAvalibleTools.find(RKN_CHARGE_TOOL);
    if(iter !=defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(RKN_SECC_CHARGE_E,fun);
        funs << fun;
        ToolBindFunsCtx[iter.value()] = funs;
    }
}
void ToolKitsManager::addBESSCabinetFuns()
{
    auto iter = defaultAvalibleTools.find(BESS_CABINET_TOOL);
    if(iter !=defaultAvalibleTools.end())
    {
        QStringList funs;
        QString fun;
        FunctionsMapper::getFunName(BESS_SECURE_CORRESPONDENCE_E,fun);
        funs << fun;
        ToolBindFunsCtx[iter.value()] = funs;
    }
}

LinkDeviceType ToolKitsManager::getToolLinkDeviceType(const QString & tool)const
{
    auto iter = impl->linkDeviceInfo.find(tool);
    if(iter != impl->linkDeviceInfo.end())
    {
        return iter.value();
    }
    return MAX_LINK_DEVICE_TYPE;
}
LinkDeviceType ToolKitsManager::getToolLinkDeviceType(ToolFunctionType type)const
{
    auto iter = impl->linkDeviceCtx.find(type);
    if(iter != impl->linkDeviceCtx.end())
    {
        return iter.value().linkType;
    }
    return MAX_LINK_DEVICE_TYPE;
}
int  ToolKitsManager::getSShAuthModel(ToolFunctionType type)const
{
    auto iter = impl->linkDeviceCtx.find(type);
    if(iter != impl->linkDeviceCtx.end())
    {
        return iter.value().sshAuthModel;
    }
    return -1;
}
int ToolKitsManager::getSerialBaund(ToolFunctionType type)const
{
    auto iter = impl->linkDeviceCtx.find(type);
    if(iter != impl->linkDeviceCtx.end())
    {
        return iter.value().serialBaund;
    }
    return -1;
}
bool ToolKitsManager::isMeterVerfiyTool(const QString &tool )
{
    return tool == METER_VERIFY_TOOL;
}
bool ToolKitsManager::isRKNTool(const QString &tool )
{
    return tool == RKN_CHARGE_TOOL;
}
bool ToolKitsManager::isSeccMacConfigureTool(const QString &tool )
{
    return tool == SECC_MAC_CONFIGURE_TOOL;
}
bool ToolKitsManager::isInfraredModuleTool(const QString &tool )
{
    return tool == INFRARED_MODEULE_TOOL;
}
bool ToolKitsManager::isPowerUpgradeTool(const QString &tool )
{
    return tool == POWER_UPGRADE_TOOL;
}
bool ToolKitsManager::isPowerSimulatorTool(const QString &tool )
{
    return tool == POWER_SIMULATOR_TOOL;
}

bool ToolKitsManager::isLightColorCheckTool(const QString &tool )
{
    return tool == LIGHT_COLOR_CHECK_TOOL;
}

bool ToolKitsManager::isPowerCommTool(const QString &tool)
{
    return tool == POWER_COMM_TOOL;
}

bool ToolKitsManager::isSeccLogAnalyzerToOL(const QString &tool )
{
    return tool == SECC_LOG_ANALYZER_TOOL;
}

bool ToolKitsManager::isVoltageCheckTool(const QString &tool )
{
    return tool == VOLTAGE_CHECK_TOOL;
}

bool ToolKitsManager::isSingleBoardCheckTool(const QString &tool)
{
    return tool == SINGLE_BOARD_CHECK_TOOL;
}

bool ToolKitsManager::isNetworkConfigTool(const QString &tool)
{
    return tool == NETWORK_CONFIG_TOOL;
}
bool ToolKitsManager::isCardWriteSystemTool(const QString &tool)
{
    return  tool == CARD_WRITE_SYSTEM_TOOL;
}
bool ToolKitsManager::isNetworkCheckTool(const QString &tool)
{
    return  tool == NETWORK_CHECK_TOOL;
}
bool ToolKitsManager::isOneClickConfigTool(const QString &tool)
{
    return tool == ONE_CLICK_CONFIG_TOOL;
}
bool ToolKitsManager::isOneClickBrunTool(const QString &tool)
{
    return tool == ONE_CLICK_BURN_TOOL;
}
bool ToolKitsManager::isMassEnergyStoreTool(const QString &tool)
{
    return tool == MASS_ENERGY_STORE_TOOL;
}
bool ToolKitsManager::isPrintTool(const QString &tool)
{
    return tool == PRINTER_TOOL;
}

bool ToolKitsManager::isRandomGeneratorTool(const QString &tool)
{
    return tool == RANDOM_GENNRATOR_TOOL;
}
bool ToolKitsManager::isLogAnalyzeTool(const QString &tool)
{
    return tool == LOG_ANALYZER_TOOL;
}
bool ToolKitsManager::isWebConfigTool(const QString &tool)
{
    return tool == WEB_CONFIG_TOOL;
}
bool ToolKitsManager::isGBPduCheckTool(const QString &tool)
{
    return tool == GB_PDU_CHECK_TOOL;
}
bool ToolKitsManager::isAgeingCheckTool(const QString &tool)
{
    return tool == AGEING_CHECK_TOOL;
}
bool ToolKitsManager::isBMWConfigTool(const QString &tool)
{
    return tool == BMW_CONFIG_TOOL;
}

bool ToolKitsManager::isMultiActionsTool(const QString &tool)
{
    return tool == MULTI_ACTIONS_TOOL;
}

bool ToolKitsManager::isEccMultiFunctionalTool(const QString &tool)
{
    return tool == ECC_MULTI_FUNCTIONAL_TOOL;
}
bool ToolKitsManager::isEVCCFunctionalTool(const QString &tool)
{
    return tool == EVCC_INTEGRATION_TOOL;
}
bool ToolKitsManager::isBESSCabinetTool(const QString &tool)
{
    return tool == BESS_CABINET_TOOL;
}
