#include <QtEndian>
#include <QDateTime>
#include <QRegularExpression>
#include "ageing_worker.h"
#include "interface_data/interface_data.h"
#include "characters/character_format.h"

AgeingWorker::AgeingWorker():currentTask(LINK_DEVICE_TASK),
                            pcsData(nullptr),
                            queryTimer(nullptr),
                            ageingTimer(nullptr),
                            respondTimer(nullptr),
                            queryInfoTimer(nullptr),
                            powerData(5000),
                            ageingStage(1),
                            isPowerUpPhase(1),
                            ageingCtx(nullptr)
{
    connect(this,&AgeingWorker::triggerTaskSignal,this,&AgeingWorker::processBusiness,Qt::QueuedConnection);
    connect(LinkManager::get(),&LinkManager::ethernetLinkedSignal,this,&AgeingWorker::processEtherLinkSlot);
    //第一步连接PCS
    tasksList[LINK_DEVICE_TASK] = &AgeingWorker::linkPcsDevice;
    //第二步PCS开机
    tasksList[SET_PCS_OPEN_TASK] = &AgeingWorker::openPcs;
    tasksList[QUERY_PCS_STATUS_TASK] = &AgeingWorker::queryPcsStatus;
    //第三步老化PCS
    tasksList[AGEING_TASK] = &AgeingWorker::handleAgeingProcess;
    //最后一步老化结果处理
    tasksList[AGEING_END_TASK] = &AgeingWorker::handleAgeingEnd;

    pcsData = new PCSData();
    queryStatusOkCnt = 0;
}
AgeingWorker::~AgeingWorker()
{
    if(pcsData)
    {
        delete pcsData;
    }
    if(queryTimer)
    {
        delete queryTimer;
    }
    if(ageingTimer)
    {
        delete ageingTimer;
    }
    if(respondTimer)
    {
        delete respondTimer;
    }
    if(ageingCtx)
    {
        delete ageingCtx;
    }
}
void AgeingWorker::setAgeingTime(int msec)
{
    if(msec >= AGEING_TIME)
    {
        ageingTimes = msec/2;//单次的老化时间
        int cnt = msec/(60*60*1000);
        if(cnt != 0)
        {
            queryInterval = msec/cnt;
        }
        else
        {
            queryInterval = 10*60*1000;
        }
        qDebug()<< "set age time"<< ageingTimes;
        qDebug()<< "set query time"<< queryInterval;
    }
}
void AgeingWorker::setFullPowerRate(int rate)
{
    if(rate >0 && rate <=100)
    {
        maxPcsPower = maxPcsPower*rate/100;
        qDebug()<< "set max power"<< maxPcsPower;
    }
}
void AgeingWorker::initAgeingCtx()
{
    if(ageingCtx == nullptr)
    {
        ageingCtx = new AgeingContext;
    }
    ageingCtx->powerPhase = POWER_LOAD_PHASE_E;
    ageingCtx->isForPartPowerSet = true;
    ageingCtx->isPowerOk = true;
    ageingCtx->ageingStage =  FIRST_CHARGE_SECOND_DISCHARGE_E;
    ageingCtx->settedPower = INIT_POWER_VALUE;
    ageingCtx->pcsPowerOkCnt = 0;
    ageingTestResult = true;
}
void AgeingWorker::startWorker(const QString  name)
{
    Q_UNUSED(name);
    qDebug()<< "start worker";

    initAgeingCtx();
    emit triggerTaskSignal();
}
void AgeingWorker::updateTaskStep(TaskStep step,bool isIncreas,int stepGap)
{
    Q_UNUSED(stepGap);
    if(isIncreas==false)
    {
        qDebug()<< "task step "<<currentStep << "append step"<< step;
        currentStep =(TaskStep) ((int)currentStep |(int)step);

    }
    else
    {
        currentStep = step;
        qDebug()<< "task step "<<currentStep << "to step"<< step;
    }

}
void AgeingWorker::updateTask(TaskType type,bool isTriggTask)
{
    if(type < UNKOW_MAX_TASK)
    {
        currentTask = type;
    }
    else
    {
        return;
    }
    qDebug()<< "task update to "<<currentTask;
    if(isTriggTask)
    {
         emit triggerTaskSignal();
    }
}
void AgeingWorker::handleErro(const QString & re)
{
    qDebug()<< "error info:"<<re;
    ageingTestResult = false;
    errorResaon = re;
    queryStatusOkCnt = 0;
    if(queryTimer)
    {
        queryTimer->stop();
    }
    emit testResultSignal(-1,CharacterFormat::change(re,CharacterColor::RED_E));
    emit displayInfoSignal(CharacterFormat::change(re,CharacterColor::RED_E));
    //TODO:清理连接和释放资源。
    updateTask(AGEING_END_TASK);
    return;
}
void AgeingWorker::processBusiness()
{
    qDebug()<< "process task"<< currentTask;
    auto iter = tasksList.find(currentTask);
    if(iter != tasksList.end())
    {
        (this->*iter.value())();
    }
}
//连接PCS
void AgeingWorker::processEtherLinkSlot(bool ret,const QString & ip,int port)
{
    qDebug()<< "connect ip"<< ip << ret;
    if(ret)
    {
        EthernetClient  * client = LinkManager::get()->getEthernetClient(ip,port);
        if(client)
        {
            linkCnt++;
            if(devicesList.size() == linkCnt)
            {
                emit tipCtxSignal("连接完成",20);
                emit displayInfoSignal(CharacterFormat::add("PCS连接成功",CharacterColor::GREEN_E));
                linkCnt = 0;
                disconnect(LinkManager::get(),&LinkManager::ethernetLinkedSignal,this,&AgeingWorker::processEtherLinkSlot);
                updateTask(SET_PCS_OPEN_TASK);
            }
        }
        else
        {
            handleErro(tr("内部错误"));
        }
    }
    else
    {
        emit displayInfoSignal(CharacterFormat::add(QString(tr("无法连接ip:%1")).arg(ip),CharacterColor::RED_E));
        handleErro(QString(tr("无法连接ip:%1")).arg(ip));
    }
}
void AgeingWorker::linkPcsDevice()
{
    //获取网络连接信息：
    GuiAgeingConfigData data;
    bool ret = InterfaceData::get()->getData(data);
    if(ret==false)
    {
        //TODO:
        emit tipCtxSignal("没有IP数据",1);
        return;
    }
    qDebug()<< "iplist"<< data.ip;
    //TODO 校验IP的重复性
    emit tipCtxSignal("连接PCS中",1);
    emit displayInfoSignal(CharacterFormat::add("连接PCS……",CharacterColor::BLUE_E));
    int port=502 ;

    int unitId = 0;
    int pairPcsCnt = 0;
    AgeingDeviceInfo *preDeviceInfo;
    foreach(auto & ipVar,data.ip)
    {
        LinkManager::get()->connectHost(ipVar,port);
        qDebug()<<"start link"<<ipVar;

        EthernetClient  * client = LinkManager::get()->getEthernetClient(ipVar,port);
        if(client == nullptr)
        {
            return;
        }
        //todo:已经连接过的，如何处理？是不是重复添加导致内存泄漏了。

        DataSendAdpater * dataSendInstance =  new DataSendAdpater();
        dataInterPoll[client] = dataSendInstance;
        connect(dataSendInstance,&DataSendAdpater::sendSignal,client,&EthernetClient::sendBytesSlot);
        connect(client,&EthernetClient::dataComInSigal,this,&AgeingWorker::recvDataSlot);

        AgeingDeviceInfo *deviceInfo = new AgeingDeviceInfo();
        deviceInfo->client = client;
        deviceInfo->port = 502;
        deviceInfo->ip = ipVar;
        devicesList[client] = deviceInfo;

        ModbusTcpSendData sendCtx ;
        sendCtx.tranId =0;
        sendCtx.unitId = unitId++;
        modbusSendCtx[client]=sendCtx;

        //简单的关联。一组PCS，包括两个PCS.两者有功率分配的联系。
        pairPcsCnt++;
        if(pairPcsCnt == 2)
        {
            preDeviceInfo->pairClient = client;
            deviceInfo->pairClient = preDeviceInfo->client;
            pairPcsCnt = 0;
            devicesPairs[preDeviceInfo]=deviceInfo;
        }
        preDeviceInfo = deviceInfo;
    }

    if(queryTimer==nullptr)
    {
        //先创建，但是不启动。
        queryTimer = new QTimer();
        connect(queryTimer,&QTimer::timeout,this,&AgeingWorker::triggerTaskSignal);
    }
    qDebug()<<"link end";
    return;
}
//开机pcs
void AgeingWorker::openPcs()
{
    emit tipCtxSignal("开机中",20);
    emit displayInfoSignal(tr("开机中……"));
    for(auto iter = devicesList.begin();iter != devicesList.end();iter++)
    {
        QByteArray data;
        packBusinessData(iter.key(),30314,1,data);

        sendMsg(iter.key(),data);
    }
    updateTask(QUERY_PCS_STATUS_TASK,false);

    queryTimer->setInterval(OPEN_PCS_QUERY_TIEM);
    queryTimer->start();
}

void AgeingWorker::queryPcsStatus()
{
    qDebug()<< "query psc status";
    queryTimer->stop();
    emit tipCtxSignal("查询开机的状态",20);
    for(auto iter = devicesList.begin();iter != devicesList.end();iter++)
    {
        QByteArray data;
        packBusinessData(iter.key(),30374,1,data);

        sendMsg(iter.key(),data);
    }

    if(respondTimer==nullptr)
    {
        respondTimer = new QTimer();
        respondTimer->setInterval(5*1000);
        connect(respondTimer,&QTimer::timeout,this,&AgeingWorker::handleReplyTimeout);
    }
    respondTimer->start();

    return;
}
void AgeingWorker::handleRespondPcsStatus(EthernetClient * client,QByteArray & data)
{
    auto iter = devicesList.find(client);
    if(iter == devicesList.end())
    {
        return;
    }
    respondTimer->stop();//TODO 需要所有的回复完成后 stop
    int status = qFromBigEndian<quint8>(data.constData()+data.size()-1);
    qDebug()<< "pcs run status"<<status;
    //2代表并网模式
    if(status == 2)
    {
        if(iter.value()->runMode != status)
        {
            queryStatusOkCnt++;
        }

        iter.value()->runMode = status;
        iter.value()->queryCnt = 0;
        if(queryStatusOkCnt == devicesList.size())
        {
            queryStatusOkCnt = 0;
            queryTimer->stop();
            emit displayInfoSignal(CharacterFormat::add("并网成功",CharacterColor::GREEN_E));
            emit displayInfoSignal(tr("功率开始加载……"));
            updateTaskStep(LOAD_POWER_UP_STEP_E);
            updateTask(AGEING_TASK);
        }
    }
    else
    {
        if(iter.value()->runMode == 2)
        {
            queryStatusOkCnt--;//查询过程中，可能出现并网后，又不是并网的状态。
        }
        if(iter.value()->queryCnt > iter.value()->maxQueryCnt)
        {
            queryTimer->stop();
            iter.value()->queryCnt = 0;
            handleErro(tr("PCS 并网异常"));
            return;
        }
        iter.value()->queryCnt++;
        iter.value()->runMode = status;
        if(!queryTimer->isActive())
        {
            queryTimer->start();
        }
    }
}
void AgeingWorker::handleAgeingProcess()
{
    qDebug()<< "aging time step"<<currentStep;
    switch(currentStep)
    {
        case LOAD_POWER_UP_STEP_E:
        case UNLOAD_POWER_DOWN_STEP_E:
            setPcsPower();
            break;
        case QEURY_PCS_POWER_STEP_E:
            queryPcsPower();
            break;
        case AGING_ERROR_STEP_E:
            handlePowerError();
            break;
        default:
            break;
    }
}
//业务逻辑：
bool AgeingWorker::sendPcsPower(EthernetClient *client,int powerData)
{
    if(client)
    {
        QByteArray data;
        packBusinessData(client,30315,powerData,data,4);
        sendMsg(client,data);
        return true;
    }
    return false;
}
//用于选择是否第一个设备进行功率设置。
bool AgeingWorker::selectSetPowerStrategy()
{
    bool isSetFirstDevicePre = true;
    if(ageingCtx->ageingStage == FIRST_CHARGE_SECOND_DISCHARGE_E)
    {
        if(ageingCtx->powerPhase == POWER_LOAD_PHASE_E)
        {
            if(ageingCtx->isForPartPowerSet)
            {
                isSetFirstDevicePre = false;
            }
        }
        else
        {
            if( ageingCtx->isForPartPowerSet == false)
            {
                isSetFirstDevicePre = false;
            }
        }
    }
    else
    {
        if(ageingCtx->powerPhase == POWER_LOAD_PHASE_E)
        {
            if( ageingCtx->isForPartPowerSet == false)
            {
                isSetFirstDevicePre = false;
            }
        }
        else
        {
            if( ageingCtx->isForPartPowerSet == true)
            {
                isSetFirstDevicePre = false;
            }
        }
    }
    return isSetFirstDevicePre;
}
bool AgeingWorker::isSetChargeDevicePhase()
{
    bool chargeDevice = true;
    if(ageingCtx->ageingStage == FIRST_CHARGE_SECOND_DISCHARGE_E)
    {
        if(ageingCtx->powerPhase == POWER_LOAD_PHASE_E)
        {
            if(ageingCtx->isForPartPowerSet)
            {
                chargeDevice = false;
            }
        }
        else
        {
            if( ageingCtx->isForPartPowerSet == true)
            {
                chargeDevice = true;
            }
            else
            {
                chargeDevice = false;
            }
        }

    }
    else
    {
        if(ageingCtx->powerPhase == POWER_LOAD_PHASE_E)
        {
            if( ageingCtx->isForPartPowerSet == true)
            {
                chargeDevice = false;
            }
            else
            {
                chargeDevice = true;
            }
        }
        else
        {
            if( ageingCtx->isForPartPowerSet == true)
            {
                chargeDevice = true;
            }
            else

            {
                chargeDevice =false;
            }
        }
    }
    return chargeDevice;
}
int AgeingWorker::calculateNeedPowerValue()
{
    int needData = ageingCtx->settedPower;
    if(isSetChargeDevicePhase())
    {
        needData -= GAP_POWER_VALUE;
        if(needData <= 0)
        {
            //防止反转。
            return 0;
        }
        needData *=-1;
    }
    return needData;
}
void AgeingWorker::setPcsPower()
{
    if(ageingCtx->powerPhase == POWER_LOAD_PHASE_E)
    {
        emit displayInfoSignal(tr("负载功率加载中....."));
        emit tipCtxSignal("负载功率加载中",10);
    }
    else
    {
        emit displayInfoSignal(tr("负载功率卸载中....."));
        emit tipCtxSignal("负载功率卸载中",50);
    }

    //根据直流源是单向的场景：
    //1:上升阶段，先增大放电的设备。再增大充电设备。
    //2:下降阶段：先降低充电的设备的功率，再降低放电的功率。
    //充电功率是正，放电功率是负
    bool isSetFirstDevicePre = selectSetPowerStrategy();
    int needData = calculateNeedPowerValue();
    for(auto iter = devicesPairs.begin();iter != devicesPairs.end();iter++)
    {
        AgeingDeviceInfo * deviceInfoPtr=nullptr;
        if(isSetFirstDevicePre)
        {
            deviceInfoPtr = iter.key();
        }
        else
        {
            deviceInfoPtr = iter.value();
        }
        deviceInfoPtr->expectPower = needData;
        deviceInfoPtr->hasSetPower = true;
        sendPcsPower(deviceInfoPtr->client,needData);
        qDebug()<<QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")<<"ip:"<<deviceInfoPtr->client->getPeerIp()<<"set power"<< needData<<"yuan"<<ageingCtx->settedPower;
    }

    if(ageingCtx->isPowerOk)
    {
//        updateTask(QEURY_PCS_POWER_TASK,false);
        qDebug()<< "wait query start";
        updateTaskStep(QEURY_PCS_POWER_STEP_E);
        queryTimer->setInterval(4000);
        queryTimer->start();
    }
    else
    {

    }

    return;
}
bool AgeingWorker::queryPcsPower(EthernetClient * client)
{
    if(client)
    {
        QByteArray data;
        packBusinessData(client,30353,1,data);
        sendMsg(client,data);
        return true;
    }
    return false;
}

void AgeingWorker::queryPcsPower()
{
    if(ageingCtx->powerPhase == POWER_KEEP_PHASE_E)
    {
        for(auto iter = devicesList.begin();iter != devicesList.end();iter++)
        {
            queryPcsPower(iter.key());
        }
        return;
    }
    bool isSetFirstDevicePre = selectSetPowerStrategy();
    for(auto iter = devicesPairs.begin();iter != devicesPairs.end();iter++)
    {
        AgeingDeviceInfo * deviceInfoPtr = nullptr;
        if(isSetFirstDevicePre == false)
        {
            deviceInfoPtr = iter.value();
        }
        else
        {
           deviceInfoPtr = iter.key();
        }
        qDebug()<<QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz")<< "query power ip"<<deviceInfoPtr->client->getPeerIp();
        queryPcsPower(deviceInfoPtr->client);
    }
    return;
}
bool AgeingWorker::queryPcsTemperature(EthernetClient * client)
{
    if(client)
    {
        QByteArray data;
        packBusinessData(client,30454,1,data);
        sendMsg(client,data);
        qDebug()<< "send data is "<<data.toHex();
        return true;
    }
    return false;
}
void AgeingWorker::queryInfoSlot()
{
    currentTask=AGEING_TASK;
    if(AGING_TIME_STEP_E & currentStep)
    {
        updateTaskStep(QUERY_INFO_STEP_E,false);
        for(auto iter = devicesList.begin();iter != devicesList.end();iter++)
        {
            queryPcsTemperature(iter.key());
        }
    }
    return;
}

bool AgeingWorker::handlePowerOk()
{
    if(ageingCtx->powerPhase == POWER_LOAD_PHASE_E)
    {
        ageingCtx->pcsPowerOkCnt++;
        if(ageingCtx->pcsPowerOkCnt == devicesPairs.size())
        {
            ageingCtx->pcsPowerOkCnt = 0;
            if(ageingCtx->isForPartPowerSet)
            {
                ageingCtx->isForPartPowerSet = false;
                updateTaskStep(LOAD_POWER_UP_STEP_E);
                updateTask(AGEING_TASK);
                return true;
            }
            else
            {
                ageingCtx->isForPartPowerSet = true;
                //是否是最后的加载。
                if(ageingCtx->settedPower < maxPcsPower + MAX_OVERLOAD_VALUE)
                {
                    ageingCtx->settedPower += STEP_INCREASE_POWER;
                    if(ageingCtx->settedPower > maxPcsPower + MAX_OVERLOAD_VALUE)
                    {
                        ageingCtx->settedPower = maxPcsPower + MAX_OVERLOAD_VALUE;
                    }
                    updateTaskStep(LOAD_POWER_UP_STEP_E);
                    updateTask(AGEING_TASK);
                    return true;
                }
                else
                {
                    ageingCtx->powerPhase = POWER_KEEP_PHASE_E;
                    if(ageingTimer==nullptr)
                    {
                        ageingTimer = new QTimer();
                        ageingTimer->setInterval(ageingTimes);
                        connect(ageingTimer,&QTimer::timeout,this,&AgeingWorker::finishedAgeingPhase);
                    }
                    if(queryInfoTimer == nullptr)
                    {
                        queryInfoTimer = new QTimer();
                        queryInfoTimer->setInterval(queryInterval);
                        connect(queryInfoTimer,&QTimer::timeout,this,&AgeingWorker::queryInfoSlot);
                    }
                    updateTaskStep(AGING_TIME_STEP_E);
                    ageingTimer->start();
                    queryInfoTimer->start();
//                    queryTimer->start(60*1000);
                    if(ageingCtx->ageingStage == FIRST_CHARGE_SECOND_DISCHARGE_E)
                    {
                        emit displayInfoSignal(CharacterFormat::add("第一阶段 老化中",CharacterColor::GREEN_E));
                        emit tipCtxSignal("第一阶段 老化两小时中",50);
                    }
                    else
                    {
                        emit displayInfoSignal(CharacterFormat::add("第二阶段 老化中",CharacterColor::GREEN_E));
                        emit tipCtxSignal("第二阶段 老化两小时中",90);
                    }
                }
                return true;
            }
        }
    }
    else if(ageingCtx->powerPhase == POWER_UNLOAD_PHASE_E)
    {
        ageingCtx->pcsPowerOkCnt++;
        if(ageingCtx->pcsPowerOkCnt == devicesPairs.size())
        {
            ageingCtx->pcsPowerOkCnt = 0;
            if(ageingCtx->isForPartPowerSet)
            {
                ageingCtx->isForPartPowerSet = false;
                updateTaskStep(UNLOAD_POWER_DOWN_STEP_E);
                updateTask(AGEING_TASK);
                return true;
            }
            else
            {
                ageingCtx->isForPartPowerSet = true;
                if(ageingCtx->settedPower > 0)
                {
                    ageingCtx->settedPower -= STEP_INCREASE_POWER;
                    if(ageingCtx->settedPower <= STEP_INCREASE_POWER)
                    {
                        ageingCtx->settedPower = 0;
                    }
                    updateTaskStep(UNLOAD_POWER_DOWN_STEP_E);
                    updateTask(AGEING_TASK);
                }
                else
                {
                    if(ageingCtx->ageingStage == FIRST_DISCHARGE_SECOND_CHARGE_E)
                    {
                        updateTask(AGEING_END_TASK);
                    }
                    else
                    {
                        qDebug()<< "enter stage"<<FIRST_DISCHARGE_SECOND_CHARGE_E;
                        ageingCtx->powerPhase = POWER_LOAD_PHASE_E;
                        ageingCtx->isForPartPowerSet = true;
                        ageingCtx->isPowerOk = true;
                        ageingCtx->ageingStage =  FIRST_DISCHARGE_SECOND_CHARGE_E;
                        ageingCtx->settedPower = INIT_POWER_VALUE;
                        ageingCtx->pcsPowerOkCnt = 0;

                        updateTaskStep(LOAD_POWER_UP_STEP_E);
                        updateTask(AGEING_TASK);
                    }
                }
                return true;
            }
        }
    }
    else if(ageingCtx->powerPhase == POWER_KEEP_PHASE_E)
    {
        queryTimer->stop();
        queryTimer->start(20000);
    }

    return true;
}
//功率错误时，直接卸载功率。
bool AgeingWorker::handlePowerError()
{
//    ageingCtx->isPowerOk = false;
    ageingCtx->powerPhase = POWER_UNLOAD_PHASE_E;
    if(ageingCtx->settedPower > 0)
    {
        //出错时，降低功率到0;
        //2:下降阶段：先降低充电的设备的功率，再降低放电的功率。
       //充电功率是正，放电功率是负
        int needData = 0;
        bool isFirstDownPower = true;
        if(ageingCtx->ageingStage == FIRST_DISCHARGE_SECOND_CHARGE_E)
        {
            isFirstDownPower = false;
        }
        do
        {
            for(auto iter = devicesPairs.begin();iter != devicesPairs.end();iter++)
            {
                needData = ageingCtx->settedPower;
                //先降低充电，再降低放电
                if(isFirstDownPower)
                {
                    needData *= -1;
                    sendPcsPower(iter.key()->client,needData);
                    needData *= -1;
                    sendPcsPower(iter.value()->client,needData);
                }
                else
                {
                    needData *= -1;
                    sendPcsPower(iter.value()->client,needData);
                    needData *= -1;
                    sendPcsPower(iter.key()->client,needData);
                }
            }
            ageingCtx->settedPower -= STEP_INCREASE_POWER;
            if(ageingCtx->settedPower < STEP_INCREASE_POWER + MAX_OVERLOAD_VALUE && ageingCtx->settedPower >= 0)
            {
                ageingCtx->settedPower = 0;
            }
        }while(ageingCtx->settedPower >= 0);
    }
    handleErro(tr("功率设置异常"));


    return true;
}
void AgeingWorker::handleRespondPcsPower(EthernetClient * client,QByteArray & data)
{
    if(ageingCtx->isPowerOk == false)
    {
        //处于错误的情况下，就不再做任何做处理。
        return;
    }
    auto iter = devicesList.find(client);
    if(iter == devicesList.end())
    {
        return;
    }
    qDebug()<< "power reply ip "<<client->getPeerIp();
    quint8 firstHigeByte = qFromBigEndian<quint8>(data.constData()+data.size()-4);
    quint8 secondHigeByte = qFromBigEndian<quint8>(data.constData()+data.size()-3);

    quint8 firstLowByte = qFromBigEndian<quint8>(data.constData()+data.size()-2);
    quint8 secondLowByte = qFromBigEndian<quint8>(data.constData()+data.size()-1);
    qDebug()<< QString("%1").number(firstHigeByte,16);
    qDebug()<< QString("%1").number(secondHigeByte,16);
    qDebug()<< QString("%1").number(firstLowByte,16);
    qDebug()<< QString("%1").number(secondLowByte,16);
    int powerValue = (int)(firstLowByte<<24) +
                     (int)(secondLowByte<<16) +
                     (int)(firstHigeByte<<8) +
                     (int)secondHigeByte;

    int expectValue = iter.value()->expectPower;
    qDebug()<< "pcs power real value"<<powerValue <<" expect "<<expectValue;
    qDebug()<<"pcs expect range value "<< expectValue - MISTASK_VALUE << " to "<<expectValue + MISTASK_VALUE;
    //简单的处理：正负1500范围。
    if(powerValue <= (expectValue + MISTASK_VALUE)  && powerValue >= (expectValue - MISTASK_VALUE))
    {
        handlePowerOk();
    }
    else
    {
        qDebug()<< "is bad error for query power,stop it";
        ageingCtx->isPowerOk = false;
        updateTaskStep(AGING_ERROR_STEP_E);
        emit triggerTaskSignal();
    }
}
void AgeingWorker::finishedAgeingPhase()
{
    qDebug()<< "end age "<<ageingCtx->ageingStage;
    ageingTimer->stop();
    queryTimer->stop();
    if(ageingCtx->settedPower >= 0)
    {
//        ageingCtx->settedPower -= STEP_INCREASE_POWER + MAX_OVERLOAD_VALUE;
        ageingCtx->settedPower -= STEP_INCREASE_POWER;
        if(ageingCtx->settedPower <= 0)
        {
            ageingCtx->settedPower = 0;
        }
    }
    ageingCtx->powerPhase = POWER_UNLOAD_PHASE_E;
    updateTaskStep(UNLOAD_POWER_DOWN_STEP_E);
    emit triggerTaskSignal();
}
void AgeingWorker::resetEnvCtx()
{
    for(auto iter = devicesPairs.begin(); iter != devicesPairs.end();)
    {
        LinkManager::get()->disconnectHost(iter.key()->client->getPeerIp(),iter.key()->client->getPeerPort());
        LinkManager::get()->disconnectHost(iter.value()->client->getPeerIp(),iter.value()->client->getPeerPort());

        delete(iter.key());
        delete(iter.value());

        iter = devicesPairs.erase(iter);
    }
    if(!devicesList.isEmpty())
    {
        devicesList.clear();
    }

    if(!dataInterPoll.isEmpty())
    {
        qDeleteAll(dataInterPoll);
    }

    return ;
}
void AgeingWorker::handleAgeingEnd()
{
    QString testTip(tr("老化结束，结果处理中"));
    //最后默认都关机
    emit tipCtxSignal("关机中",90);
    for(auto iter = devicesList.begin();iter != devicesList.end();iter++)
    {
        QByteArray data;
        packBusinessData(iter.key(),30314,0,data);

        sendMsg(iter.key(),data);
    }

    resetEnvCtx();

    if(ageingTestResult == false)
    {
        if(errorResaon.isEmpty())
        {
            testTip=tr("老化异常，请排查pcs故障");
        }
        else
        {
            testTip =errorResaon;
        }
    }
    emit tipCtxSignal(testTip,100,ageingTestResult);
    emit displayInfoSignal(CharacterFormat::add(testTip,CharacterColor::GREEN_E));
    if(ageingTestResult==false)
    {
        emit testResultSignal(-1,CharacterFormat::change("NG",CharacterColor::RED_E));
    }
    else
    {
        emit testResultSignal(0,CharacterFormat::change("OK",CharacterColor::GREEN_E));
    }

    ageingStage = 1;
    isPowerUpPhase = 1;
    powerData = INIT_POWER_VALUE;
    currentTask = LINK_DEVICE_TASK;
    if(queryTimer)
    {
        queryTimer->stop();
    }
    if(respondTimer)
    {
        respondTimer->stop();
    }
    if(queryInfoTimer)
    {
        queryInfoTimer->stop();
    }
    emit finishedTestSiganl("ageing test",ageingTestResult);
    qDebug()<< "ageing end";
}
void AgeingWorker::handleReplyTimeout()
{
    respondTimer->stop();
    handleErro(tr("PCS回复超时，请排查通信"));
    return;
}
void AgeingWorker::sendMsg(EthernetClient * client,QByteArray & data)
{
    auto adapter = dataInterPoll.find(client);
    if(adapter != dataInterPoll.end())
    {
        auto tranId = modbusSendCtx.find(client);
        if(tranId == modbusSendCtx.end())
        {
            handleErro(tr("内部错误,找不到通信内容"));
            return ;
        }
        QByteArray out;
        packModbusData(tranId.value().tranId,tranId.value().unitId,data,out);
//        qDebug()<< "send modbus data="<<out.toHex();
        adapter.value()->sendSignal(out);
    }
}
void AgeingWorker::handleTemperatuer(EthernetClient * client,QByteArray & data)
{
    auto iter = devicesList.find(client);
    if(iter == devicesList.end())
    {
        return;
    }
    quint8 cnt = qFromBigEndian<quint8>(data.constData()+8)/2;
    int index = 9;
    for(int i = 0;i < cnt;i++)
    {
        quint8 higteByte = qFromBigEndian<quint8>(data.constData()+ index++);
        quint8  lowByte= qFromBigEndian<quint8>(data.constData()+ index++);
        qDebug()<< QString("%1").number(higteByte,16);
        qDebug()<< QString("%1").number(lowByte,16);
        int tempeartue =(int)(higteByte<<8) +
                         (int)lowByte;
        double realTemp = (double )tempeartue/100.0;
        emit displayInfoSignal(CharacterFormat::add(QString("温度信息:%1").arg(realTemp),CharacterColor::GREEN_E));
    }
    return;
}
void AgeingWorker::recvDataSlot(QByteArray data)
{
    EthernetClient * client = dynamic_cast<EthernetClient*>(sender());
    if(client)
    {
//        qDebug()<< "reply ip "<<client->getPeerIp();
        unsigned char modbusCmd = data[7];
        //只处理读的回复。
        if(modbusCmd == 0x3)
        {
            switch(currentTask)
            {
                case QUERY_PCS_STATUS_TASK:
                    handleRespondPcsStatus(client,data);
                    break;
                case AGEING_TASK:
                    if(currentStep == QEURY_PCS_POWER_STEP_E
                        || currentStep == UNLOAD_POWER_DOWN_STEP_E
                        || currentStep == AGING_TIME_STEP_E)
                    {
                        handleRespondPcsPower(client,data);
                    }
                    else if((currentStep & QUERY_INFO_STEP_E))
                    {
                        handleTemperatuer(client,data);
                    }
                    break;
                default:
                    qDebug()<< "not need process task"<< currentTask << " step"<<currentStep;
                    break;
            }
        }
    }
}
bool AgeingWorker::packBusinessData(EthernetClient * client ,short int address,int value,QByteArray & data,int valueLen)
{
    PCSPointCtx pointCtx;
    pcsData->getPcsPointCtx(address,pointCtx);

    data.append(pointCtx.cmd);
    data.append((address>>8) & 0xFF);
    data.append((address) & 0xFF);
    data.append((pointCtx.num>>8)&0xFF);
    data.append(pointCtx.num&0xFF);
    if( pointCtx.cmdType == WRITE_PCS_E)
    {
        data.append(pointCtx.num*2);
        if(valueLen == 2)
        {
            for(int i =1;i>=0;i--)
            {
                data.append((value>>(8*i))&0XFF);
            }
        }
        else if (valueLen == 4)
        {
            data.append((value>>8) & 0xff);
            data.append((value) & 0xff);
            data.append((value>>23) & 0xff);
            data.append((value>>16) & 0xff);
        }
    }

    return true;
}
void AgeingWorker::packModbusData(short int tranId,unsigned char unitId,QByteArray & data,QByteArray & out)
{
    //tcp modbus标准协议格式
    out.append((tranId>>8)&0XFF);
    out.append(tranId&0xFF);

    out.append((unsigned char)0x0);
    out.append((unsigned char)0x0);

    int len = data.size() + 1;
    out.append((len>>8)&0XFF);
    out.append(len&0xFF);

    out.append(unitId);

    out.append(data);
    return;
}
void AgeingWorker::openPowerOnOff(int canId,int onOff)
{
    if(pcsPowerModule)
    {
        pcsPowerModule->openPowerOnOff(canId,onOff);

    }
}
bool AgeingWorker::setPowerInfo(int canId,const QString & info)
{
    if(pcsPowerModule == nullptr)
    {
         pcsPowerModule = new PCSPower();
         bool ret = pcsPowerModule->linkCANDevice();
         if(ret==false)
         {
             return false;
         }
    }
    QRegularExpression infoExpress("((\\d+)[VA])");
    QRegularExpressionMatchIterator  iter = infoExpress.globalMatch(info);
    int vol = -1;
    int cur =-1;
    while(iter.hasNext())
    {
        QRegularExpressionMatch match = iter.next();
        if(match.hasMatch())
        {
            int value = match.captured(match.lastCapturedIndex()).toInt();
            if(match.captured(0).contains("V"))
            {
                vol = value;
            }
            else if(match.captured(0).contains("A"))
            {
                cur = value;
            }
        }
    }
    if(vol == -1 || cur == -1)
    {
        qDebug()<< "power set failed";
        return false;
    }
    int model=(info.contains(tr("高压"))?1:0);

    qDebug()<< "vol="<<vol<<"cur"<< cur<< "model"<<model;
    int ret = pcsPowerModule->sendData(canId,vol,cur,model);
    return ret <=0 ?false:true;
}
