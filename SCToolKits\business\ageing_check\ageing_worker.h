#ifndef AGEINGWORKER_H
#define AGEINGWORKER_H
#include "workers/test_worker.h"
#include "common_share/interface_business.h"
#include "communication/link_manager.h"
#include "pcs/pcs_data.h"
#include "pcs_power.h"
class DataSendSignal;
class AgeingWorker;
#define MODBUS_WRITE_CMD 0x10
#define MODBUS_REAAD_CMD 0x03
#define PCS_MAX_NUMBER 0x2

#define OPEN_PCS_QUERY_TIEM 20000
#define POWER_QUERY_TIME 1000
#define AGEING_TIME (2*60*60*1000)
#define INFO_QUERY_TIME (AGEING_TIME/2)
//#define AGEING_TIME (2*60*1000)

//#define MAX_PCS_POWER 230000
//#define MAX_PCS_POWER 75000
#define MAX_PCS_POWER 215000
//#define MAX_PCS_POWER 24000
#define STEP_INCREASE_POWER 5000

#define MISTASK_VALUE 10000
#define MAX_OVERLOAD_VALUE 3000
#define INIT_POWER_VALUE (MAX_OVERLOAD_VALUE)

#define GAP_POWER_VALUE 5000

typedef struct
{
    int index;
    QString ip;
    int port;
    EthernetClient *client;
    bool isCharge;
    bool hasSetPower;
    int expectPower;
    int realPower;
    int needSetPower;
    int avgRealPower;
    EthernetClient * pairClient;
    int tranId;
    short int runMode=0 ;
    bool isPowerOk = false;
    int queryCnt=0;
    const int maxQueryCnt = 3;
}AgeingDeviceInfo;

typedef struct
{
    short int tranId;
    unsigned char  unitId;
}ModbusTcpSendData;
//不支持嵌套的 Q_OBJECT
class DataSendAdpater: public QObject
{
    Q_OBJECT
    public:
        DataSendAdpater(){};
        ~DataSendAdpater(){};
    signals:
        void sendSignal(QByteArray data);
};

class AgeingWorker : public TestWorker,IBusiness
{
    Q_OBJECT
public:
    AgeingWorker();
    ~AgeingWorker();

    typedef enum
    {
        LINK_DEVICE_TASK = 0,
        SET_PCS_OPEN_TASK,
        QUERY_PCS_STATUS_TASK,

        AGEING_TASK,
        SET_CHARGE_TASK,
        QEURY_PCS_POWER_TASK,

        AGEING_END_TASK,
        UNKOW_MAX_TASK
    }TaskType;

    typedef enum
    {
       LOAD_POWER_UP_STEP_E=1,
       AGING_TIME_STEP_E = (1<<1),
       UNLOAD_POWER_DOWN_STEP_E = (1<<2),

       QUERY_INFO_STEP_E = (1<<3),
//       SET_POWER_STEP_E ,

       QEURY_PCS_POWER_STEP_E=(1<<4),

       AGING_ERROR_STEP_E
    }TaskStep;

    typedef enum
    {
        POWER_LOAD_PHASE_E,
        POWER_KEEP_PHASE_E,
        POWER_UNLOAD_PHASE_E,
        MAX_AGEING_PHASE_E
    }PowerPhase;
    typedef enum
    {
        FIRST_CHARGE_SECOND_DISCHARGE_E,//先充后放。充负正充。第一个阶段，第一个设备充，第二个设备放。
        FIRST_DISCHARGE_SECOND_CHARGE_E,//先放后充。第一个设备放，第二个设备充。
    }AgeingStage;

    typedef struct
    {
       AgeingStage ageingStage;
       bool isForPartPowerSet;
       bool isPowerOk;
       PowerPhase powerPhase;

       int settedPower;
       int pcsPowerOkCnt;
       int isEndPowerSet;
    }AgeingContext;
public:
    void startWorker(const QString toolName)override;
    void setAgeingTime(int sec);
    void setFullPowerRate(int rate);
    void setPowerInfo(int index,int vol,int cur,int model);
    bool setPowerInfo(int index,const QString &info);
    void openPowerOnOff(int canId,int onOff);

//内部的信号
signals:
    void triggerTaskSignal();

private slots:
    void processEtherLinkSlot(bool ret,const QString & ip,int port);
    void recvDataSlot(QByteArray data);
    void handleReplyTimeout();
    void queryInfoSlot();
private:
    void processBusiness();
    void updateTask(TaskType type,bool isTriggTask=true);
    void updateTaskStep(TaskStep step,bool isIncreas=true,int stepGap = 1);

    void packModbusData(short int tranId,unsigned char unitId,QByteArray & data,QByteArray & out);
public:
typedef void (AgeingWorker::*taskHandler)();
    void linkPcsDevice();
    void setPcsPower();
    void openPcs();
    void queryPcsStatus();
    void queryPcsPower();
    void finishedAgeingPhase();
    void handleAgeingProcess();
private:
    void initAgeingCtx();
    void resetEnvCtx();
    bool packBusinessData(EthernetClient * client ,short int address,int value,QByteArray & out,int valueLen = 2);
    void sendMsg(EthernetClient * client,QByteArray & data);
    void handleAgeingEnd();

    void handleRespondPcsStatus(EthernetClient* client,QByteArray & data);
    void handleRespondPcsPower(EthernetClient* client,QByteArray & data);

    void handleTemperatuer(EthernetClient * client,QByteArray & data);

    void handleErro(const QString & re);

    bool handlePowerOk();
    bool handlePowerError();
    bool queryPcsPower(EthernetClient * client);
    bool sendPcsPower(EthernetClient *client,int powerData);
    bool selectSetPowerStrategy();
    bool isSetChargeDevicePhase();
    int calculateNeedPowerValue();

    bool queryPcsTemperature(EthernetClient * client);


private:
    QMap<int,AgeingDeviceInfo> deviceInfo;

    QMap<TaskType,taskHandler>tasksList;
    TaskType currentTask;
    TaskStep currentStep;
    PCSData *pcsData;
    QTimer * queryTimer;
    QTimer * ageingTimer;
    QTimer * respondTimer;
    QTimer * queryInfoTimer;
    int  queryStatusOkCnt =0;
    int  linkCnt =0;
    int powerData;
    int ageingStage;//1:第一阶段;2：第二阶段。
    int isPowerUpPhase;//每个阶段，有功率增加和下降的设置和持续。1，上升，0 持续，-1,下降。

    AgeingContext *ageingCtx;
    QString ipVar;

    bool ageingTestResult;
    QString errorResaon;
    int ageingTimes = AGEING_TIME;
    int maxPcsPower = MAX_PCS_POWER;
    int queryInterval = INFO_QUERY_TIME;

 private:
    PCSPower *pcsPowerModule = nullptr;

private:
    QMap<EthernetClient*,AgeingDeviceInfo*>devicesList;
    QMap<EthernetClient*,DataSendAdpater*>dataInterPoll;
    QMap<EthernetClient*,ModbusTcpSendData>modbusSendCtx;
    QMap<AgeingDeviceInfo*,AgeingDeviceInfo*>devicesPairs;
};

#endif // AGEINGWORKER_H
