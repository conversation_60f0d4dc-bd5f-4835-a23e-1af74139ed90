#include "bess_cabinet_worker.h"
#include <QDebug>

BESSCabinetWorker::BESSCabinetWorker():
    sshWorker(nullptr),
    businessTimer(nullptr),
    timerIndex(0)
{
    connect(this, &BESSCabinetWorker::startBusinessSignal, this, &BESSCabinetWorker::processBusiness, Qt::QueuedConnection);
}

BESSCabinetWorker::~BESSCabinetWorker()
{
    if (sshWorker)
    {
        delete sshWorker;
        sshWorker = nullptr;
    }

    if (businessTimer)
    {
        delete businessTimer;
        businessTimer = nullptr;
    }
}

void BESSCabinetWorker::startWorker(const QString toolName)
{
    qDebug() << "startWorker:" << toolName;
    emit startBusinessSignal();
}

void BESSCabinetWorker::processBusiness()
{
    // 加密相关的内容
    qDebug() << "正在加密";

    if(sshWorker == nullptr)
    {
        sshWorker = new SSHTestWorker();
    }

    // 从DeviceContext获取当前设备连接信息
    ip = DeviceContext::get()->getDeviceLinkIP();
    user = DeviceContext::get()->getSSHLoginUser();

    if (ip.isEmpty())
    {
        qDebug() << "未找到设备IP信息";
        return;
    }

    if (user.isEmpty())
    {
        user = "root"; // 默认用户
    }

    SSHClient *sshClient = LinkManager::get()->getSSH(ip);
    if(sshClient)
    {
        connect(sshWorker, &SSHTestWorker::sendDataSignal, sshClient, &SSHClient::sendData, Qt::UniqueConnection);
        connect(sshClient, &SSHClient::sigDataArrived, this, &BESSCabinetWorker::recvSSHRespond, Qt::UniqueConnection);
    }

    if(businessTimer == nullptr)
    {
        businessTimer = new QTimer();
        connect(businessTimer, &QTimer::timeout, this, &BESSCabinetWorker::processBusinessTimeout);
    }

    // 检查SSH连接状态
    currentSSHStatus = LinkManager::get()->getSSHLinkedStatus(ip, user);

    if (!currentSSHStatus)
    {
        qDebug() << QString("SSH未连接到 %1@%2").arg(user, ip);
        return;
    }

    start();
}

void BESSCabinetWorker::start()
{
    QString cmdStr("ls" + QString("\n"));
    emit sshWorker->sendDataSignal(cmdStr);
    businessTimer->start(2000);  // 2秒触发一次
}

void BESSCabinetWorker::processBusinessTimeout()
{
    timerIndex++;
    qDebug() << timerIndex;

    // 检测SSH连接状态
    currentSSHStatus = LinkManager::get()->getSSHLinkedStatus(ip, user);

    if(!currentSSHStatus)
    {
        timerIndex = 0;
        businessTimer->stop();

        // 发送信号通知controller
        emit testResultSignal(true);

        // 上报MES
        emit finishedTestSiganl("EncryptAndCloseSSH", true);
    }

    // 超过10秒判定失败
    if(timerIndex == 5)
    {
        timerIndex = 0;
        businessTimer->stop();

        emit testResultSignal(false);
        emit finishedTestSiganl("EncryptAndCloseSSH", false);

        qDebug() << "SSH关闭操作超时（10秒）";

    }
}

void BESSCabinetWorker::recvSSHRespond(const QString & msg)
{
    qDebug() << "ssh msg" << msg;
}

