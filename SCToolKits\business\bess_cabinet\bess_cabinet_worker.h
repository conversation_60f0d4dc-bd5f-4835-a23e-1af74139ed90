#ifndef BESSCABINETWORKER_H
#define BESSCABINETWORKER_H

#include <QObject>
#include "workers/test_worker.h"
#include "common_share/interface_business.h"
#include "builders/ssh_operate_builder.h"
#include "workers/ssh_test_worker.h"
#include "link_manager.h"
#include "business/workers/ssh_query_worker.h"
#include "common_share/test_manager.h"

class BESSCabinetWorker: public TestWorker, public IBusiness
{
    Q_OBJECT
public:
    BESSCabinetWorker();
    ~BESSCabinetWorker();

    // 实现IBusiness接口
    void startWorker(const QString toolName) override;

signals:
    void operationFinishedSignal(bool success, const QString &message);
    void startBusinessSignal();
    void sshCloseResult();
    void testResultSignal(bool ret);

private:
    void processCheck(int objectIndex, int operateIndex);
    void updateTestCtx(TestObject *object, int objectIndex, int operateIndex);
    void start();
    int processResult(const QString &msg);

private slots:
    void processBusiness();
    void processBusinessTimeout();
    void recvSSHRespond(const QString & msg);

private:
    SSHTestWorker *sshWorker;
    QTimer * businessTimer = nullptr;

    QString ip;
    QString user = "root";
    bool currentSSHStatus;
    int timerIndex;


};

#endif // BESSCABINETWORKER_H
