#include "fct_board_builder.h"

FCTBoardBuilder::FCTBoardBuilder()
{

}
FCTBoardBuilder::~FCTBoardBuilder()
{

}
void FCTBoardBuilder::getTestObjectNameList(QStringList & nameList)
{
    Q_UNUSED(nameList);
    return;
}

int FCTBoardBuilder::getTestObjectCount()
{
    return 0;
}

bool FCTBoardBuilder::getTestObjectsFunsList(QStringList & funs)
{
    Q_UNUSED(funs);
    return false;
}

void FCTBoardBuilder::getTaskOperateList(QStringList &taskOperateList)
{
    Q_UNUSED(taskOperateList);
    return;
}

bool FCTBoardBuilder::hasAdditionalInfo()
{
    return false;
}

int FCTBoardBuilder::getRespondResult(QByteArray &msgBody, QMap<int, QString> &additionalTestResult)
{
    Q_UNUSED(additionalTestResult);

    int respondResult =  (msgBody[0]) + (msgBody[1]<<8)
                         +(msgBody[2]<<16)+(msgBody[3]<<24);
    return respondResult;
}
