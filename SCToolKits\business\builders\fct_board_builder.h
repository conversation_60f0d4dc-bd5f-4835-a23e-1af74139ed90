#ifndef FCTBOARDBUILDER_H
#define FCTBOARDBUILDER_H

#include "test_object_builder.h"
class FCTBoardBuilder : public TestObjectBuilder
{
public:
    FCTBoardBuilder();
    ~FCTBoardBuilder();

    virtual void getTestObjectNameList(QStringList &list);
    virtual int getTestObjectCount();
    virtual bool getTestObjectsFunsList(QStringList & funs);
    virtual void getTaskOperateList(QStringList &taskOperateList);

    // 附加测试结果的处理
    virtual bool hasAdditionalInfo();
    virtual int getRespondResult(QByteArray &msgBody, QMap<int, QString> &additionalTestResult);
};

#endif // FCTBOARDBUILDER_H
