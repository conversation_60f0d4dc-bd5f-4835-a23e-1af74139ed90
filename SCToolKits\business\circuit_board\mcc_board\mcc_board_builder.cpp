#include "mcc_board_builder.h"

MCCBoardBuilder::MCCBoardBuilder():testIndex(0)
{
    int index = 0;
    supportTests[CHECK_MCC_BOARD] = index++;
}

void MCCBoardBuilder::setBuildCaseHook(QMap<QString, MCCBoardBuilder::handle> &caseHookList)
{
    caseHookList[CHECK_MCC_BOARD] = &MCCBoardBuilder::buildMCCBoardCheckBusiness;
}

void MCCBoardBuilder::buildMCCBoardCheckBusiness()
{
    QString pinName=("MCC批量检测");
    TestObject  * ptr =  new TestObject(pinName, COMM_TEST_OBJECT_TYPE);
    TestOperate operate;
    QByteArray data;

    fillOperate(operate,data,QString::fromUtf8("正在进行MCC批量检测"),
                QString::fromUtf8("MCC检测正常"),QString::fromUtf8("MCC检测失败"));
    ptr->addOperate(MCC_BOARD_CHECK_TASK, operate);
    testCases[testIndex++] = ptr;
}

bool MCCBoardBuilder::getTestObjectsFunsList(QStringList & funs)
{
    funs << CHECK_MCC_BOARD;
    return true;
}

void MCCBoardBuilder::getTaskOperateList(QStringList & taskOperateList)
{
    taskOperateList << "测试1";
    taskOperateList << "测试2";
    taskOperateList << "测试3";
    taskOperateList << "测试4";
    taskOperateList << "测试5";
    taskOperateList << "测试6";
}

bool MCCBoardBuilder::hasAdditionalInfo()
{
    return true;
}

int MCCBoardBuilder::getRespondResult(QByteArray &msgBody, QMap<int, QString> &additionalTestResult)
{
    // 在此处进行特殊的报文处理
    int respondResult =  (msgBody[0]) + (msgBody[1]<<8);

    QByteArray additionalInfo;
    additionalInfo.append(msgBody[2]);
    additionalInfo.append(msgBody[3]);

    // 保证上一次的测试结果被清空
    additionalTestResult.clear();

    processAdditionalInfo(additionalInfo, additionalTestResult);

    return respondResult;
}

void MCCBoardBuilder::processAdditionalInfo(QByteArray &additionalInfo, QMap<int, QString> &additionalTestResult)
{

    // 小端字节序转换为u16类型数据
    uint16_t value = static_cast<uint8_t>(additionalInfo[0]) | (static_cast<uint8_t>(additionalInfo[1]) << 8);

    // 使用QbitArray保存数据
    QBitArray bits(16);
    for(int i = 0; i < 16; ++i)
    {
        bits.setBit(i, value & (1 << i));
    }

    // 获取任务列表
    QStringList taskOperateList;
    getTaskOperateList(taskOperateList);

    // 筛选值为1的失败测试任务
    for(int i = 0; i < taskOperateList.size(); ++i)
    {
        if(bits.testBit(i))
        {
            additionalTestResult[i] = taskOperateList[i];
        }
    }

}

TestObject *MCCBoardBuilder::getTestObject(int index)
{
    auto iter =  testCases.find(index);
    if(iter != testCases.end())
    {
         return iter.value();
    }
    return nullptr;
}

void MCCBoardBuilder::getTestObjectNameList(QStringList & list)
{
    if(!testCases.isEmpty())
    {
        int index = 0;
        for(auto iter = testCases.begin(); iter != testCases.end(); iter++)
        {
            list.insert(index++, iter.value()->getName());
        }
    }
    return;
}

int MCCBoardBuilder::getTestObjectCount()
{
    if(testCases.isEmpty())
    {
        return 0;
    }
    return testCases.count();
}

void MCCBoardBuilder::fillOperate(TestOperate &operate, QByteArray &data,
                                                    QString beginTip, QString successTip, QString failed, TipTaskType tipType)
{
    operate.auxDataInfo = data;
    operate.tipContent.tipCtx.insert(BEGIN_TIP_E, beginTip);
    operate.tipContent.tipCtx.insert(SUCCESS_TIP_E, successTip);
    operate.tipContent.tipCtx.insert(FAILED_TIP_E, failed);
    operate.tipContent.tipType = tipType;
}

void MCCBoardBuilder::fillOperate(TestOperate &operate,int data,
                                          QString  beginTip,QString  successTip,QString failed,
                                          TipTaskType tipType)
{
    operate.data = data;
    operate.tipContent.tipCtx.insert(BEGIN_TIP_E, beginTip);
    operate.tipContent.tipCtx.insert(SUCCESS_TIP_E, successTip);
    operate.tipContent.tipCtx.insert(FAILED_TIP_E, failed);
    operate.tipContent.tipType = tipType;
}

void MCCBoardBuilder::build(QString & ,QStringList & list)
{
    QMap<QString,handle>caseHookList;
    setBuildCaseHook(caseHookList);

    QMap<int,TestObject*> testLists;
    foreach(auto &testInfo,list)
    {
        auto iter = supportTests.find(testInfo);
        if(iter != supportTests.end())
        {
            auto buildIter = caseHookList.find(testInfo);
            if(buildIter != caseHookList.end())
            {
                (this->*buildIter.value())();
            }
        }
    }
    return;
}

