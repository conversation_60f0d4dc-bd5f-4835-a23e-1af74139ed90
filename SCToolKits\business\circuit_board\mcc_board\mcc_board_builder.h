#ifndef MCCBOARDBUILDER_H
#define MCCBOARDBUILDER_H

#include "test_objects/test_object.h"
#include "builders/fct_board_builder.h"

class MCCBoardBuilder:public FCTBoardBuilder
{
public:
    MCCBoardBuilder();

    virtual void build(QString & materialCode,QStringList &list);

    TestObject * getTestObject(int index)override;
    void getTestObjectNameList(QStringList &list)override;
    int getTestObjectCount()override;
    bool getTestObjectsFunsList(QStringList & funs)override;
    void getTaskOperateList(QStringList &taskOperateList)override;
    int getRespondResult(QByteArray &msgBody, QMap<int, QString> &additionalTestResult) override;
    bool hasAdditionalInfo() override;

private:
    void fillOperate(TestOperate &operate,QByteArray & data,
                      QString  beginTip,QString  successTip,QString failed,
                      TipTaskType tipType = TIP_NO_BLOCK_CHECK_TASK_E);
    void fillOperate(TestOperate &operate,int data,
                      QString  beginTip,QString  successTip,QString failed,
                      TipTaskType tipType = TIP_NO_BLOCK_CHECK_TASK_E);
    void fillData(int ctx, QByteArray &data);

    void setGpioInfo(QByteArray & data, int pinCluster, int pinNum, int pinLevel, int delay);
    void setVoltageRangeInfo(QByteArray & data, int pinCluster, int pinNum, double start, double end);


    typedef void (MCCBoardBuilder::*handle)();
    void setBuildCaseHook(QMap<QString,handle>&caseHookList);

    void buildMCCBoardCheckBusiness();

private:
    int testIndex;
    QMap<int,TestObject*>testCases;
    QMap<QString,int>supportTests;
};

#endif // MCCBOARDBUILDER_H
