#include "test_manager.h"
#include "mes/mes_manager.h"
#include <QDebug>
#include "toolkits_manager.h"
#include "business/common_share/factory_test_policy.h"
#include "link_manager.h"
#include "trace_recorder.h"
#include "app_config.h"
#include "material_base/material_base.h"
#include "card_config/card_config_worker.h"
#include "voltage_check/voltage_check_worker.h"
#include "devices/device_context.h"
#include "device_mes_worker.h"
#include "interface_business.h"

TestManager* TestManager::instance = nullptr;
TestManager::TestManager(QObject *parent) : QObject(parent),
                                            testPolicy(nullptr),
                                            linkType(MAX_LINK_DEVICE_TYPE),
                                            linkCoditionStatus(true),
                                            testWorker(nullptr),
                                            tipController(nullptr),
                                            mesDepend(true),
                                            rootModelEnable(false),
                                            testEquipmentCode(""),
                                            toolsSelectEnable(false),
                                            businessManager(new BusinessManger),
                                            testResultInfo(new TestResult),
                                            testInformation(new TestInformation)
{
    //注意如果TestManager static instanc 不是指针，则有问题。
    connect(MesManager::get(),&MesManager::checkV2FinshedSignal,this,&TestManager::handleTestPassedCheckResultSlot);
    connect(MesManager::get(),&MesManager::equipmentsInfoSignal,this,&TestManager::handleEquipmentsInfoFromMesSlot);
    connect(MesManager::get(),&MesManager::productInfoSignal,this,&TestManager::handleProductInfoSlot);

    connect(this, &TestManager::startTestSignal, businessManager, &BusinessManger::start,Qt::UniqueConnection);
    connect(this, &TestManager::updateTestStatusSignal, businessManager, &BusinessManger::updateTestStatusSlot,Qt::UniqueConnection);

    LinkManager * linkManager = LinkManager::get();
    connect(linkManager,&LinkManager::serialPortLinkResult,&serialPortSelector,&SerialPortSelector::saveLinkedSerialPortName);
    connect(&serialPortSelector,&SerialPortSelector::toolSerialPortCheckResult,this,&TestManager::handleSerialPortCheckResult);
}
TestManager::~TestManager()
{
    delete businessManager;
    delete testResultInfo;
    if(tipController)
    {
        delete tipController;
    }
}
void TestManager::setTipController(TipController *c)
{
    if(c)
    {
        tipController = c;
        connect(this,&TestManager::tipCtxSignal, tipController, &TipController::showTipWindowSlot);
        connect(this,&TestManager::closeTipSignal, tipController, &TipController::closeTipWinowSlot);
    }
    return;
}
void TestManager::addTestWorker(TestWorker* w,bool needConnect)
{
   businessManager->addTestWorker(w);
   if(needConnect)
   {
       connect(w,&TestWorker::tipCtxSignal,this,&TestManager::handleTipSlot);
       connect(w,&TestWorker::testInfoSignal,this,&TestManager::recordTestInfoSlot);
   }
   return;
}
void TestManager::handleTipSlot(const QString & tip,int process,bool ret,int displayControl)
{
    if(ret)
    {
        if(process == 100)
        {
            //完成的时候，显示一个确定按钮键。
            int tipAux = (MODELESS|OK|TIPS);
            MSG_MODE msgMode =(MSG_MODE) (tipAux &(int) MODELESS);
            MSG_TYPE msgType =(MSG_TYPE) (tipAux &(int) OK);
            MSG_TIP_TYPE msgTipType=(MSG_TIP_TYPE)(tipAux &(int) TIPS);
            emit tipCtxSignal(tip,msgMode, msgType, msgTipType);
            QTimer::singleShot(1000,[=]()
            {
                emit closeTipSignal();
            });
        }
        else
        {
            emit tipCtxSignal(tip,(MSG_MODE)(displayControl&0x300), (MSG_TYPE)(displayControl&0x07), (MSG_TIP_TYPE)(displayControl&0xE8));
        }
    }
    else
    {
        setErrorReason(tip);
        int tipAux = (MODELESS|OK|TIPS);
        MSG_MODE msgMode =(MSG_MODE) (tipAux &(int) MODELESS);
        MSG_TYPE msgType =(MSG_TYPE) (tipAux &(int) OK);
        MSG_TIP_TYPE msgTipType=(MSG_TIP_TYPE)(tipAux &(int) TIPS);
        emit tipCtxSignal(tip,msgMode, msgType, msgTipType);
        //失败的时候，显示一个确定按钮，显示2分钟
        QTimer::singleShot(120000,[=]()
        {
            emit closeTipSignal();
        });
    }
}
void TestManager::setMesDepend(bool depend)
{
    mesDepend=depend;
    emit mesDependSignal(depend);
}
bool TestManager::getMesDepend()const
{
    return mesDepend;
}

void TestManager::setMesDeviceIdModifyEnable(bool enable)
{
    mesIdModify = enable;
}
bool TestManager::isMesDeviceModifyEnable()const
{
    return mesIdModify;
}
void TestManager::setErrorReason(const QString &reason)
{
    errorReason = reason;
}
QString TestManager::getErrorReason()const
{
    return errorReason;
}
void TestManager::updateMesCtxSlot(const QString & deviceId,const QString  &accout,const QString &password)
{
    deviceid = deviceId;
    DeviceContext::get()->setMesLogicDeviceId(deviceId);
    DeviceContext::get()->setMesLoginAccount(accout);
}

void TestManager::appendTestItemResultSlot(QJsonObject &result)
{
    testResultInfo->appendTestContent(result);
}

void TestManager::clearTestItemResultSlot()
{
    testResultExtendInfo = "";
    testResultInfo->clearTestContent();
}

bool TestManager::needCheckTestPassed(const QString & sn)
{
    if(ToolKitsManager::get()->isRandomGeneratorTool(currentTool) ||
       ToolKitsManager::get()->isPrintTool(currentTool))
    {
        return false;
    }
    return true;
}
void TestManager::updateCrossMistakeProofing(ToolFunctionType type,bool needCrossProofing)
{
    if(type < UNKOWN_TOOL_FUNCTION_E)
    {
         crossCheckProofing[type] = needCrossProofing;
    }
}
bool TestManager::needMistakeProofing()
{
    auto iter = crossCheckProofing.find(toolBindFunType);
    if(iter != crossCheckProofing.end())
    {
        return iter.value();
    }
    //TODO 删除
    if(ToolKitsManager::get()->isNetworkCheckTool(currentTool) ||
       ToolKitsManager::get()->isSingleBoardCheckTool(currentTool))
    {
        return false;
    }
    return true;
}
void TestManager::processSNSlot(const QString & sn)
{
    //TODO:先做合法性检测，然后再做过站处理
    DeviceContext::get()->setSN(sn);
    setSnCode(sn);
    if(needCheckTestPassed(sn))
    {
       emit tipCtxSignal("从MES获取过站信息，请稍等", MODELESS, NO, TIPS);
       checkV2Slot();
    }
    emit snInputFinishedSignal(sn);
}
void TestManager::processMaterialSlot(const QString & code)
{
    DeviceContext::get()->setMaterialCode(code);
    emit materialCodeInputFinishedSignal(code);
}
//需要测试前，确保此槽被处理，不然不可用。
void TestManager::handleSNSlot(QString & sn)
{
    //TODO根据不同的产品线，判定格式的合法性。
    //从MES或者本地维护的地方，获取物料号及对应的功能列表。
    QString materialFrom =  APPConfig::get()->getStringValue("SN/snWithMaterialFrom");
    QString matrialCode;
    QStringList functions;
    if(materialFrom.isEmpty() || materialFrom.contains("Local"))
    {
        QString converntSN = QString("SN")+sn;
        MaterialBase::get()->getMaterialCode(converntSN,matrialCode);

        MaterialBase::get()->getFuns(matrialCode,functions);
        if(functions.isEmpty())
        {
            //TODO 弹框提示。
            qDebug()<< "not found test fun return";
            return;
        }
    }
    else
    {
          //TODO:根据sn号，通过MES,获取物料信息。物料信息含有测试信息。
    }
    //TODO;

    TestManager::get()->buildTestCtx(matrialCode,functions);

    //创建测试策略
    //获取到使用者
    TestManager::get()->buildTestPolicy(FCT_Line_Tester);

    DeviceContext::get()->setSN(sn);
    snCode = sn;
}
void TestManager::buildTestPolicy(UserType user)
{
    switch (user)
    {
        case FCT_Line_Tester:
        case AC_Line_Tester:
        {
            buildFactoryPolicy();
            break;
        }
        default:
        break;
    }
    return;
}
void TestManager::updateSelectWorkerCtx(ToolFunctionType type,bool skipSelect)
{
    if(type < UNKOWN_TOOL_FUNCTION_E)
    {
         skipSelectTestWorkerCtx[type] = skipSelect;
    }
}

void TestManager::updateSelectSerialPortCtx(ToolFunctionType type, bool skipSelect)
{
    if(type < UNKOWN_TOOL_FUNCTION_E)
    {
         skipSelectSerialPortCtx[type] = skipSelect;
    }
}
void TestManager::selectTestWorker(QString & tool)
{
    testResult.clear();
    QString eqiupmentCode;
    eqiupmentCode =TestManager::get()->getTestEquipmentCode();
    if(needMistakeProofing())
    {
        if(hasBuilderMistakeProofing == false)
        {
            hasBuilderMistakeProofing = true;
            MistakeProofingWorker * worker = new MistakeProofingWorker(mistakeProofingData);
            businessManager->addTestWorker(worker);
            connect(worker,&MistakeProofingWorker::tipCtxSignal,this,&TestManager::tipCtxSignal);
            connect(this, &TestManager::sendMistakeProofingData, worker, &MistakeProofingWorker::setMistakeProofingData);
        }
        emit sendMistakeProofingData(mistakeProofingData);
    }

    auto iter = skipSelectTestWorkerCtx.find(toolBindFunType);
    if(iter != skipSelectTestWorkerCtx.end())
    {
        startRecordLocalReport(tool);
        return;
    }
    //todo
    {
        if(testWorker == nullptr)
        {
            testWorker =  new FlowTestWorker(tool);
            //test 开始与完成。
            connect(this, &TestManager::startTestSignal, testWorker, &FlowTestWorker::startTest);
            connect(this, &TestManager::retestSignal, testWorker, &FlowTestWorker::resetTestObject);
            connect(testWorker,&FlowTestWorker::finishedTestSiganl, this, &TestManager::recvToolTestResultSlot);

            //显示控制：
            connect(testWorker,&FlowTestWorker::sendTipContext, this, &TestManager::sendTestObjectContext);
            //testflowerwork的提示关联
            connect(testWorker, &FlowTestWorker::finishedTestSiganl, tipController, &TipController::closeTipWinowSlot);
            connect(testWorker, &FlowTestWorker::pausedTestSignal,tipController, &TipController::closeTipWinowSlot);
            connect(testWorker, &FlowTestWorker::emitTipCtxSignal, tipController, &TipController::showTipWindowSlot);

            //testflowwork与串口关联。
            SerialWorker * serialPortPtr = serialPortSelector.getSerialPort(tool,1);
            if(serialPortPtr != nullptr)
            {
                connect(testWorker,&FlowTestWorker::sendDataSignal,serialPortPtr,&SerialWorker::sendDataSlot);
                //关联父类的槽。跟是否是虚函数无关。只会调用父类的槽。
                connect(serialPortPtr,&SerialWorker::transformNetMsg,testWorker,&TestWorker::recvDataSlot);
            }
            serialPortPtr = serialPortSelector.getSerialPort(tool,2);
            if(serialPortPtr != nullptr)
            {
                connect(testWorker,&FlowTestWorker::sendVoltageDataSignal,serialPortPtr,&SerialWorker::sendDataSlot);
                connect(serialPortPtr,&SerialWorker::transformNetMsg,testWorker,&FlowTestWorker::recvVoltageAcquisitionData);
            }
        }
        //TODO
    }
    //本地测试记录过程
    startRecordLocalReport(tool);
}

void TestManager::selectSerialPort(QString &tool)
{
    bool ret = serialPortSelector.getToolSerialCheckResult(tool);
    if(!ret)
    {
        serialPortSelector.checkSerial(tool);
    }
    else
    {
        emit startTestSignal(tool);
    }
}

void TestManager::handleSerialPortCheckResult(QString tool, bool result, QString errorCode)
{
    if(result)
    {
        emit closeTipSignal();
        selectTestWorker(tool);
        emit startTestSignal(tool);
        currentTool = tool;
    }
    else
    {
        tipCtxSignal(errorCode,MODELESS,OK,FAILED);
    }
}

void TestManager::updateCurrentToolName(const QString & tool, bool isSwitchSuccess)
{
    if(isSwitchSuccess)
    {
        qDebug() <<"switch tool to = " <<tool;
        currentTool = tool;
    }
}
void TestManager::triggerTestSlot(const QString & fun,const QStringList & ctx)
{
    //TODO:
    QString tools("cardWriteSystemTool");
    startTestSlot(tools);
}

void TestManager::startTestSlot(QString & tool)
{
    if(businessManager->isBusinessTestIng())
    {
        qDebug()<< "testing wait";
        return;
    }
    if(!needSelectSerialPort(tool) && needSelectSerialPort(toolBindFunType))
    {
        serialPortSelector.checkSerial(tool);
    }
    else
    {
        selectTestWorker(tool);
        emit startTestSignal(tool);
    }
}

void TestManager::startRecordLocalReport(QString & tool)
{
    TraceRecorder * recorder =TraceRecorder::get();
    recorder->openRecorder(tool);
    connect(this,&TestManager::recordTestSignal,recorder,&TraceRecorder::recordTraceInfo);

    QString newToolRecord=QString("start test tool:%1").arg(tool);
    emit recordTestSignal(newToolRecord);
}
void TestManager::buildFactoryPolicy()
{
    if(testPolicy == nullptr)
    {
        testPolicy =  new FactoryTestPolicy(AC_Line_Tester);
    }
    testPolicy->setTestType(Signal_Module_Test);
//    testPolicy->setTestType(Multi_Module_Test);
}
void TestManager::setUser(int cat)
{
    UserType type = (UserType)cat;
    if(type < Max_User)
    {
        if (testPolicy)
        {
            testPolicy->setUser(type);
        }
        else
        {
            testPolicy =  new FactoryTestPolicy(type);
            testPolicy->setTestType(Signal_Module_Test);
        }
    }
}
UserType TestManager::getUser()const
{
    return testPolicy->getUser();
}
void TestManager::buildTestCtx(const QString & material,QStringList & funs)
{
    if(funs.isEmpty() ||  material.isEmpty())
    {
        qDebug()<< "not functions set";
        return;
    }

    TestContext testContext;
    testContext.materialCode = material;
    foreach(auto var,funs)
    {
        testContext.functions.push_back(var);
    }
    testCtx[material]=testContext;
}
void TestManager::setRootModel(bool enable)
{
    if(rootModelEnable != enable)
    {
        rootModelEnable = enable;
        //更新数据库
        SQLiteManager::get()->insert(ROOT_MODEL_CONTROL,enable);
        emit updateRootModelSignal(rootModelEnable);
    }
}

void TestManager::setToolsSelectEnable(bool enable)
{
    if(toolsSelectEnable != enable)
    {
        toolsSelectEnable = enable;
        emit updateToolSelectEnableSignal(enable);
        //更新数据库
        SQLiteManager::get()->insert(TOOL_SELECT_ENALE_CONTROL,enable);
    }
}
bool TestManager::processSwitchTest(QString & tool)
{
    QString chineseName = ToolKitsManager::get()->getToolChineseName(tool);
    if(testPolicy->getTestType() == Signal_Module_Test)
    {
        qDebug() << "not need switch tool";
        //不切换
        emit finishedTest(chineseName,0);
        QString allResultInfo("all test case end");
        emit recordTestSignal(allResultInfo);
        return false;
    }
    else
    {
        QString testName;
        //TODO:material的使用
        foreach(auto &var,testCtx)
        {
            int max = var.functions.count();
            int index = var.functions.indexOf(tool);
            if(index != -1)
            {
                int nextIndex = index+1;
                if(nextIndex < max)
                {
                    testName =  var.functions.at(nextIndex);
                    break;
                }
                else
                {
                    break;
                }
            }
        }
        if(!testName.isEmpty())
        {
            chineseName = ToolKitsManager::get()->getToolChineseName(testName);
            QString newToolRecord=QString("start tool:%1").arg(testName);
            emit recordTestSignal(newToolRecord);
            qDebug() << "switch tool test "<< testName;
            emit switchTest(chineseName);
            return true;
        }
        else
        {
            //TODO:没有找到，就认为全部结束
            emit finishedTest(chineseName,0);
            QString allResultInfo("all test case end");
            emit recordTestSignal(allResultInfo);
            return false;
        }
    }
}
void TestManager:: upLoadMultiDevices()
{
    for(auto iter = testResultList.begin();iter != testResultList.end();iter++)
    {
        TestFinishContext & testFinshCtx = *iter;
        QString uploadValue("OK");
        bool uploadOK = true;
        if(testFinshCtx.testResult == false || testFinshCtx.sn.isEmpty())
        {
            QString tip;
            tip.append(tr("%1测试失败,原因%2,是否手动上传MES").arg(testFinshCtx.sn).arg(testFinshCtx.errReason));
            uploadOK = tipCtxSignal(tip,MODAL,OKCANCEL,TIPS);
            if(uploadOK)
            {
                uploadValue="NG";
            }
            else
            {
               emit mesUploadInfoSignal(testFinshCtx.sn,1,tr("未上传到MES"));
               qDebug()<< "human choose not upload sn to mes"<<testFinshCtx.sn;
            }
        }

        if(uploadOK)
        {
            emit mesUploadInfoSignal(testFinshCtx.sn,0,"upload mes start");
            MesRequestData postData;
            postData.mesRequestType = DATA_TO_MES;
            postData.mesPortType = MES_UPLOAD_DATA;
            postData.deviceId = deviceid; //工具套

            postData.dataInfo = testFinshCtx.testData;
            postData.result = uploadValue; //获取最终结果
            postData.deviceSN = testFinshCtx.sn;
            MesManager::get()->postData(postData);
        }
    }
    testResultList.clear();
}
void TestManager::recvToolTestResultSlot(QString & tool,bool ret)
{
    if(isParallelMode)
    {
        upLoadMultiDevices();
        return;
    }
    qDebug() << tool<<" tool tests result is "<<ret;
    //日志记录。
    int result;
    QString resultInfo=QString("%1 tool-------->%2-------->result:").arg(tool).arg(snCode);
    if(ret)
    {
        resultInfo.append("OK");
        result = 1;
    }
    else
    {
        resultInfo.append("NG");
        result = 0;
    }
    emit recordTestSignal(resultInfo);
    //保存测试结果
    //todo materialCode;
    QString materialCode("debugMatrial");
    updateTestResultSlot(materialCode,tool,result);
    //统一处理是否需要切换。
    bool switchOK = processSwitchTest(tool);

    bool uploadOK = false;
    QString tip;
    if(!ret)
    {
        if(errorReason.isEmpty())
        {
            tip.append(tr("测试失败，是否需要手动上传MES"));
        }
        else
        {
            tip.append(tr("错误原因:")+errorReason + tr("\n测试失败,是否需要上传MES"));
        }
        uploadOK = tipCtxSignal(tip,MODAL,OKCANCEL,TIPS);
    }
    else
    {
        tip.append("测试内容验证正确，测试完成");
        uploadOK = true;
    }

    if(mesDepend)
    {
        //TODO:更多的策略判定是否需要上传MES.
       if(switchOK==false && uploadOK==true)
       {
           handleTestResultPreProcess();
           uploadToMesSlot();
       }
    }
    else
    {
        //error的显示优化
        handleTipSlot(tip,100,ret);
    }

    return;
}
void TestManager::updateTestResultSlot(QString & materialCode,QString & testFuctionName, int &result)
{
    //测试完成，则 emit finishedTest
    FuctionTestResult testResultCtx;
    testResultCtx.testResult = result;
    testResultCtx.testFuntion = testFuctionName;
    testResultCtx.hasTested = 1;
    testResultCtx.materialCode = materialCode;
    testResult.insert(testFuctionName, testResultCtx);
}

void TestManager::resetTestResultSlot()
{

}
TestContext::TestContext()
{
    //TODO获取

}
TestContext::~TestContext()
{

}
void TestContext::initFunctions(const QString & material,QStringList & funs)
{

}

void TestContext::addFuntionBack(QString &functionName)
{

}

void TestContext::deleteFunction(QString &functionName)
{

}

QString TestManager::getTestResult()
{
    QString finalResult;
    int passCnt = 0;
    for(QMultiMap<QString,FuctionTestResult>::iterator iter = testResult.begin(); iter != testResult.end(); iter++)
    {
        if(iter.value().testResult == 1)
        {
            passCnt++;
        }
        else
        {
            passCnt = 0;
        }
    }

    if(passCnt >= testResult.count())
    {
        finalResult = "OK";
    }
    else
    {
        finalResult = "NG";
    }

    return finalResult;
}

QString TestManager::getTestResultExtendInfo()
{
    QJsonObject data;

    if(testResultExtendInfo.isEmpty())
    {
        for(QMultiMap<QString,FuctionTestResult>::iterator iter = testResult.begin(); iter != testResult.end(); iter++)
        {
            QString resultStr = iter.key() + QString("_Test_Result");
            QString testResultStr;
            if(iter.value().testResult == 1)
            {
                testResultStr = "OK";
            }
            else
            {
                testResultStr = "NG";
            }

            data.insert(resultStr,testResultStr);
        }
        testResultExtendInfo = QString(QJsonDocument(data).toJson(QJsonDocument::Compact));
    }

    return testResultExtendInfo;
}

void TestManager::handleTestResultPreProcess()
{
    if(ToolKitsManager::get()->isNetworkCheckTool(currentTool))
    {
        if(toolBindFunType == INDIAN_MENISCUS_NETWORK_CHECK_E)
        {
            testResultInfo->setTestItemInfo("imei",DeviceContext::get()->getIMEIInfo());
            testResultInfo->setTestItemInfo("macid",DeviceContext::get()->getMACIdInfo());
            testResultInfo->setTestItemInfo("rsn",DeviceContext::get()->getRSNInfo());
        }
        testResultInfo->setTestItemInfo("testContent",testResultInfo->getTestContent());

    }
    else if(ToolKitsManager::get()->isOneClickConfigTool(currentTool))
    {
        if(toolBindFunType == TEST_TOOL_BARCODE_CONFIG_E)
        {
            testResultInfo->setTestItemInfo("testToolBarcode",DeviceContext::get()->getMaterialCode());
        }
    }
    testResultExtendInfo = testResultInfo->getTestItemInfo();
}

void TestManager::updateMesUploadDataInfoCtxSlot(const QString & info)
{
    testResultExtendInfo = info;
    //注意：MES上传成功后需要清除dataInfo
}

void TestManager::uploadToMesSlot()
{
    MesRequestData postData;
    postData.mesRequestType = DATA_TO_MES;
    postData.mesPortType = MES_UPLOAD_DATA;
    postData.deviceId = deviceid; //工具套

    postData.dataInfo = getTestResultExtendInfo();
    postData.result = getTestResult(); //获取最终结果
    postData.deviceSN = snCode;

    MesManager::get()->postData(postData);
}

void TestManager::checkV2Slot()
{
    QSharedPointer<DeviceMesWorker> mesClient(new DeviceMesWorker);
    mesClient->requestMesInfo(MES_GET_CHECK_V2);
}

void TestManager::handleTestPassedCheckResultSlot(bool mescheck,const QString & msg)
{
    emit closeTipSignal();
    if(mescheck == false)
    {
        QString code =TestManager::get()->getTestEquipmentCode();
        ToolFunctionType funType = ToolKitsManager::get()->getFunType(code);

        if(funType == SCHNEIDER_NETWORK_CHECK_E  || funType == UNKOWN_TOOL_FUNCTION_E)
        {
            return ;
        }
        else
        {
            //mes返回false，是否继续测试
            bool ret = tipCtxSignal(msg + QString("是否继续测试？"), MODAL, OKCANCEL, TIPS);

            emit slectTestStatusSignal(ret);    // SN错误
        }
    }
    else
    {
        if(needMistakeProofing())
        {
            emit tipCtxSignal("正在获取产品信息，请稍等", MODELESS, NO, TIPS);
            DeviceMesWorker mesClient;
            mesClient.getProductInfoSlot();
        }

        emit slectTestStatusSignal(true);    // SN正确
    }
}

void TestManager::handleEquipmentsInfoFromMesSlot(bool ret, const QString & ctx, const QJsonObject & info)
{
    qDebug() << "equipments from mes info:" << ret << ctx << info;
}
void TestManager::handleProductInfoSlot(bool ret, const QString & ctx, const QJsonObject & data)
{
    emit closeTipSignal();
    if(ret == false)
    {
       emit tipCtxSignal(ctx, MODAL, OK, TIPS);
    }
    else
    {
        //获取到产品信息
        mistakeProofingData.QRCode= data.value("qrcode").toString();
        qDebug() << "product : " << data;
    }
    emit productInfoGetFinished(ret);
}
void TestManager::setToolBindFun(ToolFunctionType type)
{
    if(type < UNKOWN_TOOL_FUNCTION_E)
    {
        toolBindFunType = type;
    }
}
void TestManager::setSnCode(const QString &sn)
{
    snCode = sn;
    DeviceContext::get()->setSN(sn);
}

bool TestManager::needSelectSerialPort(const QString & tool)
{
    if(toolBindFunType == LIQUID_COOL_BOARD_E ||
       toolBindFunType == DC_CONTROL_BOTTOM_BOARD_E ||
       toolBindFunType == AC_METER_CTRL_BOARD_E ||
       toolBindFunType == DC_TOP_MAIN_BOARD_E ||
       toolBindFunType == PDU_CONTROL_BOARD_E ||
       toolBindFunType == LIQUID_COOL_BOARD_E ||
       toolBindFunType == IONCHI_CCU_BOARD_E ||
       toolBindFunType == XGB_CCU_BOARD_E ||
       toolBindFunType == US_WAN_YUE_CORE_BOARD_E ||
       toolBindFunType == SCHNEIDER_NEW_RESI_CTRL_BOARD_E)
    {
        return true;
    }
    if(ToolKitsManager::get()->isWebConfigTool(tool))
    {
        return true;
    }
    return  serialPortSelector.getToolSerialCheckResult(tool);;
}

bool TestManager::needSelectSerialPort(ToolFunctionType type)
{
    auto iter = skipSelectSerialPortCtx.find(type);
    if(iter != skipSelectSerialPortCtx.end())
    {
        return iter.value();
    }
    return false;
}

bool TestManager::needLoginMesFirst()const
{
    //产线区分:
    UserType userType = testPolicy->getUser();
    switch(userType)
    {
        case AC_Line_Tester:
        return true;
        default:
        return false;
    }
    return true;
}
void TestManager::setDeviceLinkConditionStatus(bool st)
{
    linkCoditionStatus = st;
    if(st)
    {
        emit deviceLinkConditionOkSignal();
    }
}
bool TestManager::hasDeviceLinkCondition() const
{
    return linkCoditionStatus;
}
bool TestManager::needAutoLinkDevice()const
{
    UserType userType = testPolicy->getUser();
    switch(userType)
    {
        case AC_Line_Tester:
        return true;
        default:
        return true;
    }
    return true;
}
void TestManager::setLinkDeviceType(LinkDeviceType type)
{
    linkType = type;
}
LinkDeviceType TestManager::getLinkDeviceType()const
{
    //以设置的优先。
    if(linkType != MAX_LINK_DEVICE_TYPE)
    {
        return linkType;
    }
    //增加判定
    //用户维度。
    //工具维度。
    //todo 初始化就固定下来。
    QString eqiupmentCode;
    ToolFunctionType funType;
    eqiupmentCode =TestManager::get()->getTestEquipmentCode();
    funType = ToolKitsManager::get()->getFunType(eqiupmentCode);
    qDebug()<<"TestManager::getLinkDeviceType--currenttool"<<currentTool<<"funtype--"<<funType;
    if(ToolKitsManager::get()->isMassEnergyStoreTool(currentTool))
    {
        return ETHERNET_LINK;
    }
    else if(ToolKitsManager::get()->isSingleBoardCheckTool(currentTool))
    {
        if(funType == LIQUID_COOL_BOARD_E ||
           funType == DC_CONTROL_BOTTOM_BOARD_E ||
           funType == DC_TOP_MAIN_BOARD_E ||
           funType == IONCHI_CCU_BOARD_E ||
           funType == XGB_CCU_BOARD_E ||
           funType == MCC_BOARD_CHECK_E)
        {
            return ETHERNET_LINK;
        }
    }
    UserType userType = testPolicy->getUser();
    qDebug() << "userType: " <<userType;
    switch(userType)
    {
        case AC_Line_Tester:
        return SSH_LINKE;
        case FCT_Line_Tester:
        return SERIAL_PORT_LINK;
        case DC_Line_Tester:
        return SSH_LINKE;
        default:
        return SERIAL_PORT_LINK;
    }
    return SERIAL_PORT_LINK;

}

void TestManager::recordTestInfoSlot(const QString & object,const QString & info,const QString & aux,int TestType)
{
    //0 开始，-1结束;1:其他中间信息
    if(TestType == 1)
    {
        testInformation->setTestInfo(object,info,aux);
    }
    else if (TestType == 0)
    {
        testInformation->setTestObjectInfo(object);
        testInformation->setStartInfo(info);
    }
    else if (TestType == -1)
    {
        testInformation->setEndInfo(info.toInt(),aux);
    }
}
void TestManager::addTestResultInfo(const TestFinishContext & testCtx)
{
        testResultList.push_back(testCtx);
}
void TestManager::setMultiDevicesParallelModel(bool isMulti)
{
    isParallelMode = isMulti;
}
