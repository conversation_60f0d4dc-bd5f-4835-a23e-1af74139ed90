﻿#ifndef TESTMANAGER_H
#define TESTMANAGER_H

#include <QObject>
#include <QMap>
#include <QMultiMap>
#include <QList>
#include <QTimer>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include "interface_test_policy.h"
#include "workers/flow_test_worker.h"
#include "card_config/card_config_worker.h"
#include "controller/tip_controller.h"
#include "serial_port_selector.h"
#include "mistake_proofing/mistake_proofing_worker.h"
#include "toolkits_manager.h"
#include "test_result.h"
#include "test_manage/test_info.h"
class BusinessManger;
class TestContext
{
public:
    TestContext();
    ~TestContext();
    void initFunctions(const QString & material,QStringList & funs);
    void addFuntionBack(QString & functionName);
    void deleteFunction(QString & functionName);
public:
    QString materialCode;
    QList<QString> functions;
};
typedef struct
{
    QString materialCode;
    QString testFuntion;
    int testResult;
    bool hasTested;
}FuctionTestResult;


//用于管理测试顺序，物料绑定功能,测试前后的处理，比如上报
class TestManager : public QObject
{
    Q_OBJECT
public:
    static TestManager* get()
    {
        if(instance == nullptr)
        {
            instance = new TestManager;
        }
        return instance;
    }
public:
    void setTipController(TipController *);
    void addTestWorker(TestWorker* w,bool needConnect=true);
    void updateCrossMistakeProofing(ToolFunctionType type,bool needCrossProofing);//todo重载这个函数
    void updateSelectWorkerCtx(ToolFunctionType type,bool skipSelect=true);//todo重载这个函数
    void updateSelectSerialPortCtx(ToolFunctionType type,bool skipSelect=false);//todo重载这个函数

    //运行控制相关
    bool needLoginMesFirst()const;
    bool needAutoLinkDevice()const;
    LinkDeviceType getLinkDeviceType()const;
    void setLinkDeviceType(LinkDeviceType type);

    void setSnCode(const QString &sn);
    void setToolBindFun(ToolFunctionType type);
    void setUser(int user);
    void setToolsSelectEnable(bool enable);
    void setRootModel(bool enable);
    void setTestEquipmentCode(const QString & e) { testEquipmentCode = e;};

    UserType getUser()const;
    QString getTestEquipmentCode(){return testEquipmentCode;}
    bool isRootModel() { return rootModelEnable;}
    bool isToolsSelectEnable()const {return toolsSelectEnable;}

    void setErrorReason(const QString & resaon);
    QString getErrorReason()const;

    void setDeviceLinkConditionStatus(bool st);
    bool hasDeviceLinkCondition()const;

    void setMesDepend(bool);
    bool getMesDepend()const;

    void setMesDeviceIdModifyEnable(bool);
    bool isMesDeviceModifyEnable()const;
signals:
    void startTestSignal(const QString &);
    void finishedTest(const QString &,int result);
    void switchTest(QString & testName);
    void recordTestSignal(const QString &);
    void clearSnCodeSignal();
    void updateRootModelSignal(bool);
    void updateTestItemEnableSignal(int);

    bool tipCtxSignal(QString tips, MSG_MODE mode, MSG_TYPE ntype, MSG_TIP_TYPE ntiptype);
    void closeTipSignal();

    void finishedResult(bool result);
    void productInfoGetFinished(bool);
    void sendMistakeProofingData(MistakeProofingData &data);

    void updateToolSelectEnableSignal(bool);
    void snInputFinishedSignal(const QString &);
    void materialCodeInputFinishedSignal(const QString &);

    void deviceLinkConditionOkSignal();
    void updateTestStatusSignal(bool);
    //TODO:后续需要去掉的
    //单板的
    void retestSignal();
    void sendTestObjectContext(TipContext &);
    void materialInfoSignal(QStringList &);

    void mesDependSignal(bool st);

    //确认继续进行测试
    void slectTestStatusSignal(bool );

    void mesUploadInfoSignal(const QString & sn,int ,const QString &);
public slots:
    void startTestSlot(QString &);
    //QT对槽重载不友好。所以新增triggerTestSlot;
    void triggerTestSlot(const QString & fun,const QStringList & ctx);

    void startRecordLocalReport(QString &);
    void buildTestCtx(const QString & material,QStringList & funs);
    void updateTestResultSlot(QString & materialCode,QString & testFuctionName, int &);
    void resetTestResultSlot();

    void buildTestPolicy(UserType userType);
    void handleTestPassedCheckResultSlot(bool mescheck,const QString& msg);

    void handleProductInfoSlot(bool ret, const QString & ctx, const QJsonObject & data);
    //MES相关
    void uploadToMesSlot();
    void updateMesUploadDataInfoCtxSlot(const QString & info);
    void recvToolTestResultSlot(QString & tool,bool ret);

    void handleSerialPortCheckResult(QString, bool, QString);

    void updateCurrentToolName(const QString &,bool);
    //for SN 处理
    void handleSNSlot(QString & sn);
    void processSNSlot(const QString & sn);//与handleSNSlot合并。
    //物料处理
    void processMaterialSlot(const QString & code);
    //for MES
    void updateMesCtxSlot(const QString & deviceId,const QString  &accout,const QString &password);
    void appendTestItemResultSlot(QJsonObject &);
    void clearTestItemResultSlot();

    //for test 信息的手机
    void recordTestInfoSlot(const QString & object,const QString & info,const QString & aux="",int TestType = 1);

    //多设备并行测试管理
    void addTestResultInfo(const TestFinishContext & testCtx);
    void setMultiDevicesParallelModel(bool isMulti=true);
    void upLoadMultiDevices();

private slots:
    void handleEquipmentsInfoFromMesSlot(bool ret, const QString &, const QJsonObject & info);
    //处理通用的提示信号
    void handleTipSlot(const QString &,int process,bool ret = true, int displayControl = 0x200|0x8|0x1);

private:
    bool needCheckTestPassed(const QString & sn);
    bool needMistakeProofing();
    void selectTestWorker(QString & tool);
    void selectSerialPort(QString & tool);
    bool needSelectSerialPort(const QString & tool);
    bool needSelectSerialPort(ToolFunctionType type);
    bool processSwitchTest(QString & tool);//return ture：need switch,false，not need
    void buildFactoryPolicy();
    void getEquipmentsInfoFromMes();
    //MES数据
    void checkV2Slot();
    QString getTestResult();
    QString getTestResultExtendInfo();
    void handleTestResultPreProcess();
private:
    QMap<QString,TestContext> testCtx;
    QMultiMap<QString,FuctionTestResult> testResult;
    ITestPolicy * testPolicy;
    LinkDeviceType linkType;
    bool linkCoditionStatus;
private:
    //mes相关字段。
    QString deviceid;
    QString snCode;
    QString testResultExtendInfo;
    QString pinCode;
private:
    bool isParallelMode = false;//多个设备同时，主要用于上报的时候处理。
private:
    FlowTestWorker * testWorker;
    TipController * tipController;
    SerialPortSelector serialPortSelector;
    bool hasBuilderMistakeProofing;

    QString errorReason;
    bool mesDepend;
    bool mesIdModify {false};
private:
    MistakeProofingData mistakeProofingData;
    QString currentTool;
    //root model
    bool rootModelEnable;
    QString testEquipmentCode;
    ToolFunctionType toolBindFunType;
    bool toolsSelectEnable;
    QMap<ToolFunctionType,bool>crossCheckProofing;
    QMap<ToolFunctionType,bool>skipSelectTestWorkerCtx;
    QMap<ToolFunctionType,bool>skipSelectSerialPortCtx;

private:
    BusinessManger * businessManager;
    TestResult *testResultInfo;
    TestInformation * testInformation;
    QList<TestFinishContext> testResultList;
private:
    static TestManager *instance;
private:
    explicit TestManager(QObject *parent = nullptr);
    ~TestManager();
};

#endif // TESTMANAGER_H
