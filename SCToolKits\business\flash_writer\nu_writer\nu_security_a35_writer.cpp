#include <QResource>
#include <QTemporaryFile>
#include <QDir>
#include <QDebug>
#include <QProcess>
#include "nu_security_a35_writer.h"
#include <QSharedPointer>
#include "link_manager.h"
#include "characters/character_format.h"
NuSecurityA35Writer::NuSecurityA35Writer()
{
    authData.resize(16);
    //todo value可配值
    quint32 value {0xAABBCCDD};
    for(auto i = 0;i<authData.size();)
    {
        authData[i++] = (value>>24) &0XFF;
        authData[i++] = (value>>16) &0XFF;
        authData[i++] = (value>>8) &0XFF;
        authData[i++] = value & 0XFF;
    }
}
void NuSecurityA35Writer::setValue(quint32 value)
{
    authData.resize(16);
    //todo value可配值
    for(auto i = 0;i<authData.size();)
    {
        authData[i++] = (value>>24) &0XFF;
        authData[i++] = (value>>16) &0XFF;
        authData[i++] = (value>>8) &0XFF;
        authData[i++] = value & 0XFF;
    }
}
void NuSecurityA35Writer::processIdentityAuthResult(int exitCode,QProcess::ExitStatus status)
{
    QProcess * process = dynamic_cast<QProcess*>(sender());
    if(process)
    {
       qDebug()<<"auth ptyhon="<<QString::fromUtf8(process->readAllStandardOutput());
    }

    if(QProcess::NormalExit == status)
    {
        if(exitCode==1)
        {
            isAuthAble=true;
            qDebug()<< "user has auth";
        }
        else
        {
            qDebug() << "user has not auth";
        }
    }
    else
    {
        isAuthAble=false;
        qDebug()<< "expecet exit,user has not auth";
    }
    if(isAuthAble)
    {
        emit displayInfoSignal(tr("身份正常"));
    }
    else
    {
       emit displayInfoSignal(CharacterFormat::add(tr("无烧录权限"),CharacterColor::RED_E));
    }
    emit identityAuthResultSignal(isAuthAble);
}
void NuSecurityA35Writer::startIdentityAuth()
{
    // 从资源中读取 Python 脚本
    QResource scriptResource(":/scripts/3rdparty/utilities/nu_writer/load_auth.py");
    if (!scriptResource.isValid())
    {
        qDebug() << "Script resource not found!";
        return ;
    }
    //删除可能被处理的临时文件
    bool ret = QFile::exists(QDir::tempPath() + "/youcai.py");
    if(ret)
    {
        QFile::remove(QDir::tempPath() + "/youcai.py");
    }
    // 创建临时文件
    QTemporaryFile tempFile(QDir::tempPath() + "/youcai.py");
    if (!tempFile.open())
    {
        qDebug() << "Failed to create temporary file";
        return ;
    }

    // 写入脚本内容
    tempFile.write(reinterpret_cast<const char*>(scriptResource.data()), scriptResource.size());
    tempFile.close();

    // 设置 Python 命令 (根据系统调整)
//    QString pythonCmd = QDir::currentPath() +"\\Python39-32\\python.exe"; // Windows/Linux
    QString pythonCmd = QDir::currentPath() +"/wsr/Python39-32/python.exe";
//    QString pythonCmd = "python.exe"; // Windows/Linux

    // 执行 Python 脚本
    QSharedPointer<QProcess> process(new QProcess);
    connect(process.get(),QOverload<int,QProcess::ExitStatus>::of(&QProcess::finished),this,&NuSecurityA35Writer::processIdentityAuthResult);
    process->start(pythonCmd, QStringList() << tempFile.fileName());

    if (!process->waitForStarted())
    {
        qDebug() << "Failed to start Python process";
        return ;
    }
    //最大等待10分钟。
    ret = process->waitForFinished(2*60*1000);
    qDebug()<< "wait process finish"<< ret;
    if(ret == false)
    {
        isAuthAble = false;
        qDebug()<<"timeout finished auth is false";
        emit displayInfoSignal(CharacterFormat::add(tr("无法鉴权,无烧录权限"),CharacterColor::RED_E));
        emit identityAuthResultSignal(isAuthAble);
    }

    tempFile.remove();
    return;
}
void NuSecurityA35Writer::startWriter()
{
    startWritePattern();
}
void NuSecurityA35Writer::processComRespondInfo(QByteArray & msg)
{
    respondInfo.append(msg);
    qDebug()<< "respondInfo="<<msg;
    if(respondInfo.contains("usbd boot with signature"))
    {
        emit testResultSignal(-2,"");
        writePatternTimer->stop();
        respondInfo.clear();
        isEnalbeWritePattern = true;
        emit displayInfoSignal(CharacterFormat::add(tr("进入attach阶段"),CharacterColor::GREEN_E));
        //进入attach和烧录阶段。
        QTimer::singleShot(1000,this,[this](){
            doAttachLink();
        });
    }
    else if(respondInfo.size() > 256)
    {
        respondInfo.clear();
        if(isEnalbeWritePattern == false && isPausReceivMsg == false)
        {
            emit displayInfoSignal(CharacterFormat::add(tr("无法进入烧录状态，请先断电后再试"),CharacterColor::GREEN_E));
            emit testResultSignal(-1,"");
        }
    }
    else if (respondInfo.contains("header1 CRC OK"))
    {
        respondInfo.clear();
        if(isEnalbeWritePattern == false && isPausReceivMsg == false)
        {
            emit displayInfoSignal(CharacterFormat::add(tr("无法进入烧录状态，请先断电后再试"),CharacterColor::GREEN_E));
            emit testResultSignal(-1,"");
        }
    }
}
void NuSecurityA35Writer::startWritePattern()
{
    if(writePatternTimer == nullptr)
    {
        writePatternTimer = new QTimer();
        connect(writePatternTimer,&QTimer::timeout,this,[this](){
            emit comPortDataSignal(authData);

        });
    }
    isPausReceivMsg=false;
    SerialWorker * serialWorker = LinkManager::get()->getSerail(DeviceContext::get()->getSerialPortName());
    if(serialWorker)
    {
        serialWorker->setSerialMsgType(BYTE_ONE_BY_ONE_MSG);
        qRegisterMetaType<QByteArray>("QByteArray&");
        connect(this,&NuSecurityA35Writer::comPortDataSignal,serialWorker,&SerialWorker::sendDataSlot, Qt::UniqueConnection);
        connect(serialWorker,&SerialWorker::transformNetMsg,this,&NuSecurityA35Writer::processComRespondInfo,Qt::UniqueConnection);

        emit comPortDataSignal(authData);
        writePatternTimer->start(6);
    }
    else
    {
        emit testResultSignal(-1,"");
        qDebug()<< "not link comport";
    }
}

void NuSecurityA35Writer::doAttachLink()
{
    // 从资源中读取 Python 脚本
    qDebug()<< "do attach start";
    QString wirter = QDir::currentPath() + "/wsr/nu_writer/nuwriter.py";
    if(!QFile::exists(wirter))
    {
        qDebug()<< "not found nuwriter";
        emit testResultSignal(-1,"");
        return;
    }

    // 设置 Python 命令 (根据系统调整)
    QString pythonCmd = QDir::currentPath() +"/wsr/Python39-32/python.exe";

    // 执行 Python 脚本
    QStringList cmd ;
    cmd << wirter;
    cmd << "-a";
    cmd <<QDir::currentPath() + "/wsr/nu_writer/enc_ddr3_winbond_256mb.bin";
    QSharedPointer<QProcess> process(new QProcess);
    process->setWorkingDirectory(QDir::currentPath() +"/wsr/Python39-32/");
    connect(process.get(),QOverload<int,QProcess::ExitStatus>::of(&QProcess::finished),this,&NuSecurityA35Writer::processAttachReuslt);
    process->start(pythonCmd, cmd);
    for(auto &i:cmd)
    {
        qDebug()<< i;

    }
    if (!process->waitForStarted())
    {
        qDebug() << "Failed to start Python process";
        emit testResultSignal(-1,"");
        return ;
    }
    //最大等待2分钟。
    bool ret = process->waitForFinished(2*60*1000);
    qDebug()<< "wait process finish"<< ret;
    if(ret == false)
    {
        emit testResultSignal(-1,"");
        qDebug()<<"timeout finished ,do attach failed";
    }
    return;
}
void NuSecurityA35Writer::processAttachReuslt(int exitCode,QProcess::ExitStatus status)
{
    QProcess * process = dynamic_cast<QProcess*>(sender());
    if(process)
    {
       qDebug()<<"attach ptyhon="<<QString::fromUtf8(process->readAllStandardOutput());
    }

    if(QProcess::NormalExit == status)
    {
        if(exitCode==0)
        {
            qDebug()<<"do attach success";
            emit displayInfoSignal(CharacterFormat::add(tr("烧录中，预期80秒……"),CharacterColor::GREEN_E));
            isAttachSuccess = true;
            QTimer::singleShot(1000,this,[this](){
                writeFirmware();
            });
        }
        else
        {
            emit displayInfoSignal(CharacterFormat::add(tr("无法进入烧录，请断电重试。注意接线正常"),CharacterColor::RED_E));
            qDebug() << "do attach failed"<<exitCode;
            resetEnv();
            emit testResultSignal(-1,"");
        }
    }
    else
    {
        emit displayInfoSignal(CharacterFormat::add(tr("无法进入烧录，请断电重试。"),CharacterColor::RED_E));
        qDebug()<< "expecet exit,attach failed";
        resetEnv();
        emit testResultSignal(-1,"");
    }

}
void NuSecurityA35Writer::writeFirmware()
{
    qDebug()<< "do start write";
    QString wirter = QDir::currentPath() + "/wsr/nu_writer/nuwriter.py";
    if(!QFile::exists(wirter))
    {
        qDebug()<< "not found nuwriter";
        return;
    }
    // 设置 Python 命令 (根据系统调整)
    QString pythonCmd = QDir::currentPath() +"/wsr/Python39-32/python.exe"; // Windows/Linux

    // 执行 Python 脚本
    QStringList cmd ;
    cmd << wirter;
    cmd << "-w";
    cmd << "emmc";
    cmd <<DeviceContext::get()->getCoreBoardFirmware();

    QSharedPointer<QProcess> process(new QProcess);
    process->setWorkingDirectory(QDir::currentPath() +"/wsr/Python39-32/");
    connect(process.get(),QOverload<int,QProcess::ExitStatus>::of(&QProcess::finished),this,&NuSecurityA35Writer::processWriteFirmwareResult);
    process->start(pythonCmd, cmd);
    for(auto &i:cmd)
    {
        qDebug()<< i;
    }
    //10分钟烧录
    bool ret = process->waitForFinished(10*60*1000);
    qDebug()<< "wait process finish"<< ret;
    if(ret == false)
    {
        emit displayInfoSignal(CharacterFormat::add(tr("烧录失败，未知原因，请断电重试"),CharacterColor::RED_E));
        qDebug()<<"timeout finished ,do attach failed";
        emit testResultSignal(-1,"");
    }

    return;
}
void NuSecurityA35Writer::processWriteFirmwareResult(int exitCode,QProcess::ExitStatus status)
{
    QProcess * process = dynamic_cast<QProcess*>(sender());
    if(process)
    {
        qDebug()<<"write python="<<QString::fromUtf8(process->readAllStandardOutput());
    }
    if(QProcess::NormalExit == status)
    {
        if(exitCode==0)
        {
            emit displayInfoSignal(CharacterFormat::add(tr("烧录成功，请断电重启设备"),CharacterColor::GREEN_E));
            testResultSignal(true,"");
            qDebug()<<"write firmware success";
        }
        else
        {
            emit displayInfoSignal(CharacterFormat::add(tr("烧录失败，请断电重启设备重试"),CharacterColor::RED_E));
            qDebug() << "write firmware failed"<< exitCode;
            testResultSignal(false,"");
        }
    }
    else
    {
        emit displayInfoSignal(CharacterFormat::add(tr("烧录异常，请断电重启设备重试"),CharacterColor::RED_E));
        qDebug()<< "expecet exit,write firmware";
        emit testResultSignal(-1,"");
    }
    resetEnv();
}
void NuSecurityA35Writer::resetEnv()
{
    isEnalbeWritePattern=false;
    isPausReceivMsg = true;
}
