#ifndef NUSECURITYA35WRITER_H
#define NUSECURITYA35WRITER_H

#include <QObject>
#include <QTimer>
#include <QProcess>
class NuSecurityA35Writer:public QObject
{
    Q_OBJECT
public:
    NuSecurityA35Writer();
    void setValue(quint32 configValue);
signals:
    void identityAuthResultSignal(bool);
    void comPortDataSignal(const QByteArray &);
    //todo：为啥不继承testWorker???
    void displayInfoSignal(const QString &);
    void testResultSignal(int ret,const QString & msg);
public slots:
    void startIdentityAuth();
    void startWriter();
    void writeFirmware();
private slots:
    void processIdentityAuthResult(int exitCode,QProcess::ExitStatus status);
    void startWritePattern();
    void processComRespondInfo(QByteArray & msg);
    void doAttachLink();
    void processAttachReuslt(int exitCode,QProcess::ExitStatus status);

    void processWriteFirmwareResult(int exitCode,QProcess::ExitStatus status);
    void resetEnv();
private:
    bool isAuthAble {false};
    QProcess * process {nullptr};
    QTimer * writePatternTimer {nullptr};
    QByteArray respondInfo;
    bool isEnalbeWritePattern {false};
    bool isAttachSuccess {false};
    bool isPausReceivMsg {false};
    QByteArray authData;

};

#endif // NUSECURITYA35WRITER_H
