#include "forth_g_check.h"
#include "builders/ftp_test_object_builder.h"
#include "test_objects/test_object.h"
#include "ssh/ftp_client.h"
#include "builders/ssh_operate_builder.h"
#include "workers/ssh_test_worker.h"
#include "link_manager.h"

#include <QCoreApplication>
//有一个问题，不是内部使用信号和槽。
class ForthGCheckImp : public TestWorker
{
public:
    ForthGCheckImp(ForthGCheck* ptr) : builder(nullptr),
        sshBuilder(nullptr),
        ftpUploader(nullptr),
        sshWoker(nullptr),
        forthGCheck(ptr),
        testObjectIndex(0),testOperateIndex(0)
    {

    }
    ~ForthGCheckImp()
    {
        if(builder)
        {
            delete builder;
            builder = nullptr;
        }
        if(sshBuilder)
        {
            delete sshBuilder;
            sshBuilder = nullptr;
        }
        if(ftpUploader)
        {
            delete ftpUploader;
            ftpUploader = nullptr;
        }
        if(sshWoker)
        {
            delete sshWoker;
            sshWoker = nullptr;
        }
    }
public:
    void start();
public slots:
    void handleDownloadSlot(QString);
private:
    void processCheck(int objectIndex,int operateIndex);
    int processReusult(const QString &);
    bool processMsgResult(TestObject *, const QString &);
private:
    void updateTestCtx(TestObject*,int,int);
    void packageRSNInfo();
    void initRSNInfo();
    bool isIMEICode(const QString& );
    bool isMacAddress(const QString& );
    void handleTestResult(SSHClient* ,const QString& ,const QString& ,int);
private:
    friend ForthGCheck;
    FTPTestObjectBuilder * builder;
    SSHOperateBuilder * sshBuilder;
    SecureFileUploader *ftpUploader;
    SSHTestWorker * sshWoker;
    ForthGCheck * forthGCheck;
    TestObject *testObject;
    int testObjectIndex;
    int testOperateIndex;

    int cnt;
    QString imei;
    QString macId;
    QString rsn;
    QMap<int,QString> monthMap;
    QMap<int,QString> yearMap;
};
void ForthGCheckImp::updateTestCtx(TestObject *object, int objectIndex, int operateIndex)
{
    testObject = object;
    testObjectIndex = objectIndex;
    testOperateIndex = operateIndex;
}
void ForthGCheckImp::packageRSNInfo()
{
    QString sn = DeviceContext::get()->getSN();
    QString monthCode = monthMap[sn.mid(4,2).toInt()];
    QString yearCode = yearMap[sn.mid(2,2).toInt()];
    rsn = "RWACA"+monthCode+yearCode+sn.mid(4);
    qDebug()<<"rsn:"<<rsn;
}
void ForthGCheckImp::initRSNInfo()
{
    for (int i = 1; i <= 12; ++i) {
        char letter = 'A' + (i - 1);
        monthMap.insert(i, QString(letter));
    }
    for (int i = 25; i <= 35; ++i) {
        char letter = 'L' + (i - 25);
        yearMap.insert(i, QString(letter));
    }
}

bool ForthGCheckImp::isIMEICode(const QString& input)
{
    QRegularExpression regex("^\\d{15}$");
    return regex.match(input).hasMatch();
}

bool ForthGCheckImp::isMacAddress(const QString& input)
{
    QRegularExpression regex("^([0-9A-Fa-f]{2}:){5}[0-9A-Fa-f]{2}$");
    return regex.match(input).hasMatch();
}

void ForthGCheckImp::handleTestResult(SSHClient* sshclient,const QString& processMessage,const QString& resultMessage,int result)
{
    emit forthGCheck->checkProcessSignal(processMessage);
    emit forthGCheck->checkResultSignal(resultMessage);
    emit forthGCheck->finishedTestSiganl("TEST_4G",result);
    deleteTestOjbects();
    imei = "";
    macId = "";
    rsn = "";
    if(sshclient)
    {
       bool dis = disconnect(sshclient,&SSHClient::sigDataArrived,sshWoker,&SSHTestWorker::recvSSHDataSlot);
       qDebug()<< "dis ssh ret "<< dis;
    }
    disconnect(ftpUploader,&SecureFileUploader::updateDownloadProcessResult,this,&ForthGCheckImp::handleDownloadSlot);
}
int ForthGCheckImp::processReusult(const QString &ctx)
{
    //TODO:
    testObject = getTestObject(testObjectIndex);
    if(testObject == nullptr)
    {
        return 1;
    }

    SSHClient * sshclient =  LinkManager::get()->getSSH();
    //处理结果
    bool isSuccess = processMsgResult(testObject, ctx);
    if(!isSuccess)
    {
        handleTestResult(sshclient, "4G通信异常，请排查SIM卡或天线位置等问题","4G测试失败",0);
        return 0;
    }
    else
    {
        int operateIndex = testOperateIndex + 1;
        TestOperate * testOperate = nullptr;
        testObject->getTestOperate(operateIndex,&testOperate);
        if(testOperate)
        {
            processCheck(testObjectIndex,operateIndex);
            return 0;
        }
        else
        {
            int objectIndex = testObjectIndex +1;
            TestObject * testObject =  getTestObject(objectIndex);
            if(testObject == nullptr)
            {
                qDebug()<< "4g check over";
                updateTestCtx(nullptr,0,0);

                if(forthGCheck->isNeedUploadInfo)
                {
                    if(imei == "" || macId == "")
                    {
                        handleTestResult(sshclient, "IMEI或MAC地址为空，请排查问题","4G测试失败",0);
                        return 0;
                    }
                    else if(!isIMEICode(imei))
                    {
                        handleTestResult(sshclient, "IMEI获取失败，请排查问题","4G测试失败",0);
                        return 0;
                    }
                    else if(!isMacAddress(macId))
                    {
                        handleTestResult(sshclient, "MAC地址获取失败，请排查问题","4G测试失败",0);
                        return 0;
                    }
                    DeviceContext::get()->setIMEIInfo(imei);
                    DeviceContext::get()->setMACIdInfo(macId);
                    initRSNInfo();
                    packageRSNInfo();
                    DeviceContext::get()->setRSNInfo(rsn);
                }
                QJsonObject itemResult;
                itemResult["itemName"] = "TEST_4G";
                itemResult["itemResult"] = "OK";
                emit forthGCheck->appendTestItemResultSignal(itemResult);
                handleTestResult(sshclient, "4G通信正常","4G测试通过",1);
                return 1;
            }
            else
            {
                processCheck(objectIndex,0);
                return 0;
            }
        }
    }
}
bool ForthGCheckImp::processMsgResult(TestObject *testObject, const QString &ctx)
{
    TestOperate *testOperate = nullptr;
    testObject->getTestOperate(testOperateIndex, &testOperate);

    if(testOperate == nullptr)
    {
        return 0;
    }
    else if(testOperate->getOperateCode() == FTP_TASK)
    {
        if(!ctx.contains("OK"))
        {
            return 0;
        }
    }
    else if (testOperate->getOperateCode() == SSH_TASK)
    {
        QStringList dataCtx;
        testOperate->getStringData(dataCtx);

        if(!ctx.contains(dataCtx[1]))
        {
            return 0;
        }
        if(testObject->getName() == "IMEI码获取")
        {
            imei = ctx;
            qDebug()<<"ForthGCheckImp IMEI:"<<imei;
        }
        else if(testObject->getName() == "MAC地址获取")
        {
            macId = ctx;
            qDebug()<<"ForthGCheckImp MACID:"<<macId;
        }
    }

    return 1;
}
void ForthGCheckImp::start()
{
    emit forthGCheck->checkProcessSignal("正在检测4G通信是否正常，请稍等……");

    processCheck(testObjectIndex,testOperateIndex);
}

void ForthGCheckImp::handleDownloadSlot(QString tip)
{
    if(tip.contains("error"))
    {
        SSHClient * sshclient =  LinkManager::get()->getSSH();
        handleTestResult(sshclient, "上传脚本失败","4G测试失败",0);
    }
}
void ForthGCheckImp::processCheck(int objectIndex,int operateIndex)
{
    //多个worker的组合工作。
    // ftpUploader,sshWorker都是其中的一个worker
   TestObject * testObject =  getTestObject(objectIndex);
   if(testObject == nullptr)
   {
       qDebug()<< "not found test obbject for index 0";
       return;
   }
   TestOperate * testOperate = nullptr;
   testObject->getTestOperate(operateIndex,&testOperate);
   if(testOperate == nullptr)
   {
       qDebug()<< "not found testOperate for index 0";
       return ;
   }

   updateTestCtx(testObject,objectIndex,operateIndex);
   if(FTP_TASK == testOperate->getOperateCode())
   {
       QStringList ftpCtx;
       testOperate->getStringData(ftpCtx);

       QString src = ftpCtx[0];
       QString dst = ftpCtx[1];
       ftpUploader->upload(src, dst, DeviceContext::get()->getDeviceLinkIP(),
                                  DeviceContext::get()->getSSHLoginUser(),
                                  DeviceContext::get()->getSSHLoginPassword(),
                                  LinkManager::get()->getSshModel(),DeviceContext::get()->getSSHPrivateKey());
       }
   else if (SSH_TASK == testOperate->getOperateCode())
   {
       QStringList sshCtx;
       testOperate->getStringData(sshCtx);
       QString src = sshCtx[0];
       sshWoker->sendSSHDataSlot(src);
   }
}

ForthGCheck::ForthGCheck(QObject *parent) : forthGCheckImp(new ForthGCheckImp(this)),isNeedUploadInfo(false),hasLossRate(0)
{
    connect(this,&ForthGCheck::startBusinessSignal,this,&ForthGCheck::processBusiness,Qt::QueuedConnection);
}
ForthGCheck::~ForthGCheck()
{
    delete forthGCheckImp ;
}
void ForthGCheck::startBusiness(const QString &busniess)
{
    qDebug()<< "is forthgChck";
}

void ForthGCheck::setUploadInfoStatus(bool status)
{
    isNeedUploadInfo = status;
}

void ForthGCheck::setHasLossRate(int data)
{
    hasLossRate = data;
}
void ForthGCheck::processBusiness()
{
    qDebug()<< "start test 4g";
    deleteTestOjbects();
    //创建测试对象。
    if(forthGCheckImp->builder  == nullptr)
    {
        forthGCheckImp->builder = new FTPTestObjectBuilder(forthGCheckImp);

    }
    //TODO
    QString path = APPConfig::get()->getWsrDir();
    QString localFile =path+QString("/communication_script/4g_check.sh");
    qDebug()<< "ftp file "<< localFile;
    forthGCheckImp->builder->build(localFile,"/tmp");

    //创建ftp传输
    if(forthGCheckImp->ftpUploader == nullptr)
    {
        SecureFileUploader * ftpUploader = new SecureFileUploader;
        connect(ftpUploader,&SecureFileUploader::endDownloadSignal,this,&ForthGCheck::endDownloadSlot);
        forthGCheckImp->ftpUploader = ftpUploader;
    }
    connect(forthGCheckImp->ftpUploader,&SecureFileUploader::updateDownloadProcessResult,forthGCheckImp,&ForthGCheckImp::handleDownloadSlot);


    //build ssh 信令
    if(forthGCheckImp->sshBuilder == nullptr)
    {
        forthGCheckImp->sshBuilder = new SSHOperateBuilder(forthGCheckImp);

    }
    forthGCheckImp->sshBuilder->setTestWorker(forthGCheckImp);
    forthGCheckImp->sshBuilder->build(QString("sh /tmp/4g_check.sh %1").arg(hasLossRate),"4G Module ping over","4G检测");
    if(isNeedUploadInfo)
    {
        forthGCheckImp->sshBuilder->build("grep 'IMEI=' /run/network/ndis_status  | sed 's/.*IMEI=//'",
                                          "","IMEI码获取");
        forthGCheckImp->sshBuilder->build("ip link show eth0 | grep link/ether | awk '{print $2}'",
                                          "","MAC地址获取");
    }

    if(forthGCheckImp->sshWoker == nullptr)
    {
        forthGCheckImp->sshWoker = new SSHTestWorker();
    }

    SSHClient * sshclient =  LinkManager::get()->getSSH();
    if(sshclient)
    {
        connect(forthGCheckImp->sshWoker,&SSHTestWorker::sendDataSignal,sshclient,&SSHClient::sendData,Qt::UniqueConnection);
        connect(forthGCheckImp->sshWoker,&SSHTestWorker::respondDataSignal,this,&ForthGCheck::recvSSHRespond,Qt::UniqueConnection);

        connect(sshclient,&SSHClient::sigDataArrived,forthGCheckImp->sshWoker,&SSHTestWorker::recvSSHDataSlot,Qt::UniqueConnection);
    }
    //启动worker
    forthGCheckImp->start();
}
void ForthGCheck::startWorker(const QString )
{
//    processBusiness();
    emit startBusinessSignal();
}
void ForthGCheck::recvSSHRespond(const QString & msg)
{
    qDebug()<<"forth recv ssh msg"<<msg;
    forthGCheckImp->processReusult(msg);
}
void ForthGCheck::endDownloadSlot(int result,QString ctx)
{
    if(result)
    {
        qDebug()<< "is ok" <<ctx;
        forthGCheckImp->processReusult(ctx);
    }
}

void ForthGCheck::processConnectResult(bool ,QString ,int )
{
    forthGCheckImp->updateTestCtx(nullptr, 0, 0);

    SSHClient * sshclient =  LinkManager::get()->getSSH();
    if(sshclient)
    {
       bool dis = disconnect(sshclient,&SSHClient::sigDataArrived,forthGCheckImp->sshWoker,&SSHTestWorker::recvSSHDataSlot);
       qDebug()<< "dis ssh ret "<< dis;
    }
}
