#ifndef FORTHGCHECK_H
#define FORTHGCHECK_H

#include <QObject>
#include "workers/test_worker.h"
#include "common_share/interface_business.h"
#include <QRegularExpression>
#include <QJsonObject>
#include "common_base/database/sqlite_manager.h"
#include "app_config.h"
class ForthGCheckImp;
//QT 不允许多重继承，故无法使用virtual继承。
class ForthGCheck : public TestWorker,public IBusiness
{
    Q_OBJECT
public:
    explicit ForthGCheck(QObject *parent = nullptr);
    ~ForthGCheck();
    void startWorker(const QString toolName);
    void startBusiness(const QString &);
    void setUploadInfoStatus(bool);
    void setHasLossRate(int);
public slots:
    void endDownloadSlot(int result,QString ctx);
    void recvSSHRespond(const QString & msg);
    friend ForthGCheckImp;

    void processConnectResult(bool ,QString ,int );

signals:
    void finished(int ret);
    void startBusinessSignal();
    void checkProcessSignal(const QString &);
    void checkResultSignal(const QString &);
    void updateProcessBarSignal(int, int);
    void appendTestItemResultSignal(QJsonObject &);
private slots:
    void processBusiness();
private:
    ForthGCheckImp * forthGCheckImp;
    bool isNeedUploadInfo;
    int hasLossRate;
};

#endif // FORTHGCHECK_H
