#include "bluetooth_check.h"
#include "ssh/ftp_client.h"
#include "builders/ssh_operate_builder.h"
#include "workers/ssh_test_worker.h"
#include "link_manager.h"

BluetoothCheck::BluetoothCheck(QObject *) :
    testObjectIndex(0),
    testOperateIndex(0),
    builder(nullptr),
    processWorker(nullptr),
    isNeedBluetoothName(false)
{

}

BluetoothCheck::~BluetoothCheck()
{
    if(processWorker)
    {
        processThread.quit();
        processThread.wait();
        delete processWorker;
    }
}

void BluetoothCheck::parseDualNetworkRecvLog(const QString &log, QStringList &list)
{
    list = log.split(QRegExp("\\r\\n"), Qt::SkipEmptyParts);
}

size_t BluetoothCheck::saveCurlResponseToStdString(void *contents, size_t size, size_t nmemb, string *s)
{
    size_t newLength = size * nmemb;
    size_t oldLength = s->size();
    s->resize(oldLength + newLength);
    std::copy((char*)contents, (char*)contents+newLength, s->begin()+oldLength);
    return size * nmemb;
}

bool BluetoothCheck::getQrCodeBySn()
{
    CURL *curl = curl_easy_init();
    int ret = 0;

    if (curl)
    {
        std::string response;
        curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "POST");
        curl_easy_setopt(curl, CURLOPT_URL, "http://mes-local-service.wbstar.com/api/Device/AnalysisQRCodeByStarCharge");
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, saveCurlResponseToStdString);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
        curl_easy_setopt(curl, CURLOPT_VERBOSE, 0L);
        curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
        curl_easy_setopt(curl, CURLOPT_DEFAULT_PROTOCOL, "http");
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_easy_setopt(curl, CURLOPT_HEADER, true);
        curl_easy_setopt(curl, CURLOPT_TIMEOUT, 90);

        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type:application/json");
        headers = curl_slist_append(headers, "Accept:*/*");

        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        QString postData = QString("{\"CUSTOMER_QRCODE\":\"123\",\"SN\":\"%1\"}").arg(DeviceContext::get()->getSN());
        std::string uidBody = postData.toStdString();
        const char *uidBodyPtr = uidBody.c_str();
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, uidBodyPtr);
        CURLcode code = curl_easy_perform(curl);

        int info = 0; // status
        curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &info);
        qDebug() << "device name info:" << info;

        if (info == 200)
        {
            if (code == CURLE_OK)
            {
                QRegularExpression re("\"STAR_CHARGE_QRCODE\":\"(\\d+)\"");
                QRegularExpressionMatch match = re.match(QString::fromStdString(response));

                // 检查是否找到匹配
                if (match.hasMatch())
                {
                    bluetoothName = match.captured(1);
                    qDebug() << "STAR_CHARGE_QRCODE:" << bluetoothName;
                    qDebug()<<"qrcode is："<<bluetoothName;
                    processCheck(testObjectIndex, testOperateIndex);
                }
                else
                {
                    qDebug() << "没有找到匹配的内容。";
                    emit checkProcessSignal("蓝牙异常");
                    emit checkResultSignal("桩号获取失败");
                    emit finishedTestSiganl("TEST_BLE",0);
                }
            }
            else
            {
                qDebug() << "CURL error: " << curl_easy_strerror(code);
            }
        }
        else
        {
            qDebug() << "HTTP response code: " << info;
        }

        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
    }
    else
    {
        qDebug() << "Error initializing cURL";
    }

    return ret;
}

void BluetoothCheck::updateTestCtx(TestObject *object, int objectIndex, int operateIndex)
{
    testObject = object;
    testObjectIndex = objectIndex;
    testOperateIndex = operateIndex;
}

int BluetoothCheck::processReusult(const QString &)
{
    testObject = getTestObject(testObjectIndex);
    if(testObject == nullptr)
    {
        return 1;
    }
    TestOperate * testOperate = nullptr;
    testObject->getTestOperate(testOperateIndex,&testOperate);
    if(testOperate == nullptr)
    {
        return 1;
    }

    QString thirdToolDir = APPConfig::get()->getWsrDir();
    thirdToolDir.append("/bluesearch_tool/bletest_result.txt");
    QFile blefile;
    QTextStream blein;
    QString blefileContent;
    blefile.setFileName(thirdToolDir);
    if (!blefile.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        qWarning() << "无法打开文件：" << thirdToolDir<< "，错误：" << blefile.errorString();
    }
    blein.setDevice(&blefile);
    blefileContent = blein.readAll();

    // 检测是否包含特定字符串
    if (blefileContent.contains("successful"))
    {
        updateTestCtx(nullptr,0,0);
        QJsonObject itemResult;
        itemResult["itemName"] = "TEST_BLE";
        itemResult["itemResult"] = "OK";
        emit appendTestItemResultSignal(itemResult);
        emit checkProcessSignal("蓝牙正常");
        emit checkResultSignal("蓝牙测试通过");
        emit finishedTestSiganl("TEST_BLE",1);
        return 1;
    }
    else if (blefileContent.contains("fail"))
    {
        emit checkProcessSignal("蓝牙异常");
        emit checkResultSignal("蓝牙测试失败");
        emit finishedTestSiganl("TEST_BLE",0);
        return 0;
    }
    blefile.close();
    return 0;
}

void BluetoothCheck::start()
{
    emit checkProcessSignal("正在检测蓝牙是否正常，请稍等……");
    if(isNeedBluetoothName)
    {
        getQrCodeBySn();
    }
    else
    {
        processCheck(testObjectIndex, testOperateIndex);
    }

}

void BluetoothCheck::setIsNeedBlutoothName(bool ret)
{
    isNeedBluetoothName = ret;
}

void BluetoothCheck::processCheck(int objectIndex, int operateIndex)
{
    TestObject * testObject =  getTestObject(objectIndex);
    if(testObject == nullptr)
    {
        qDebug()<< "not found test object for index 0";
        return;
    }
    TestOperate * testOperate = nullptr;
    testObject->getTestOperate(operateIndex,&testOperate);
    if(testOperate == nullptr)
    {
        qDebug()<< "not found testOperate for index 0";
        return ;
    }

    updateTestCtx(testObject,objectIndex,operateIndex);
    if(testOperate->code == JLINK_CMD_TASK)
    {
        QStringList programData;

        testOperate->getStringData(programData);
        if(programData.isEmpty())
        {
            return;
        }
        QString exePath = programData[0];
        QString cmd = programData[1];

        QStringList cmdlist;
        if(isNeedBluetoothName)
        {
            cmdlist << bluetoothName;
        }

        if(processWorker == nullptr)
        {
            processWorker = new ProcessWorker();
            connect(this, SIGNAL(startExecProcess(QString,QStringList)), processWorker, SLOT(startProcess(QString,QStringList)));
            connect(processWorker, &ProcessWorker::processLog, this, &BluetoothCheck::recvBluetoothRespond);

            QString thirdToolDir = APPConfig::get()->getWsrDir();
            thirdToolDir.append("/bluesearch_tool");
            processWorker->setWorkDir(thirdToolDir);

            processWorker->moveToThread(&processThread);
            processThread.start();
        }
        emit startExecProcess(exePath, cmdlist);
    }
}

void BluetoothCheck::startBusiness(const QString &)
{
    qDebug()<<"is BluetoothCheck";
}

void BluetoothCheck::processBusiness()
{
    qDebug()<<"start BluetoothCheck";
    if(builder == nullptr)
    {
        builder = new JLinkWriteBuilder(this);
    }

    deleteTestOjbects();
    QString thirdToolDir = APPConfig::get()->getWsrDir();
    thirdToolDir.append("/bluesearch_tool/bluesearch_tool.exe");
    QString cmd = thirdToolDir;
    QString param = bluetoothName;
    QString respond = "";
    builder->build("测试蓝牙", cmd, param, respond);

    start();
}

void BluetoothCheck::startWorker(const QString )
{
    processBusiness();
}

void BluetoothCheck::recvBluetoothRespond(const QString & msg)
{
    qDebug()<<"recv msg"<<msg;
    processReusult(msg);
}
