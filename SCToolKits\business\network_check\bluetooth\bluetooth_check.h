#ifndef BLUETOOTHCHECK_H
#define BLUETOOTHCHECK_H

#include <QObject>
#include "workers/test_worker.h"
#include "common_share/interface_business.h"
#include "data/interface_data/interface_data.h"
#include "gui/tip_window.h"
#include "test_objects/test_object.h"
#include <QCoreApplication>
#include <QDebug>
#include "business/flash_writer/JLink/jlink_write_builder.h"
#include "workers/process_worker.h"
#include "workers/ssh_test_worker.h"
#include "mes/mes_manager.h"
#include "3rdparty/curl/include/curl.h"
#include <QDir>
#include <QThread>
#include <QRegularExpression>
#include <QRegularExpressionMatch>

class BluetoothCheck : public TestWorker,public IBusiness
{
    Q_OBJECT
public:
    explicit BluetoothCheck(QObject *parent = nullptr);
    ~BluetoothCheck();
    void startWorker(const QString toolName);
    void startBusiness(const QString &);

public:
    void start();
    void setIsNeedBlutoothName(bool);
public slots:
    void recvBluetoothRespond(const QString & msg);
signals:
    void finished(int ret);
    void startBusinessSignal();
    void startExecProcess(const QString & exePath, const QStringList & cmd);
    void bluetoothProgressCtx(const QString & name, int index, int totalNum);

    void checkProcessSignal(const QString &);
    void checkResultSignal(const QString &);
    void appendTestItemResultSignal(QJsonObject &);
private slots:
    void processBusiness();
private:
    void processCheck(int objectIndex,int operateIndex);
    int processReusult(const QString &msg);
    void updateTestCtx(TestObject*,int,int);
    void parseDualNetworkRecvLog(const QString & log, QStringList & list);
    static size_t saveCurlResponseToStdString(void *contents, size_t size, size_t nmemb, string *s);
    bool getQrCodeBySn();
private:
    TestObject *testObject;
    int testObjectIndex;
    int testOperateIndex;

    JLinkWriteBuilder * builder;
    ProcessWorker * processWorker;
    QThread processThread;
    bool isNeedBluetoothName;
    QString bluetoothName;
};

#endif // BLUETOOTHCHECK_H
