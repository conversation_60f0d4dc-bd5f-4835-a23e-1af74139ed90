#include "ethernet_check.h"
#include "builders/ftp_test_object_builder.h"
#include "test_objects/test_object.h"
#include "ssh/ftp_client.h"
#include "builders/ssh_operate_builder.h"
#include "workers/ssh_test_worker.h"
#include "link_manager.h"

#include <QCoreApplication>

class EthernetCheckImp : public TestWorker
{
    EthernetCheckImp(EthernetCheck* ptr) : builder(nullptr),
        sshBuilder(nullptr),
        ftpUploader(nullptr),
        sshWoker(nullptr),
        ethernetCheck(ptr),
        testObjectIndex(0),testOperateIndex(0)
    {

    }
    ~EthernetCheckImp()
    {
        if(builder)
        {
            delete builder;
            builder = nullptr;
        }
        if(sshBuilder)
        {
            delete sshBuilder;
            sshBuilder = nullptr;
        }
        if(ftpUploader)
        {
            delete ftpUploader;
            ftpUploader = nullptr;
        }
        if(sshWoker)
        {
            delete sshWoker;
            sshWoker = nullptr;
        }
    }
public:
    void start();
public slots:
    void handleDownloadSlot(QString);
private:
    void processCheck(int objectIndex,int operateIndex);
    int processReusult(const QString &);
    bool processMsgResult(TestObject *, const QString &);
private:
    void updateTestCtx(TestObject*,int,int);
private:
    friend EthernetCheck;
    FTPTestObjectBuilder * builder;
    SSHOperateBuilder * sshBuilder;
    SecureFileUploader *ftpUploader;
    SSHTestWorker * sshWoker;
    EthernetCheck * ethernetCheck;
    TestObject *testObject;
    int testObjectIndex;
    int testOperateIndex;

    int cnt;
};

void EthernetCheckImp::updateTestCtx(TestObject *object, int objectIndex, int operateIndex)
{
    testObject = object;
    testObjectIndex = objectIndex;
    testOperateIndex = operateIndex;
}

int EthernetCheckImp::processReusult(const QString &ctx)
{
    //TODO:
    testObject = getTestObject(testObjectIndex);
    if(testObject == nullptr)
    {
        return 1;
    }

    SSHClient * sshclient =  LinkManager::get()->getSSH();
    //处理结果
    bool isSuccess = processMsgResult(testObject, ctx);
    if(!isSuccess)
    {
        emit ethernetCheck->checkProcessSignal("以太网通信异常，请排查问题");
        emit ethernetCheck->checkResultSignal("以太网测试失败");
        emit ethernetCheck->finishedTestSiganl("TEST_EHT0",0);
        deleteTestOjbects();
        qDebug() << "check failed";
        if(sshclient)
        {
           bool dis = disconnect(sshclient,&SSHClient::sigDataArrived,sshWoker,&SSHTestWorker::recvSSHDataSlot);
           qDebug()<< "dis ssh ret "<< dis;
        }
        disconnect(ftpUploader,&SecureFileUploader::updateDownloadProcessResult,this,&EthernetCheckImp::handleDownloadSlot);
        return 0;
    }
    else
    {
        int operateIndex = testOperateIndex + 1;
        TestOperate * testOperate = nullptr;
        testObject->getTestOperate(operateIndex,&testOperate);
        if(testOperate)
        {
            processCheck(testObjectIndex,operateIndex);
            return 0;
        }
        else
        {
            int objectIndex = testObjectIndex +1;
            TestObject * testObject =  getTestObject(objectIndex);
            if(testObject == nullptr)
            {
                qDebug()<< "ethernet check over";
                updateTestCtx(nullptr,0,0);

                QJsonObject itemResult;
                itemResult["itemName"] = "TEST_EHT0";
                itemResult["itemResult"] = "OK";
                emit ethernetCheck->appendTestItemResultSignal(itemResult);
                emit ethernetCheck->checkProcessSignal("以太网通信正常");
                emit ethernetCheck->checkResultSignal("以太网测试通过");
                emit ethernetCheck->finishedTestSiganl("TEST_EHT0",1);
                deleteTestOjbects();

                if(sshclient)
                {
                   bool dis = disconnect(sshclient,&SSHClient::sigDataArrived,sshWoker,&SSHTestWorker::recvSSHDataSlot);
                   qDebug()<< "dis ssh ret "<< dis;
                }
                disconnect(ftpUploader,&SecureFileUploader::updateDownloadProcessResult,this,&EthernetCheckImp::handleDownloadSlot);
                return 1;
            }
            else
            {
                processCheck(objectIndex,0);
                return 0;
            }
        }
    }
}

bool EthernetCheckImp::processMsgResult(TestObject *testObject, const QString &ctx)
{
    TestOperate *testOperate = nullptr;
    testObject->getTestOperate(testOperateIndex, &testOperate);

    if(testOperate == nullptr)
    {
        return 0;
    }
    else if(testOperate->getOperateCode() == FTP_TASK)
    {
        if(!ctx.contains("OK"))
        {
            return 0;
        }
    }
    else if (testOperate->getOperateCode() == SSH_TASK)
    {
        QStringList dataCtx;
        testOperate->getStringData(dataCtx);

        if(!ctx.contains(dataCtx[1]))
        {
            return 0;
        }
    }

    return 1;
}

void EthernetCheckImp::start()
{
    emit ethernetCheck->checkProcessSignal("正在检测以太网通信是否正常，请稍等……");

    processCheck(testObjectIndex,testOperateIndex);//初始值0，0
}

void EthernetCheckImp::handleDownloadSlot(QString tip)
{
    if(tip.contains("error"))
    {
        SSHClient * sshclient =  LinkManager::get()->getSSH();
        emit ethernetCheck->checkProcessSignal("上传脚本失败");
        emit ethernetCheck->checkResultSignal("以太网测试失败");
        emit ethernetCheck->finishedTestSiganl("TEST_EHT0",0);
        deleteTestOjbects();
        qDebug()<<"check failed!";
        if(sshclient)
        {
           bool dis = disconnect(sshclient,&SSHClient::sigDataArrived,sshWoker,&SSHTestWorker::recvSSHDataSlot);
           qDebug()<< "dis ssh ret "<< dis;
        }
        disconnect(ftpUploader,&SecureFileUploader::updateDownloadProcessResult,this,&EthernetCheckImp::handleDownloadSlot);
    }
}

void EthernetCheckImp::processCheck(int objectIndex,int operateIndex)
{
    //多个worker的组合工作。
    // ftpUploader,sshWorker都是其中的一个worker
   TestObject * testObject =  getTestObject(objectIndex);
   if(testObject == nullptr)
   {
       qDebug()<< "not found test obbject for index 0";
       return;
   }
   TestOperate * testOperate = nullptr;
   testObject->getTestOperate(operateIndex,&testOperate);
   if(testOperate == nullptr)
   {
       qDebug()<< "not found testOperate for index 0";
       return ;
   }

   updateTestCtx(testObject,objectIndex,operateIndex);
   if(FTP_TASK == testOperate->getOperateCode())
   {
       QStringList ftpCtx;
       testOperate->getStringData(ftpCtx);

       QString src = ftpCtx[0];
       QString dst = ftpCtx[1];
       ftpUploader->upload(src, dst, DeviceContext::get()->getDeviceLinkIP(),
                                  DeviceContext::get()->getSSHLoginUser(),
                                  DeviceContext::get()->getSSHLoginPassword(),
                                  LinkManager::get()->getSshModel(),DeviceContext::get()->getSSHPrivateKey());
   }
   else if (SSH_TASK == testOperate->getOperateCode())
   {
       QStringList sshCtx;
       testOperate->getStringData(sshCtx);
       QString src = sshCtx[0];
       emit sshWoker->sendDataSignal(src);//写命令
   }
}

/********************************EthernetCheck函数*****************/
EthernetCheck::EthernetCheck(QObject *parent) : ethernetCheckImp(new EthernetCheckImp(this))
{
    connect(this,&EthernetCheck::startBusinessSignal,this,&EthernetCheck::processBusiness,Qt::QueuedConnection);
}

EthernetCheck::~EthernetCheck()
{
    delete ethernetCheckImp ;
}
void EthernetCheck::startBusiness(const QString &busniess)
{
    qDebug()<< "is EthernetCheck";
}
void EthernetCheck::processBusiness()
{
    qDebug()<< "ForthGCheck::processBusiness()--start test wifi";
    deleteTestOjbects();
    //创建测试对象。
    if(ethernetCheckImp->builder  == nullptr)
    {
        ethernetCheckImp->builder = new FTPTestObjectBuilder(ethernetCheckImp);

    }
    //TODO
    QString path = APPConfig::get()->getWsrDir();
    QString localFile =path+QString("/communication_script/wifi_check.sh");
    qDebug()<< "ftp file "<< localFile;
    ethernetCheckImp->builder->build(localFile,"/tmp");

    //创建ftp传输
    if(ethernetCheckImp->ftpUploader == nullptr)
    {
        SecureFileUploader * ftpUploader = new SecureFileUploader;
        connect(ftpUploader,&SecureFileUploader::endDownloadSignal,this,&EthernetCheck::endDownloadSlot);
        ethernetCheckImp->ftpUploader = ftpUploader;
    }
    connect(ethernetCheckImp->ftpUploader,&SecureFileUploader::updateDownloadProcessResult,ethernetCheckImp,&EthernetCheckImp::handleDownloadSlot);


    //build ssh 信令
    if(ethernetCheckImp->sshBuilder == nullptr)
    {
        ethernetCheckImp->sshBuilder = new SSHOperateBuilder(ethernetCheckImp);
    }
    ethernetCheckImp->sshBuilder->setTestWorker(ethernetCheckImp);
    ethernetCheckImp->sshBuilder->build("sh /tmp/wifi_check.sh ethernet","network MODEL OK","以太网检测");

    if(ethernetCheckImp->sshWoker == nullptr)
    {
        ethernetCheckImp->sshWoker = new SSHTestWorker();
    }

    SSHClient * sshclient =  LinkManager::get()->getSSH();
    if(sshclient)
    {
        connect(ethernetCheckImp->sshWoker,&SSHTestWorker::sendDataSignal,sshclient,&SSHClient::sendData,Qt::UniqueConnection);
        connect(ethernetCheckImp->sshWoker,&SSHTestWorker::respondDataSignal,this,&EthernetCheck::recvSSHRespond,Qt::UniqueConnection);

        connect(sshclient,&SSHClient::sigDataArrived,ethernetCheckImp->sshWoker,&SSHTestWorker::recvSSHDataSlot,Qt::UniqueConnection);
    }
    //启动worker
    ethernetCheckImp->start();
}

void EthernetCheck::startWorker(const QString )
{
//    processBusiness();
    emit startBusinessSignal();
}
void EthernetCheck::recvSSHRespond(const QString & msg)
{
    qDebug()<<"forth recv ssh msg"<<msg;
    ethernetCheckImp->processReusult(msg);
}
void EthernetCheck::endDownloadSlot(int result,QString ctx)
{
    if(result)
    {
        qDebug()<< "is ok" <<ctx;
        ethernetCheckImp->processReusult(ctx);
    }
}

void EthernetCheck::processConnectResult(bool ,QString ,int )
{
    ethernetCheckImp->updateTestCtx(nullptr, 0, 0);

    SSHClient * sshclient =  LinkManager::get()->getSSH();
    if(sshclient)
    {
       bool dis = disconnect(sshclient,&SSHClient::sigDataArrived,ethernetCheckImp->sshWoker,&SSHTestWorker::recvSSHDataSlot);
       qDebug()<< "dis ssh ret "<< dis;
    }
}
