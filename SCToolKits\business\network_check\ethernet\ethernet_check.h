#ifndef ETHERNETCHECK_H
#define ETHERNETCHECK_H

#include <QObject>
#include <QJsonObject>
#include "workers/test_worker.h"
#include "common_share/interface_business.h"
#include "app_config.h"

class EthernetCheckImp;
class EthernetCheck : public TestWorker,public IBusiness
{
    Q_OBJECT
public:
    explicit EthernetCheck(QObject *parent = nullptr);
    ~EthernetCheck();
    void startWorker(const QString toolName);
    void startBusiness(const QString &);
public slots:
    void endDownloadSlot(int result,QString ctx);
    void recvSSHRespond(const QString & msg);
    friend EthernetCheckImp;

    void processConnectResult(bool ,QString ,int );

signals:
    void finished(int ret);
    void startBusinessSignal();
    void checkProcessSignal(const QString &);
    void checkResultSignal(const QString &);
    void updateProcessBarSignal(int, int);
    void appendTestItemResultSignal(QJsonObject &);
private slots:
    void processBusiness();
private:
    EthernetCheckImp * ethernetCheckImp;
};

#endif // ETHERNET_H
