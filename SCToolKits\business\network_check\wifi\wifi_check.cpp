#include "wifi_check.h"
#include "builders/ftp_test_object_builder.h"
#include "test_objects/test_object.h"
#include "ssh/ftp_client.h"
#include "builders/ssh_operate_builder.h"
#include "workers/ssh_test_worker.h"
#include "link_manager.h"
#include <QCoreApplication>
class WIFICheckImp : public TestWorker
{
public:
    WIFICheckImp(WIFICheck* ptr) :
        builder(nullptr),
        sshBuilder(nullptr),
        ftpUploader(nullptr),
        sshWoker(nullptr),
        wifiCheck(ptr),
        testObject(nullptr),testObjectIndex(0),testOperateIndex(0)
    {

    }
    ~WIFICheckImp()
    {
        if(builder)
        {
            delete builder;
            builder = nullptr;
        }
        if(sshBuilder)
        {
            delete sshBuilder;
            sshBuilder = nullptr;
        }
        if(ftpUploader)
        {
            delete ftpUploader;
            ftpUploader = nullptr;
        }
        if(sshWoker)
        {
            delete sshWoker;
            sshWoker = nullptr;
        }

    }
public:
    void start();
public slots:
    void handleDownloadSlot(QString);
public:
    void processCheck(int objectIndex,int  operateIndex);
    int processReusult(const QString &);
    bool processMsgResult(TestObject *, const QString &);

private:
    void updateTestCtx(TestObject *object, int objectIndex, int operateIndex);
private:
    friend WIFICheck;
    FTPTestObjectBuilder * builder {nullptr};
    SSHOperateBuilder * sshBuilder {nullptr};
    SecureFileUploader *ftpUploader {nullptr};
    SSHTestWorker * sshWoker {nullptr};
    WIFICheck * wifiCheck {nullptr};
    TestObject *testObject {nullptr};
    int testObjectIndex;
    int testOperateIndex;

    int cnt;//暂时不知道作用
};

void WIFICheckImp::updateTestCtx(TestObject *object, int objectIndex, int operateIndex)
{
    testObject = object;
    testObjectIndex = objectIndex;
    testOperateIndex = operateIndex;
}
int  WIFICheckImp::processReusult(const QString &ctx)
{
    //TODO:
    testObject = getTestObject(testObjectIndex);
    if(testObject==nullptr)
    {
        return 1;
    }

    SSHClient * sshclient =  LinkManager::get()->getSSH();
    //处理结果
    bool isSuccess = processMsgResult(testObject, ctx);
    if(!isSuccess)
    {
        emit wifiCheck->checkProcessSignal("WIFI通信异常，请排查问题！");
        emit wifiCheck->checkResultSignal("WIFI测试失败！");
        emit wifiCheck->finishedTestSiganl("TEST_WIFI",0);
        deleteTestOjbects();
        qDebug()<<"check failed!";
        if(sshclient)
        {
           bool dis = disconnect(sshclient,&SSHClient::sigDataArrived,sshWoker,&SSHTestWorker::recvSSHDataSlot);
           qDebug()<< "dis ssh ret "<< dis;
        }
        disconnect(ftpUploader,&SecureFileUploader::updateDownloadProcessResult,this,&WIFICheckImp::handleDownloadSlot);
        return 0;
    }
    else
    {
        int operateIndex = testOperateIndex + 1;
        TestOperate * testOperate = nullptr;
        testObject->getTestOperate(operateIndex,&testOperate);
        if(testOperate)
        {
            processCheck(testObjectIndex,operateIndex);
            return 0;
        }
        else
        {
            int objectIndex = testObjectIndex +1;
            TestObject * testObject =  getTestObject(objectIndex);
            if(testObject == nullptr)
            {
                updateTestCtx(nullptr,0,0);

                QJsonObject itemResult;
                itemResult["itemName"] = "TEST_WIFI";
                itemResult["itemResult"] = "OK";
                emit wifiCheck->appendTestItemResultSignal(itemResult);
                emit wifiCheck->checkProcessSignal("WIFI通信正常");
                emit wifiCheck->checkResultSignal("WIFI测试通过");
                emit wifiCheck->finishedTestSiganl("TEST_WIFI",1);
                deleteTestOjbects();
                //TODO why disconnect()成功后，也无法断开
                SSHClient * sshclient =  LinkManager::get()->getSSH();
                if(sshclient)
                {
                    bool dis = disconnect(sshclient,&SSHClient::sigDataArrived,sshWoker,&SSHTestWorker::recvSSHDataSlot);
                    qDebug()<< "dis ssh ret "<< dis;
                }
                disconnect(ftpUploader,&SecureFileUploader::updateDownloadProcessResult,this,&WIFICheckImp::handleDownloadSlot);
                qDebug()<< "wifi check over";
                return 1;
            }
            else
            {
                processCheck(objectIndex,0);
                return 0;
            }
        }
    }
}

bool WIFICheckImp::processMsgResult(TestObject *testObject, const QString &ctx)
{
    TestOperate *testOperate = nullptr;
    testObject->getTestOperate(testOperateIndex, &testOperate);

    if(testOperate == nullptr)
    {
        return 0;
    }
    else if(testOperate->getOperateCode() == FTP_TASK)
    {
        if(!ctx.contains("OK"))
        {
            return 0;
        }
    }
    else if (testOperate->getOperateCode() == SSH_TASK)
    {
        QStringList dataCtx;
        testOperate->getStringData(dataCtx);

            if(!ctx.contains(dataCtx[1]))
            {
                return 0;
            }
        }
    return 1;
}
void WIFICheckImp::start()
{
    emit wifiCheck->checkProcessSignal("正在检测WIFI通信是否正常，请稍等……");
    processCheck(testObjectIndex,testOperateIndex);
}

void WIFICheckImp::handleDownloadSlot(QString tip)
{
    if(tip.contains("error"))
    {
        SSHClient * sshclient =  LinkManager::get()->getSSH();
        emit wifiCheck->checkProcessSignal("上传脚本失败");
        emit wifiCheck->checkResultSignal("WIFI测试失败！");
        emit wifiCheck->finishedTestSiganl("TEST_WIFI",0);
        deleteTestOjbects();
        qDebug()<<"check failed!";
        if(sshclient)
        {
           bool dis = disconnect(sshclient,&SSHClient::sigDataArrived,sshWoker,&SSHTestWorker::recvSSHDataSlot);
           qDebug()<< "dis ssh ret "<< dis;
        }
        disconnect(ftpUploader,&SecureFileUploader::updateDownloadProcessResult,this,&WIFICheckImp::handleDownloadSlot);
    }

}
void WIFICheckImp::processCheck(int objectIndex,int  operateIndex)
{
    //多个worker的组合工作。
    // ftpUploader,sshWorker都是其中的一个worker
   TestObject * testObject =  getTestObject(objectIndex);
   if(testObject == nullptr)
   {
       qDebug()<< "not found test obbject for index 0";
       return;
   }
   TestOperate * testOperate = nullptr;
   testObject->getTestOperate(operateIndex,&testOperate);
   if(testOperate == nullptr)
   {
       qDebug()<< "not found testOperate for index 0";
       return ;
   }
   updateTestCtx(testObject,objectIndex,operateIndex);
   if(FTP_TASK == testOperate->getOperateCode())
   {
       QStringList ftpCtx;
       testOperate->getStringData(ftpCtx);

       QString src = ftpCtx[0];
       QString dst = ftpCtx[1];

       ftpUploader->upload(src, dst, DeviceContext::get()->getDeviceLinkIP(),
                                  DeviceContext::get()->getSSHLoginUser(),
                                  DeviceContext::get()->getSSHLoginPassword(),
                                  LinkManager::get()->getSshModel(),DeviceContext::get()->getSSHPrivateKey());
   }
   else if (SSH_TASK == testOperate->getOperateCode())
   {
       QStringList sshCtx;
       testOperate->getStringData(sshCtx);
       QString src = sshCtx[0];
       emit sshWoker->sendDataSignal(src);
   }
}
WIFICheck::WIFICheck():wifiCheckImp(new WIFICheckImp(this))
{
    connect(this,&WIFICheck::startBusinessSignal,this,&WIFICheck::processBusiness,Qt::QueuedConnection);

}
WIFICheck::~WIFICheck()
{
    delete wifiCheckImp;
}

void WIFICheck::startBusiness(const QString &busniess)
{
    qDebug()<<"is WIFIcheck";
}

void WIFICheck::processBusiness()
{
    qDebug()<< "start test wifi";
    WifiConfData data;
    InterfaceData::get()->getData(data);
    if(data.ssid.isEmpty())
    {
        if(SQLiteManager::get()->select(data))
        {
            qDebug()<<"WIFICheck::processBusiness()----"<<data.ssid<<"----"<<data.model<<"----"<<data.pwd;
        }
    }
//    if(data.ssid != wifiConfData.ssid || data.model != wifiConfData.model || data.pwd != wifiConfData.pwd)
    {
//        InterfaceData::get()->getData(wifiConfData);
        script = QString("sh /tmp/wifi_check.sh wifi %1 %2 %3").arg(data.ssid).arg(data.model).arg(data.pwd);
    }
    //创建测试对象。
    if(wifiCheckImp->builder == nullptr)
    {
        wifiCheckImp->builder = new FTPTestObjectBuilder(wifiCheckImp);

    }

    //build ssh 信令
    if(wifiCheckImp->sshBuilder == nullptr)
    {
        wifiCheckImp->sshBuilder = new SSHOperateBuilder(wifiCheckImp);
    }

    //TODO
    QString path = APPConfig::get()->getWsrDir();
    QString localFile =path+QString("/communication_script/wifi_check.sh");
    qDebug()<< "ftp file "<< localFile;
    wifiCheckImp->builder->build(localFile,"/tmp");
    wifiCheckImp->sshBuilder->setTestWorker(wifiCheckImp);
    wifiCheckImp->sshBuilder->build(script,"WIFI MODEL OK","WIFI检测");

    //创建ftp传输
    if(wifiCheckImp->ftpUploader == nullptr)
    {
        SecureFileUploader * ftpUploader = new SecureFileUploader;
        connect(ftpUploader,&SecureFileUploader::endDownloadSignal,this, &WIFICheck::endDownloadSlot);
        wifiCheckImp->ftpUploader = ftpUploader;
    }
    connect(wifiCheckImp->ftpUploader,&SecureFileUploader::updateDownloadProcessResult,wifiCheckImp,&WIFICheckImp::handleDownloadSlot);

    if(wifiCheckImp->sshWoker == nullptr)
    {
        wifiCheckImp->sshWoker = new SSHTestWorker();
    }
    //临时放到此处
    SSHClient * sshclient =  LinkManager::get()->getSSH();
    if(sshclient)
    {
       connect(wifiCheckImp->sshWoker,&SSHTestWorker::sendDataSignal,sshclient,&SSHClient::sendData,Qt::UniqueConnection);
       connect(wifiCheckImp->sshWoker,&SSHTestWorker::respondDataSignal,this,&WIFICheck::recvSSHRespond,Qt::UniqueConnection);

       connect(sshclient,&SSHClient::sigDataArrived,wifiCheckImp->sshWoker,&SSHTestWorker::recvSSHDataSlot,Qt::UniqueConnection);
    }

    //启动worker
    wifiCheckImp->start();
}
void WIFICheck::startWorker(const QString )
{
//    processBusiness();
    emit startBusinessSignal();
}



void WIFICheck::recvSSHRespond(const QString & msg)
{
    qDebug()<< "wifi ssh msg="<<msg;
    wifiCheckImp->processReusult(msg);
}

void WIFICheck::endDownloadSlot(int result,QString ctx)
{
    if(result)
    {
        qDebug()<< "is ok"<<ctx;
        wifiCheckImp->processReusult(ctx);

    }
}

void WIFICheck::processConnectResult(bool ,QString ,int )
{
    wifiCheckImp->updateTestCtx(nullptr, 0, 0);

    SSHClient * sshclient = LinkManager::get()->getSSH();
    if(sshclient)
    {
        bool dis = disconnect(sshclient,&SSHClient::sigDataArrived,wifiCheckImp->sshWoker,&SSHTestWorker::recvSSHDataSlot);
        qDebug()<<"dis ssh ret"<<dis;
    }
}
