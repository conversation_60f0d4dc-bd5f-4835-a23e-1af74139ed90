#ifndef WIFICHECK_H
#define WIFICHECK_H

#include <QObject>
#include <QJsonObject>
#include "workers/test_worker.h"
#include "common_share/interface_business.h"
#include "data/interface_data/interface_data.h"
#include "database/sqlite_manager.h"
#include "communication/link_manager.h"
#include "app_config.h"
class WIFICheckImp;
class WIFICheck:public TestWorker,public IBusiness
{
        Q_OBJECT
public:
    explicit WIFICheck();
    ~WIFICheck();
public:
    void startWorker(const QString toolName);
    void startBusiness(const QString &busniess);

public slots:
    void endDownloadSlot(int result,QString ctx);
    void recvSSHRespond(const QString & msg);

    friend WIFICheckImp;
    void processConnectResult(bool ,QString ,int );
signals:
    void finished(int ret);
    void startBusinessSignal();
    void checkProcessSignal(const QString &);
    void checkResultSignal(const QString &);
    void updateProcessBarSignal(int, int);
    void appendTestItemResultSignal(QJsonObject &);
private slots:
    void processBusiness();
private:
    WIFICheckImp * wifiCheckImp;
    QString script;
    WifiConfData wifiConfData;
};

#endif // WIFICHECK_H

