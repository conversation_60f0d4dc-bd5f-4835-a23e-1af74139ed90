#include "interantional_dc.h"
#include "link_manager.h"
#include "security/se05x/se05x.h"
#include "app_config.h"
#include "common_share/device_mes_worker.h"
#include "git/git_client.h"
#include "xml/xml_parser.h"
#include "mes/mes_manager.h"
#include <QSharedPointer>
#include "interface_data/interface_data.h"
INTLDCConfig::INTLDCConfig():testResult(true),
                             maintenacePattern(false),
                             hasGetProductInfo(false),
                             needContinueTest(true),
                             currentCoreTask(GET_HWID_TYPE_E),
                             sshWorker(nullptr),
                             processWorker(nullptr),
                             isBaseProductType(false),
                             isneedAuthKey(false),
                             isCabinet(false),
                             exceptionTimer(nullptr),
                             certificateWorker(nullptr)

{
    connect(this,&INTLDCConfig::triggerTaskSignal,this,&INTLDCConfig::processCoreTasks,Qt::QueuedConnection);
    connect(this,&INTLDCConfig::startBusinessSignal,this,&INTLDCConfig::processBusiness,Qt::QueuedConnection);

    connect(MesManager::get(),&MesManager::gbConfigInfoSingal,this,&INTLDCConfig::recvMesReply);
    connect(MesManager::get(),&MesManager::queryAuthkeySignal,this,&INTLDCConfig::recvMesReply);
    connect(MesManager::get(),&MesManager::equipmentsInfoSignal,this,&INTLDCConfig::recvMesReply);

    coreTasks[CONFIRE_PRODUCT_TYPE] = &INTLDCConfig::confirmProductType;
    coreTasks[GET_HWID_TYPE_E] = &INTLDCConfig::getHWID;
    coreTasks[WRITE_CONFIG_E] = &INTLDCConfig::writeConfig;
    coreTasks[MES_REQUEST_TASK_E] = &INTLDCConfig::requestInfoFromMes;
    coreTasks[CHECK_DEVICE_INFO_TASK_E] = &INTLDCConfig::checkConfigInfo;
    coreTasks[END_TEST_TASKE_E] = &INTLDCConfig::finishedTest;
}
INTLDCConfig::~INTLDCConfig()
{
    if(processWorker)
    {
        processThread.exit();
        processWorker->deleteLater() ;
    }
}
void INTLDCConfig::initTestCtx()
{
    errorReason.clear();
    testResult=true;
    portCacheId.clear();
    //TEST 代码
//    updateTaskStep(REQUSET_PRODUCT_INFO_STEP_E);
//    updateCoreTask(CHECK_DEVICE_INFO_TASK_E);
//    updateTaskStep(GET_UID_STEP_E);
//    updateCoreTask(GET_SE05X_CTX_TASK_E);

    updateTaskStep(DOWN_XML_FILE_SETP_E);
    updateCoreTask(GET_HWID_TYPE_E);

//    updateTaskStep(EXCUTE_CONFIG_SCRIPTE_STEP_E);
//    updateTaskStep(WAIT_DEVICE_REBOOT_STEP_E);
//    updateCoreTask(WRITE_CONFIG_E);

//    updateTaskStep(GET_PRODUCT_TYPE_STEP_E);
//    updateCoreTask(CONFIRE_PRODUCT_TYPE);


}
void INTLDCConfig::setMaintaincePattern(bool st)
{
    maintenacePattern=!st;
    qDebug()<<"maintenacePattern"<<maintenacePattern;
}
void INTLDCConfig::startWorker(const QString toolName)
{
    Q_UNUSED(toolName);
    initTestCtx();
    emit startBusinessSignal();
}
void INTLDCConfig::handleErro(const QString & reason)
{
    testResult = false;
    errorReason = reason;
    resetEnv(testResult);
    updateCoreTask(END_TEST_TASKE_E);
    updateTaskStep(UNKOW_MAX_TASK_STEP_E);
    triggerTaskSignal();

    return;
}
void INTLDCConfig::finishedTest()
{
    QString testTip(tr("配置完成"));
    if(testResult == false)
    {
        if(errorReason.isEmpty())
        {
            testTip=tr("配置异常");
        }
        else
        {
            testTip =errorReason;
        }
    }
    updateCoreTask(END_TEST_TASKE_E);
    updateTaskStep(UNKOW_MAX_TASK_STEP_E);
    emit tipCtxSignal(testTip,100,testResult);
    disconnect(sshWorker,&SSHTestWorker::respondDataSignal,this,&INTLDCConfig::recvSSHRespond);

    emit finishedTestSiganl("dc config",testResult);
}
void INTLDCConfig::resetEnv(bool ret)
{
    testResult = ret;
    currentCoreTask = GET_HWID_TYPE_E;
    currentTaskStep = DOWN_XML_FILE_SETP_E;
}
void INTLDCConfig::updateCoreTask(TaskType task)
{
    currentCoreTask = task;
}
void INTLDCConfig::updateTaskStep(TaskStepOrder step,bool isIncrease,int stepGap)
{
    //?是否安全？
    //TODO 更多的策略
    if(step >= UNKOW_MAX_TASK_STEP_E)
    {
        return ;
    }
    currentTaskStep = step;
}
void INTLDCConfig::setTestTaskIndex(int index)
{
    if((TaskType)index<UNKOW_MAX_TASK)
    {
        currentCoreTask = (TaskType)index;
    }
}
void INTLDCConfig::processBusiness()
{
    //第一步确定SSH是否连接上。需要从桩端获取一些信息控制上位机的流程。
    SSHClient * sshClient =  LinkManager::get()->getSSH(DeviceContext::get()->getDeviceLinkIP());
    if(sshClient)//TODO:增加对是否连接的判定
    {
        if(sshWorker == nullptr)
        {
            sshWorker = new SSHTestWorker();

            connect(sshClient,&SSHClient::sigDataArrived,sshWorker,&SSHTestWorker::recvSSHDataSlot,Qt::UniqueConnection);
            connect(sshWorker,&SSHTestWorker::sendDataSignal,sshClient,&SSHClient::sendData,Qt::UniqueConnection);
        }
        connect(sshWorker,&SSHTestWorker::respondDataSignal,this,&INTLDCConfig::recvSSHRespond,Qt::UniqueConnection);
    }
    else
    {
        handleErro(tr("ssh无法连接"));
        return;
    }
    processCoreTasks();
}
void INTLDCConfig::processCoreTasks()
{
    auto iter = coreTasks.find(currentCoreTask);
    if(iter != coreTasks.end())
    {
        (this->*iter.value())();
    }
}
void INTLDCConfig::excuteSSHCmd(const QString & cmd)
{
    emit sshWorker->sendDataSignal(cmd);
}
void INTLDCConfig::recvSSHRespond(const QString & sshMsg)
{
    switch (currentTaskStep)
    {
        break;
        case GET_PRODUCT_TYPE_STEP_E:
            parseProductType(sshMsg);
        break;
        case EXCUTE_CONFIG_SCRIPTE_STEP_E:
            parseConfigureResult(sshMsg);
        break;
        case GET_DEVICE_VERSION_STEP_E:
            parseDeviceVersion(sshMsg);
        break;
        default:
        break;
    }
}
void INTLDCConfig::recvMesReply(bool ret, const QString & type, const QJsonObject &configInfo)
{
    if(ret)
    {
        switch (currentTaskStep)
        {
            case REQUSET_PRODUCT_INFO_STEP_E:
                parseMaterialInfo(type,configInfo);
            break;
            case GET_AUTHKEY_STEP_E:
                parseAuthkeyInfo(type,configInfo);
            break;
            case GET_MATERIAL_CODE_STEP:
                parseMaterialCodeInfo(type,configInfo);
            break;
            default:
            break;
        }
    }
    else
    {
        handleErro(tr("MES不可用"));
        return;
    }

}
bool INTLDCConfig::parseProductType(const QString & productCtx)
{
    if(productCtx.contains("DC_TOOLS_DOWNLOAD_CERTS=DISABLE"))
    {
        isBaseProductType = true;
    }
    else
    {
       isBaseProductType = false;
    }
    if(productCtx.contains("DC_MANUFACTURE_CONF_TYPE=VINFAST"))
    {
        isneedAuthKey = true;
    }
    if(productCtx.contains("DC_TOOLS_VERFIY_DEVICE=CABINET"))
    {
        isCabinet = true;
    }
    qDebug()<< "product type is "<<isBaseProductType
            <<"manufacture type is"<<isneedAuthKey
            <<"cabinet is"<<isCabinet;
    DeviceContext::get()->setBaseProductType(isBaseProductType);
    hasGetProductInfo = true;
    emit productInfoSignal(isBaseProductType);
    if(needContinueTest)
    {
        updateTaskStep(DOWN_XML_FILE_SETP_E);
        updateCoreTask(GET_HWID_TYPE_E);
        triggerTaskSignal();
    }

    return true;
}
void INTLDCConfig::confirmProductType()
{
    excuteSSHCmd("/etc/getSystemConfig.sh \n");
}
bool INTLDCConfig::parseMaterialInfo(const QString & type,const QJsonObject & configInfo)
{
    if(configInfo.contains("model"))
    {
        if(configInfo["model"].isArray())
        {
            //todo:
            QJsonArray model=configInfo["model"].toArray();
            for(int i = 0; i < model.count(); i++)
            {
                QJsonObject obj = model.at(i).toObject();
                QString softwareName = obj.value("softwareName").toString();
                QString softwareFile = obj.value("softwareFile").toString();
                qDebug()<< "softwareName:"<< softwareName<<"softwareFile:"<<softwareFile;
                verInfoList[softwareName]=softwareFile;
            }
            updateTaskStep(GET_DEVICE_VERSION_STEP_E);
            triggerTaskSignal();
        }
    }
    else
    {
        handleErro(tr("无法获取桩端的产品信息"));
    }

    return true;
}

bool INTLDCConfig::parseAuthkeyInfo(const QString &, const QJsonObject & configInfo)
{
    if(!configInfo.isEmpty())
    {
        authKey = configInfo.value("authKey").toString();
        qDebug()<< "authKey:"<< authKey;
        if(authKey.isEmpty())
        {
            handleErro(tr("无法获取authKey信息"));
            return false;
        }
        updateTaskStep(EXCUTE_CONFIG_SCRIPTE_STEP_E);
        updateCoreTask(WRITE_CONFIG_E);
        triggerTaskSignal();
    }
    else
    {
        handleErro(tr("无法获取MES信息"));
    }

    return true;
}

bool INTLDCConfig::parseMaterialCodeInfo(const QString &, const QJsonObject & configInfo)
{
    if(!configInfo.isEmpty())
    {
        DeviceContext::get()->setMaterialCode(configInfo.value("modelCode").toString());
        qDebug()<< "modelCode:"<< configInfo.value("modelCode").toString();
        updateTaskStep(PARSE_HWID_STEP_E);
        triggerTaskSignal();
    }
    else
    {
        handleErro(tr("无法获取料号信息"));
    }

    return true;
}
void INTLDCConfig::requestInfoFromMes()
{
    switch (currentTaskStep)
    {
        case REQUSET_PRODUCT_INFO_STEP_E:
            DeviceMesWorker::requestMesInfo(MES_GET_GB_CONFIRUE_IFNO,DeviceContext::get()->getSN());
        break;
        case GET_AUTHKEY_STEP_E:
            DeviceMesWorker::requestMesInfo(MES_GET_AUTHKEY_INFO,"1");
        break;
        case GET_MATERIAL_CODE_STEP:
            DeviceMesWorker::requestMesInfo(MES_GET_EQUIPMENTS_INFO);
        break;
        default:
        break;
    }
}
bool INTLDCConfig::requestInfoFromGit()
{
    QDir dir(APPConfig::get()->getFunsDataDir() + "git");
    if(dir.exists())
    {
        if (dir.removeRecursively())
        {
            qDebug() << "文件夹 '" << APPConfig::get()->getFunsDataDir() + "git" << "' 及其内容已成功删除。";
        }
        else
        {
            qDebug() << "无法删除文件夹 '" << APPConfig::get()->getFunsDataDir() + "git" << "'。可能没有权限或被占用。";
        }
    }
    QSharedPointer<GitClient>gitPtr(new GitClient());
    QSharedPointer<QSettings> projectFile ( new QSettings("SCToolKitsConfig.ini", QSettings::IniFormat));
    projectFile->beginGroup("GIT");
    gitPtr->setGitURL(projectFile->value("gitUrl").toString());
    int ret = gitPtr->cloneRepo( APPConfig::get()->getFunsDataDir() + "git","baseline");
    if(ret == 0)
    {
        xmlFile=APPConfig::get()->getFunsDataDir() + "git/" +"MappingList.xml";
        if(isBaseProductType)
        {
            updateTaskStep(GET_MATERIAL_CODE_STEP);
        }
        else
        {
            updateTaskStep(PARSE_HWID_STEP_E);
        }
        triggerTaskSignal();

        return true;
    }
    else
    {
        handleErro(tr("无法从git获取信息"));
        return false;
    }
    qDebug()<< "clone ret"<<ret;
}
bool INTLDCConfig::getXmlInfoFromLocal()
{
    xmlFile = InterfaceData::get()->getCustomFile();
    updateTaskStep(PARSE_HWID_STEP_E);
    triggerTaskSignal();
    return true;
}
bool INTLDCConfig::parseHWIDFromXml()
{
    QSharedPointer<XmlParser>xmlPtr(new XmlParser);
    if(xmlPtr->loadXML(xmlFile))
    {
        QString match;
        QString materialCode;
        if(isBaseProductType)
        {
            materialCode = DeviceContext::get()->getMaterialCode();
        }
        else
        {
            materialCode = DeviceContext::get()->getCustomMaterialCode();
        }
        if(materialCode.isEmpty())
        {
            handleErro(tr("料号获取错误"));
            return false;
        }
        bool ret = xmlPtr->findElement("ProductcodeSource","MaterialCode",materialCode,"SoftwareCode",match);
        if(ret)
        {
            HWID=match;
            if(isneedAuthKey)
            {
                updateTaskStep(GET_AUTHKEY_STEP_E);
                updateCoreTask(GET_HWID_TYPE_E);
                triggerTaskSignal();
                return true;
            }
            updateTaskStep(EXCUTE_CONFIG_SCRIPTE_STEP_E);
            updateCoreTask(WRITE_CONFIG_E);
            triggerTaskSignal();
        }
        else
        {
            qDebug()<<"input materialCode:"<<materialCode;
            handleErro(tr("无法获取HWID"));
            return false;
        }
    }
    else
    {
        handleErro(tr("xml文件无法获取"));
    }
    qDebug()<< "xml is "<<xmlFile;
    return true;
}
void INTLDCConfig::getHWID()
{
    emit tipCtxSignal("获取HWID中",10);
    switch(currentTaskStep)
    {
        case DOWN_XML_FILE_SETP_E:
            if(!maintenacePattern)
            {
                //当前方案，标品和订制品，都是从git上获取。
                requestInfoFromGit();
            }
            else
            {
                getXmlInfoFromLocal();
            }
        break;
        case PARSE_HWID_STEP_E:
            parseHWIDFromXml();
        break;
        case GET_AUTHKEY_STEP_E:
        case GET_MATERIAL_CODE_STEP:
            requestInfoFromMes();
        break;
        default:
            requestInfoFromGit();
        break;
    }

}
bool INTLDCConfig::parseConfigureResult(const QString & msg)
{
    qDebug()<<"recv ssh "<<msg;
    int pos = msg.indexOf("success");
    if(pos != -1)
    {
        if(maintenacePattern)
        {
            updateCoreTask(END_TEST_TASKE_E);
            updateTaskStep(NON_PAD_STEP_E);
            triggerTaskSignal();
        }
        else
        {
            if(!isBaseProductType)
            {
                updateTaskStep(WAIT_DEVICE_REBOOT_STEP_E);
                triggerTaskSignal();
            }
            else
            {
                updateCoreTask(END_TEST_TASKE_E);
                updateTaskStep(NON_PAD_STEP_E);
                triggerTaskSignal();
            }
        }
    }
    else
    {
        handleErro(tr("配置失败"));
        return false;
    }
    return true;
}
void INTLDCConfig::hanleExceptTimeout()
{
    exceptionTimer->stop();
    switch(currentTaskStep)
    {
        case WAIT_DEVICE_REBOOT_STEP_E:
            waitSSHLink();
            break;
        case WAIT_SSH_OPEND_STEP_E:
            handleSSHLink();
            break;
        default:
            handleErro(tr("设备重启"));
            break;
    }
}
bool INTLDCConfig::dipsplayHumanOperateTip(const QString & tip)
{
   //在多线程中，直接返回是无效的，需要通过信号传递。
   bool ret =  emit humanProcessTipSignal(tip,0x100|0x8|0x2,10);
   qDebug()<< "humin::::::"<<ret;
   return ret;
}
bool INTLDCConfig::handleSSHLink()
{
    if(LinkManager::get()->getSSHLinkedStatus(DeviceContext::get()->getDeviceLinkIP())==false)
    {
        handleErro(tr("无法连接SSH,请web打开SSH,重新测试"));
        return true;
    }
    else
    {
        if(certificateWorker == nullptr)
        {
            certificateWorker = new CertificateWorker();
            connect(certificateWorker,&CertificateWorker::certificateWriteResultSignal,this,&INTLDCConfig::finishedSe05xTestSlot);
            connect(certificateWorker,&CertificateWorker::tipCtxSignal,this,&INTLDCConfig::tipCtxSignal);
        }
        QSharedPointer<NXPEdgeGoServer> ptr(new NXPEdgeGoServer(""));
        ptr->setDeviceGroudId(groupId);
        certificateWorker->setServerInfo(ptr->getNXPELinkInfo());
        certificateWorker->startWorker("");
    }
    return true;
}
void INTLDCConfig::receivHumanOperateResultSlot(bool ret)
{
    if(ret)
    {
        if(LinkManager::get()->getSSHLinkedStatus(DeviceContext::get()->getDeviceLinkIP())==false)
        {
            emit tipCtxSignal("SSH重连中,请确保SSH已经打开",50);
            updateTaskStep(WAIT_SSH_OPEND_STEP_E);
            timeoutMs = 20*1000;
            exceptionTimer->start(timeoutMs);
        }
        else
        {
            if(certificateWorker == nullptr)
            {
                certificateWorker = new CertificateWorker();
                connect(certificateWorker,&CertificateWorker::certificateWriteResultSignal,this,&INTLDCConfig::finishedSe05xTestSlot);
                connect(certificateWorker,&CertificateWorker::tipCtxSignal,this,&INTLDCConfig::tipCtxSignal);
            }
            QSharedPointer<NXPEdgeGoServer> ptr(new NXPEdgeGoServer(""));
            ptr->setDeviceGroudId(groupId);
            certificateWorker->setServerInfo(ptr->getNXPELinkInfo());
            certificateWorker->startWorker("");
        }
    }
    else
    {
        //TODO:当前一定是true
    }
}
bool INTLDCConfig::waitSSHLink()
{
    updateTaskStep(WAIT_SSH_OPEND_STEP_E);
    dipsplayHumanOperateTip(tr("请先在web打开ssh，再按确定键"));
    return true;
}
bool INTLDCConfig::waitDeviceReboot()
{
    if(exceptionTimer == nullptr)
    {
        exceptionTimer = new QTimer();
        connect(exceptionTimer,&QTimer::timeout,this,&INTLDCConfig::hanleExceptTimeout);

    }
    timeoutMs = 1*60*1000;
    exceptionTimer->start(timeoutMs);
    emit tipCtxSignal("设备重启中,预期大约一分钟",50);
    return true;
}
bool INTLDCConfig::excutConfigScripts()
{
    QString cmd = "/usr/local/conf/preconf/burn_product_info.sh " + HWID;

    if(isBaseProductType)
    {
        cmd.append(" --sn="+DeviceContext::get()->getSN());
        //todo:--materialCode
    }
    else
    {
        cmd.append(" --sn="+DeviceContext::get()->getCustomSN());
        cmd.append(" --materialCode="+DeviceContext::get()->getCustomMaterialCode());
    }
    if(isneedAuthKey)
    {
        cmd.append(" --authKey=" + authKey + " \n");
    }
    else
    {
        cmd.append(" \n");
    }
    excuteSSHCmd(cmd);
    return true;
}
void INTLDCConfig::writeConfig()
{
    emit tipCtxSignal("配置中",30);
    switch(currentTaskStep)
    {
        case EXCUTE_CONFIG_SCRIPTE_STEP_E:
            excutConfigScripts();
            break;
        case WAIT_DEVICE_REBOOT_STEP_E:
            waitDeviceReboot();
            break;

        default:
            break;
    }
}

bool INTLDCConfig::isValidVer(const QString & msg,const QRegularExpression &  dataReg,const QString & data)
{
    QRegularExpressionMatch match = dataReg.match(msg);
    if(match.hasMatch())
    {
       QString realVer= match.capturedRef(1).toString();
       qDebug()<<"realVer"<<realVer<<"data"<<data;
       if(data.contains(realVer))
       {
            return true;
       }
       else
       {
            return false;
       }
    }
    else
    {
        return  false;
    }
}
bool INTLDCConfig::parseDeviceVersion(const QString &msg)
{
    if(1)
    {
        if(isValidVer(msg,QRegularExpression("CORE_VER:\\s*(.*)M4"),verInfoList["A7主板"]) == false &&
           isValidVer(msg,QRegularExpression("CORE_VER:\\s*(.*)M4"),verInfoList["A53主板"]) == false)
        {
            handleErro(tr("主板版本信息错误"));
            return  false;
        }

        if(isCabinet == false)
        {
            if(isValidVer(msg,QRegularExpression("M4_VER:b\\s*(.*)SECC1"),verInfoList["M4板"]) == false)
            {
                handleErro(tr("M4版本信息错误"));
                return  false;
            }
            if(isValidVer(msg,QRegularExpression("SECC1_VER:\\s*(.*)SECC2"),verInfoList["SECC控制板"]) == false)
            {
                handleErro(tr("SECC1版本信息错误"));
                return  false;
            }
            if(isValidVer(msg,QRegularExpression("SECC2_VER:\\s*(.*)"),verInfoList["SECC控制板"]) == false)
            {
                handleErro(tr("SECC2版本信息错误"));
                return  false;
            }
        }

        updateCoreTask(END_TEST_TASKE_E);
        triggerTaskSignal();
    }
    else
    {
        handleErro(tr("版本信息错误"));
    }

    return true;
}
bool INTLDCConfig::getDeviceVersionInfo()
{
    excuteSSHCmd("/etc/getSystemConfig.sh DC_VERSION \n");
    return true;
}

void INTLDCConfig::checkConfigInfo()
{
    emit tipCtxSignal("配置校验中",80);
    switch (currentTaskStep)
    {
        case REQUSET_PRODUCT_INFO_STEP_E:
            requestInfoFromMes();
            break;
        case GET_DEVICE_VERSION_STEP_E:
            getDeviceVersionInfo();
            break;
        default:
        break;
    }
}

void INTLDCConfig::requestProductInfoSlot(bool ret)
{
    if(ret)
    {
        SSHClient * sshClient =  LinkManager::get()->getSSH(DeviceContext::get()->getDeviceLinkIP());
        if(sshClient)//TODO:增加对是否连接的判定
        {
            if(sshWorker == nullptr)
            {
                sshWorker = new SSHTestWorker();

                connect(sshClient,&SSHClient::sigDataArrived,sshWorker,&SSHTestWorker::recvSSHDataSlot,Qt::UniqueConnection);
                connect(sshWorker,&SSHTestWorker::sendDataSignal,sshClient,&SSHClient::sendData,Qt::UniqueConnection);
            }
            connect(sshWorker,&SSHTestWorker::respondDataSignal,this,&INTLDCConfig::recvSSHRespond,Qt::UniqueConnection);
        }
        if(!hasGetProductInfo)
        {
            needContinueTest = false;
            updateTaskStep(GET_PRODUCT_TYPE_STEP_E);
            updateCoreTask(CONFIRE_PRODUCT_TYPE);
            triggerTaskSignal();
        }
        else
        {
            emit productInfoSignal(isBaseProductType);
        }
    }
    else
    {
        hasGetProductInfo = false;
    }
}

void INTLDCConfig::updateGroupIdSlot(const QString & id)
{
    groupId = id;
}

void INTLDCConfig::finishedSe05xTestSlot(bool ret, const QString &error)
{
    if(certificateWorker)
    {
        delete certificateWorker;
        certificateWorker = nullptr;
    }
    if(ret)
    {
        updateTaskStep(REQUSET_PRODUCT_INFO_STEP_E);
        updateCoreTask(CHECK_DEVICE_INFO_TASK_E);
        triggerTaskSignal();
    }
    else
    {
        handleErro(error);
    }
}
void INTLDCConfig::recvProcessFinishSlot(int exitCode,const QString & msg)
{
    if(currentTaskStep == FIND_PORT_CACHE_E)
    {
        //startpOS 和endPos值需要做判定吗？
        int startPos = msg.indexOf("LISTENING");
        int endPos = msg.indexOf("TCP",startPos);
        portCacheId = msg.mid(startPos+9, endPos-startPos-13).remove(" ");

        if(portCacheId.isEmpty())
        {
            qDebug()<< "not found cach port id ";
            updateTaskStep(START_RTP_SERVER_E);

        }
        else
        {
            qDebug()<< "cach port id "<<portCacheId;
            updateTaskStep(CLEAR_PORT_CACHE_E);
        }
          triggerTaskSignal();
    }
    else if (currentTaskStep == CLEAR_PORT_CACHE_E)
    {
        updateTaskStep(START_RTP_SERVER_E);
        triggerTaskSignal();
    }
    return;
}
