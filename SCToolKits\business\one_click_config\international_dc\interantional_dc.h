#ifndef INTLDCCONFIG_H
#define INTLDCCONFIG_H

#include <QObject>
#include <QMap>
#include "business/workers/test_worker.h"
#include "workers/ssh_test_worker.h"
#include "common_share/interface_business.h"
#include "ssh/ftp_client.h"
#include "one_click_config/one_click_config_builder.h"
#include "workers/ssh_test_worker.h"
#include "workers/process_worker.h"
#include "workers/process_worker.h"
#include "security/nxp_edge_lock_go/nxp_edge_go_server.h"
#include "security/certificate_worker.h"
//international abbr INTL
class INTLDCConfig : public TestWorker,public IBusiness
{
    Q_OBJECT
public:
    INTLDCConfig();
    ~INTLDCConfig();
private:
    void getHWID();
    void confirmProductType();
    void writeConfig();
    void requestInfoFromMes();
    void checkConfigInfo();

    void finishedTest();


public slots:
    void startWorker(const QString toolName);
    void setTestTaskIndex(int index);//for debug
    void setMaintaincePattern(bool st);
    void receivHumanOperateResultSlot(bool ret);
    void requestProductInfoSlot(bool ret);
    void updateGroupIdSlot(const QString &);
    void finishedSe05xTestSlot(bool,const QString & error);
private slots:
    void processBusiness();
    void processCoreTasks();
    void hanleExceptTimeout();
    void recvProcessFinishSlot(int exitCode,const QString & msg);
//    void processMesRelySlot(bool, const QString &, const QJsonObject &);
private:
//    void processCheck(int testObjectIndex, int testOperateIndex);
//    bool isFinishedTestObjectOperate(TestObject * testObject,int operateIndex);
//    bool findNextTestOperate(int &testObjectIndex,int &operateIndex,bool needUpdate=true);
//    void resetTestEnv();
//    //
//    void requestInfoFromMes();

signals:
    void startBusinessSignal();
    void triggerTaskSignal();
    void startExecProcess(const QString &,const QStringList &);
    void endWriteSe05xSignal();
    bool humanProcessTipSignal(const QString & tip,int diplayControl=0x100|0x8|0x2,int timeout=0);//timeout 秒
    void productInfoSignal(bool isBaseProductType);

private:
    typedef enum
    {
        GET_HWID_TYPE_E,//用于获取配置执行的参数HWID.
        CONFIRE_PRODUCT_TYPE,
        REQUEST_PRODUCT_INFO_E,
        MES_REQUEST_TASK_E,

        WRITE_CONFIG_E,

        GET_SE05X_CTX_TASK_E,
        WRITE_SE05X_TASK_E,

        CHECK_DEVICE_INFO_TASK_E,
        END_TEST_TASKE_E,
        UNKOW_MAX_TASK
    }TaskType;
    typedef enum
    {
        GET_PRODUCT_TYPE_STEP_E,
        GET_MATERIAL_CODE_STEP,
        DOWN_XML_FILE_SETP_E,
        PARSE_HWID_STEP_E,
        GET_AUTHKEY_STEP_E,

        REQUSET_PRODUCT_INFO_STEP_E,

        EXCUTE_CONFIG_SCRIPTE_STEP_E,
        WAIT_DEVICE_REBOOT_STEP_E,
        WAIT_SSH_OPEND_STEP_E,

        GET_UID_STEP_E,
        REGISTER_UID_TO_ELG_E,
        GET_SE05X_CONFIG_FILE_E,

        FIND_PORT_CACHE_E,
        CLEAR_PORT_CACHE_E,
        START_RTP_SERVER_E,
        START_RTP_CLIENT_E,

        GET_DEVICE_VERSION_STEP_E,

        CHECK_DEVICE_VERSIONI_E,

        CHECK_SE05X_PRIVATE_KEY_STEP_E,
        CHECK_SE05X_CERT_STEP_E,

        NON_PAD_STEP_E,
        UNKOW_MAX_TASK_STEP_E

    }TaskStepOrder;
private:
    bool testResult;
    bool maintenacePattern;
    bool hasGetProductInfo;
    bool needContinueTest;
    QString errorReason;
    TaskType currentCoreTask;
    TaskStepOrder currentTaskStep;

    SSHTestWorker *sshWorker;
    ProcessWorker *processWorker;
    QThread processThread;

    QString  HWID;
    QString authKey;
    QString se05xUid;
    QString portCacheId;

    QString expectCoreVer;
    QString expectM4Ver;
    QString expectSECCOneVer;
    QString expectSECCTwoVer;

    QString groupId;
    QString se05xConfigFile;
    QString xmlFile;
    bool isBaseProductType;
    bool isneedAuthKey;
    bool isCabinet;

    QTimer * exceptionTimer;
    int timeoutMs;

    QMap<QString,QString>verInfoList;
    typedef void (INTLDCConfig::*taskHandler)();
    QMap<TaskType,taskHandler>coreTasks;
    CertificateWorker * certificateWorker;
private:
    void updateCoreTask(TaskType task);
    void updateTaskStep(TaskStepOrder step,bool isIncreas=true,int stepGap = 1);
    void excuteSSHCmd(const QString & cmd);
    void recvSSHRespond(const QString & sshMsg);
    void recvMesReply(bool, const QString &, const QJsonObject &);
    void handleErro(const QString & reason);
    void resetEnv(bool ret = false);
    void initTestCtx();

private:
    bool parseProductType(const QString & msg);
    bool requestInfoFromGit();
    bool getXmlInfoFromLocal();
    bool parseMaterialInfo(const QString &, const QJsonObject &);
    bool parseAuthkeyInfo(const QString &, const QJsonObject &);
    bool parseMaterialCodeInfo(const QString &, const QJsonObject &);
    bool parseHWIDFromXml();

    bool excutConfigScripts();
    bool parseConfigureResult(const QString & msg);
    bool waitDeviceReboot();
    bool waitSSHLink();
    bool handleSSHLink();
    bool dipsplayHumanOperateTip(const QString & tip);

    //
    bool getDeviceVersionInfo();
    bool parseDeviceVersion(const QString &msg);
    bool isValidVer(const QString & msg,const QRegularExpression &  dataReg,const QString & data);


};
#endif // INTLDCCONFIG_H
