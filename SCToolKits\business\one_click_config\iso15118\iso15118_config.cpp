#include "iso15118_config.h"
#include<QRegularExpression>
#include "web/web_client.h"
#include "devices/device_context.h"
ISO15118Config::ISO15118Config()
{
    connect(this,&ISO15118Config::startBusinessSignal,this,&ISO15118Config::processBusiness,Qt::QueuedConnection);
}
ISO15118Config::~ISO15118Config()
{

}
void ISO15118Config::startWorker(const QString toolName)
{
    emit displayInfoSignal(tr("关闭ISO15118中..."));
    startBusinessSignal();
}
void ISO15118Config::receWebRespondSlot(const QString & msg)
{
    QRegularExpression resultExpress("(\"status\":\\s*1\\b)");
    QRegularExpressionMatch match = resultExpress.match(msg);
    if(match.hasMatch())
    {
       qDebug()<< "set 15118 sucess";
        emit tipCtxSignal("关闭ISO15118完成",100);
        emit testResultSignal(1,tr("15118关闭成功"));
        emit finishedTestSiganl("15118",1);
    }
    else
    {
       qDebug()<< "set 15118 failed";
        emit tipCtxSignal("关闭ISO15118失败",100);
        emit testResultSignal(1,tr("15118关闭失败"));
        emit finishedTestSiganl("15118",0);
    }
    disconnect(WebClient::get(),&WebClient::postRespond,this,&ISO15118Config::receWebRespondSlot);
}
void ISO15118Config::processBusiness()
{
    bool ret = loginWeb();
    if(ret)
    {
        bool enable = false;
        set15118Enable(enable);
    }
    else
    {
        emit tipCtxSignal(tr("web 登录失败,配置终止"),progressInfo,false);
        emit tipCtxSignal("关闭ISO15118失败",100);
        emit testResultSignal(1,tr("15118关闭失败"));
        emit finishedTestSiganl("15118",0);
        return;
    }
}
bool ISO15118Config::set15118Enable(bool enable)
{
    if(enable == false)
    {
         emit tipCtxSignal(tr("关闭15118中"),progressInfo);
    }
    else
    {
        emit tipCtxSignal(tr("打开15118中"),progressInfo);
    }

    WebRequestData data;
    data.postDataType = WEB_V2_15118_EN_METHOD;
    QString info=QString("{\"mode\":%1,\"action\":\"setIso15118Mode\"}").arg(enable==true?1:0);
    data.data.append(info);

    WebClient * webclient = WebClient::get();
    webclient->postData(data);

    return true;
}
bool ISO15118Config::loginWeb()
{
    //后续主要是2.0。先做web2.0
    emit tipCtxSignal("判定web类型中",progressInfo);
//    if(WebClient::get()->getWebVersion() !=  WEB_UNKOWN_VERSION)
//    {
//        return true;
//    }
    //web 登录
    if(WebClient::get()->detectWebType())
    {
        //交流的web1.0，交流产品自身维护。2.0由资源线web团队同意维护。
        bool ret = WebClient::get()->createClient();
        if(!ret)
        {
            qDebug() << "http create failed";
            emit tipCtxSignal("web无法操作",progressInfo,false);
        }
        WebClient * webclient = WebClient::get();
        QString webIp = "**************";
        QString account;
        QString password;
        DeviceContext::get()->getWebLoginInf(account,password);

        webclient->setLoginInfo(webIp,account,password);
        ret = webclient->loginWeb();
        if(ret==false)
        {
            qDebug() << "web login erro";
            emit tipCtxSignal("web登录失败",progressInfo,false);
            return false;
        }
        connect(webclient,&WebClient::postRespond,this,&ISO15118Config::receWebRespondSlot,Qt::UniqueConnection);
    }
    else
    {
        qDebug() << "can not 侦测web 类型";
        emit tipCtxSignal("未知的web 版本",progressInfo,false);
        return false;
    }
    return  true;
}
