#include "sn_rules.h"
#include <QRegularExpression>
SNRules::SNRules()
{

}
SNRules::~SNRules()
{

}
void SNRules::setRules()
{

}

bool SNRules::parse(const QString & sn,QStringList & outResult,QString & reason)
{
    if(sn.isEmpty())
    {
        return false;
    }
    QRegularExpression regExp("^SN\\d{10,}$");
    QRegularExpressionMatch match = regExp.match(sn);
    if(match.hasMatch())
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool SNDSNRules::parse(const QString & sn,QStringList & outResult,QString & reason)
{
    QString data=sn;
    int pos=0;
    int endpos=0;
    QString customerStr;
    //查找ref=,取CR值，CR长度9-12，不定长
    pos = data.indexOf("ref=");
    customerStr = data.mid(pos+4);
    endpos = customerStr.indexOf("/");
    QString cr = customerStr.mid(0,endpos);
    if(cr.isEmpty())
    {
        reason="CR号为空，请重新扫描";
        return false;
    }
    outResult<<cr;

    //查找sn=,取SN值，CR长度12
    pos = customerStr.indexOf("sn=");
    customerStr = customerStr.mid(pos+3);
    endpos = customerStr.indexOf("/");
    QString convertSn = customerStr.mid(0,endpos);
    if(convertSn.isEmpty())
    {
        reason="SN号为空，请重新扫描";
        return false;
    }
    outResult<<convertSn;

    //查找cpid=,取cpid值，CR长度36
    pos = customerStr.indexOf("cpid=");
    QString cpid = customerStr.mid(pos+5);
    if(cpid.isEmpty())
    {
        reason="cpid号为空，请重新扫描";
        return false;
    }
    outResult<<customerStr.mid(pos+5);
    outResult<<customerStr.mid(pos+5);
    return true;
}

bool PINSNRules::parse(const QString &sn, QStringList & outResult, QString &reason)
{
    QRegularExpression regExp("^SN\\d{10}$");
    QRegularExpressionMatch match = regExp.match(sn);
    if(!match.hasMatch())
    {
        return false;
    }
    outResult<<sn;
    return true;
}

bool SCQRSNRules::parse(const QString &qrCode, QStringList &out, QString &reason)
{
    QString qrCodeStr("");
    QRegularExpression regex("https://qrcode\\.starcharge\\.com/#/XPACHOME/(\\d+)");
    QRegularExpressionMatch match = regex.match(qrCode);

    if(!match.hasMatch())
    {
        qrCodeStr = qrCode;
    }
    else
    {
        qrCodeStr = match.captured(1);
    }

    if(qrCodeStr.count() == 8)
    {
        qrCodeStr += "01";
    }
    out << qrCodeStr;
    return true;
}


bool FCTSNRules::parse(const QString &sn, QStringList &out, QString &reason)
{
    if(sn.isEmpty())
    {
        return false;
    }
    if(sn.size() == 15)
    {
        return true;
    }
    else
    {
        return false;
    }
}

SNDEVDRules::SNDEVDRules()
{

}

bool SNDEVDRules::parse(const QString &sn, QStringList &outResult, QString &reason)
{
    evdPrefixMap = InterfaceData::get()->getEvdPrefixMap();
    for(auto iter = evdPrefixMap.begin();iter != evdPrefixMap.end();iter++)
    {
        if(sn.contains(iter.key()))
        {
            outResult<<iter.value();
            return true;
        }
    }
    reason="EVD号无对应gruopID";
    return false;
}
