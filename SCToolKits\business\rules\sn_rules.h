#ifndef SNRULES_H
#define SNRULES_H

#include <QObject>
#include<QString>
#include<QStringList>
#include <QMap>
#include "data/interface_data/interface_data.h"
class SNRules
{
public:
    explicit SNRules();
    virtual ~SNRules();
    void setRules();
    virtual bool parse(const QString & sn,QStringList &out,QString & reason);
};

class SNDSNRules:public SNRules
{
public:
    virtual bool parse(const QString & sn,QStringList &out,QString & reason);
};

class PINSNRules:public SNRules
{
public:
    virtual bool parse(const QString & sn,QStringList &out,QString & reason);
};

//星星充电二维码
class SCQRSNRules:SNRules
{
public:
    virtual bool parse(const QString & qrCode,QStringList &out,QString & reason);
};

//FCT标准SN号
class FCTSNRules:SNRules
{
public:
    virtual bool parse(const QString & sn,QStringList &out,QString & reason);
};

//ECD号解析
class SNDEVDRules:public SNRules
{
public:
    SNDEVDRules();
    virtual bool parse(const QString & sn,QStringList &out,QString & reason);
private:
    QMap<QString,QString> evdPrefixMap;
};
#endif // SNRULES_H
