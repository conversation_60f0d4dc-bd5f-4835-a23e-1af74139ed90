#include "nxp_edge_go_server.h"

NXPEdgeGoServer::NXPEdgeGoServer(const QString & groudId):
    deviceGroupId(groudId),
    apiKey("SZVT3hQ8F6xsh2HR5xyBkf6ObIgFVXIu24NVvvoJtOluNph83PqRuTTZuT367jMEfaplYDNsU5UYONkNVocbZzNuyTBCoZH5lJ6i"),
    nc12("935386988472")
{
    nxpLinkInfo.groudId = deviceGroupId;
    nxpLinkInfo.apiKey = apiKey;
    nxpLinkInfo.nc12 = nc12;
}
void NXPEdgeGoServer::setNXPELinkInfo(const NXPELinkInfo & info)
{
    deviceGroupId = info.groudId;
    apiKey = info.apiKey;
    nc12= info.nc12;
}

NXPELinkInfo NXPEdgeGoServer::getNXPELinkInfo()
{
    return nxpLinkInfo;
}
void NXPEdgeGoServer::setDeviceGroudId(const QString & id)
{
    nxpLinkInfo.groudId =id;
    return;
}
QString NXPEdgeGoServer::getDeviceGroudId()const
{
    return deviceGroupId;
}
QString NXPEdgeGoServer::getApiKey()const
{
    return apiKey;
}
QString NXPEdgeGoServer::getNC12()const
{
    return nc12;
}
