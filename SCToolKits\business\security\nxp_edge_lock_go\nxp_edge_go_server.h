#ifndef NXPEDGEGOSERVER_H
#define NXPEDGEGOSERVER_H

#include<QString>
typedef struct
{
    QString groudId;
    QString apiKey;
    QString nc12;
}NXPELinkInfo;
class NXPEdgeGoServer
{
public:
    NXPEdgeGoServer(const QString & groudId);
    void setNXPELinkInfo(const NXPELinkInfo &  info);
    NXPELinkInfo getNXPELinkInfo();
    void setDeviceGroudId(const QString & );
    QString getDeviceGroudId()const;
    QString getApiKey()const;
    QString getNC12()const;
private:
    QString deviceGroupId;
    QString apiKey;
    QString nc12;//nc12 is what?
    NXPELinkInfo nxpLinkInfo;
};

#endif // NXPEDGEGOSERVER_H
