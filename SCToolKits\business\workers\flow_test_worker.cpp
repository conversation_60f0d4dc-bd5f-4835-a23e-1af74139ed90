#include <QDebug>
#include <QTimer>
#include "flow_test_worker.h"
#include "test_objects/support_test_define.h"
#include "protocol/protocol_maintainer.h"
#include "protocol/third_party_protocol.h"
#include "test_objects/pin_ciruit.h"
#include "app_start/global.h"
#include "crc.h"


FlowTestWorker::FlowTestWorker(const QString & toolName) : testObjectBuiler(nullptr),fctTestObjectBuilder(nullptr),
                               curretTestObject(nullptr),curTestObjectIndex(0),
                               curTestOperateIndex(0),curTool(toolName),
                               failedRetryMaxNumber(3),curentRetryCnt(0),respondTimer(nullptr),
                               shangLiangTimer(nullptr),timeoutMax(10000),shangLiangTimeoutMax(1000),VoltageRetryCount(0),
                               testResults(nullptr),voltageRange(0),
                               oldEquipmentCode(""),CanWorker(nullptr),
                               linkType(LINK_UNKNOW)
{
    testResults = new TestResult();

    //使用QueuedConnection方式发送。
    connect(this,&FlowTestWorker::sendHumanDataResult,this,&TestWorker::recvDataSlot,Qt::QueuedConnection);
}
FlowTestWorker::~FlowTestWorker()
{
    if(testResults)
    {
        delete testResults;
    }
}
void FlowTestWorker::createTestObjects(const QString & tool)
{
    testObjectBuiler = selectBuilder();
    if(testObjectBuiler)
    {
        fctTestObjectBuilder = dynamic_cast<FCTBoardBuilder*>(testObjectBuiler);
        if(fctTestObjectBuilder)
        {
            QStringList funList;
            fctTestObjectBuilder->getTestObjectsFunsList(funList);
            selectTestObjects(funList);

            QString equipmentCode;
            ToolKitsManager::get()->getEquipment(equipmentCode);
            testObjectBuiler->build(equipmentCode,funList);

            fctTestObjectBuilder->getTestObjectNameList(nameList);
            objectCnt = fctTestObjectBuilder->getTestObjectCount();

            if(hasAdditionalInfo)
            {
                fctTestObjectBuilder->getTaskOperateList(taskOperateList);                         // 获取测试任务的映射表
            }
        }
        else
        {
            qDebug()<< "fct builder err";
        }
    }
    return;
}

void FlowTestWorker::processHumanData(TestOperate & testOperate,int ret)
{
    //数据组装。
    //协议和操作码获取。
    //先获取测试对象，然后获取测试对象有何种操作，再根据操作，获取不同的测试内容。
    QByteArray data;
    ProtocolMaintainer::addHeader(data);
    ProtocolMaintainer::addFixedPackageInfo(data);
    //统一在发送的地方?todo使用一个类里处理messagId信息。临时放在ProtocolMaintainer中。
    ProtocolMaintainer::get()->updateMsgId(data);

    int cmd = ProtocolMaintainer::get()->getCmd(testOperate.code);
    ProtocolMaintainer::add2Bytes(cmd,data);
    {
        int dataLen = 4;
        ProtocolMaintainer::add2Bytes(dataLen,data);

        if(ret == testOperate.data)
        {
            ProtocolMaintainer::add4Bytes(testOperate.data,data);
        }
        else
        {
           ProtocolMaintainer::add4Bytes(0,data);
        }
    }
    ProtocolMaintainer::addCrc(data);

    emit sendHumanDataResult(data);
}

void FlowTestWorker::updateCurTestObject(TestObject * testObject,int testObjectindex,int operateIndex)
{
    curTestObjectIndex = testObjectindex;
    curTestOperateIndex = operateIndex;
    curretTestObject = testObject;
}

bool FlowTestWorker::isFinishedTestObjectOperate(TestObject * testObject,int operateIndex)
{
    if(testObject)
    {
        TestOperate *testOperate = nullptr;
        testObject->getTestOperate(operateIndex,&testOperate);
        if(!testOperate)
        {
            return true;
        }
        return false;
    }
    return true;
}
bool FlowTestWorker::isValidTestObject(int testObjectIndex)
{
    TestObject * testObject = testObjectBuiler->getTestObject(testObjectIndex);
    if(testObject == nullptr)
    {
        return false;
    }
    else
    {
        return true;
    }
}
bool FlowTestWorker::findNextTestOperate(int &testObjectIndex,int &operateIndex,bool needUpdate)
{
    //TODO 不更新时候 参数判定
    testObjectIndex = curTestObjectIndex;

    TestObject * testObject = testObjectBuiler->getTestObject(testObjectIndex);
    if(testObject == nullptr)
    {
        return false;
    }

    if(!needUpdate)
    {
        operateIndex = curTestOperateIndex;
        return true;
    }

    operateIndex = curTestOperateIndex + 1;
    bool finished =  isFinishedTestObjectOperate(testObject,operateIndex);
    if(finished)
    {
        //寻找新的TestObjecet;
        testObjectIndex++;
        testObject = testObjectBuiler->getTestObject(testObjectIndex);
        if(testObject == nullptr)
        {
            return false;
        }
        operateIndex = 0;
        return true;
    }
    else
    {
        return true;
    }
}

bool FlowTestWorker::processResult(RespondMsgContext & fillData)
{
    bool retResult = true;
    TestResultContextData result;
    result.testObject = testObjectBuiler->getTestObject(curTestObjectIndex);

    QMap<int, QString> additionalTestResult;
    int respondResult = fctTestObjectBuilder->getRespondResult(fillData.msgBody, additionalTestResult);

    qDebug() << "process result = "<< respondResult;

    if(respondResult)
    {
        result.result = true;
    }
    else
    {
        result.result = false;
        retResult = false;
    }

    TestOperate *testOperate = nullptr;
    (result.testObject)->getTestOperate(curTestOperateIndex,&testOperate);
    if(!testOperate)
    {
        result.testType = UNKOWN_OPERATE_CODE;
        result.result = false;
    }
    else
    {
        result.testType = testOperate->code;
    }

    result.testOperateIndex = curTestOperateIndex;

    testResults->recordTestResult(result);

    return retResult;
}

void FlowTestWorker::processMsg(RespondMsgContext & respondData)
{
    respondTimer->stop();
    //处理消息.可以多态对应的工具去处理。比如VoltageCheckWorker是继承FlowTestWorker
    //成功和失败。失败需要重试。
    bool isSuccess = processResult(respondData);

    TestObject * testObject = testObjectBuiler->getTestObject(curTestObjectIndex);
    if(testObject == nullptr)
    {
        qDebug() << "not found testObject";
        return;
    }

    fillAndSendTipContext(testObject, isSuccess ? TEST_OK : TEST_FAILED);

    if(!isSuccess)
    {
        qDebug() << curentRetryCnt;
        if(curentRetryCnt >= failedRetryMaxNumber)
        {
            qDebug() << "retry more than" << curentRetryCnt;

            //弹窗
            emit pausedTestSignal();
            emit emitTipCtxSignal("测试三次失败，请排查原因",MODAL,OK,TIPS);
            emit updateTestStatusSignal(false);
            curentRetryCnt = 0;
            bool chooseRet =  emitTipCtxSignal("是否跳过此用例，继续测试",MODAL,OKCANCEL,TIPS);

            if(chooseRet==false)
            {
                fillAndSendTipContext(testObject, TEST_FAILED);

                // 测试失败
                if(hasAdditionalInfo)
                {
                   emit sendAdditionalInfo(additionalTestInfo);
                }

                return;
            }
            else
            {
                curTestObjectIndex++;
                curTestOperateIndex=0;
                isSuccess = false;
            }
        }
        curentRetryCnt++;
    }
    else
    {
        curentRetryCnt = 0;
    }
    //for what?进度
    //那表格的更新呢？
    testResultCtxSignal(curTool,curTestObjectIndex,curTestOperateIndex,true);

    qDebug() << "curTestObjectIndex:" << curTestObjectIndex;
    qDebug() << "curTestOperateIndex:" << curTestOperateIndex;

    //发布消息处理结果。
    //下一个测试内容或者下一个测试对象，或者重试。
    int objectIndex =0;
    int operateIndex = 0;

    qDebug() << "isSuccess:" << isSuccess;

    bool find = findNextTestOperate(objectIndex,operateIndex,isSuccess);

    qDebug() << "objectIndex:" << objectIndex;
    qDebug() << "operateIndex:" << operateIndex;

    qDebug() << "find:" << find;

    if(find)
    {
        handleTestObject(objectIndex,operateIndex);
    }
    else
    {
        bool allTestResut = testResults->joinTestResult();
        emit finishedTestSiganl(curTool,allTestResut);
        resetTestObject();

        // 含有附加信息且测试失败
        if(hasAdditionalInfo && !isSuccess)
        {
           emit sendAdditionalInfo(additionalTestInfo);
        }

        qDebug()<< "finish all test result = "<<allTestResut;
    }
    return;
}
#if 0
void FlowTestWorker::processTip(TipTaskType taskType,QString &tip)
{
    QString inputValue;
    bool yesResult;
    do
    {
        if(taskType == NO_TIP_TIME_CHECK_TASK_E
                || taskType == NO_TIP_TIME_NO_CHECK_TASK_E
                || taskType == NO_TIP_NO_BLOCK_CHECK_TASK_E
                || taskType == NO_TIP_NO_BLOCK_NO_CHECK_TASK_E)
        {
            break;
        }
        if(taskType == TIP_NO_BLOCK_NO_CHECK_TASK_E )
        {
            emit sendTipCtxSignal(tip,NO,TIPS,MODELESS);
            yesResult = true;
        }
        else if (taskType == OPERATE_CHOOSE_NO_CHECK_TASK)
        {
            yesResult =  emitTipCtxSignal(tip,MODELESS,OKCANCEL,TIPS);
            break;
        }
        else
        {
            yesResult =  emitTipCtxSignal(tip,OK,TIPS,MODAL);
            yesResult = true;
        }
    } while(yesResult == false);
}
#endif
void FlowTestWorker::handleTestObject(int testIndex,int operateIndex)
{
    qDebug()<< "process testObject "<< testIndex << " operate index "<<operateIndex;
    TestObject * testObject = testObjectBuiler->getTestObject(testIndex);
    if(testObject == nullptr)
    {
        qDebug() << "not found testObject";
        return;
    }

    TestOperate *testOperate = nullptr;
    QString testName = testObject->getName();
    testObject->getTestOperate(operateIndex,&testOperate);
    if(!testOperate)
    {
        qDebug() << "not found test operate";
        return ;
    }

    updateCurTestObject(testObject,testIndex,operateIndex);

    fillAndSendTipContext(testObject, TESTING);

    QString tip("测试中，请稍等");
    auto iter = testOperate->tipContent.tipCtx.find(BEGIN_TIP_E);
    if(iter != testOperate->tipContent.tipCtx.end())
    {
        tip = iter.value();
    }

    //提示用例
    if(testOperate->tipContent.tipType == TIP_NO_BLOCK_CHECK_TASK_E)
    {
        emit emitTipCtxSignal(tip,MODELESS,NO,TIPS);
    }
    else if(testOperate->tipContent.tipType == OPERATE_CHOOSE_NO_CHECK_TASK)
    {
        bool tipResult =  emitTipCtxSignal(tip,MODAL,OKCANCEL,TIPS);
        processHumanData(*testOperate,tipResult);

        updateCurTestObject(testObject,testIndex,operateIndex);
        return;
    }
    //数据组装。
    //协议和操作码获取。
    //先获取测试对象，然后获取测试对象有何种操作，再根据操作，获取不同的测试内容。
    TestObjectType objectType = testObject->getType();
    if(objectType == COMM_TEST_OBJECT_TYPE || objectType == PIN_GPIO_TYPE)
    {
        QByteArray data;
        ProtocolMaintainer::addHeader(data);
        ProtocolMaintainer::addFixedPackageInfo(data);
        //统一在发送的地方?todo使用一个类里处理messagId信息。临时放在ProtocolMaintainer中。
        ProtocolMaintainer::get()->updateMsgId(data);

        int cmd = ProtocolMaintainer::get()->getCmd(testOperate->code);
        ProtocolMaintainer::add2Bytes(cmd,data);

        //对于GPIO例子，数据内容包括，GIPO port和需要设置的值，共8字节。
        //TODO:后期直接从 操作步骤中取数据serialData，替代单一的data。

        if(PIN_GPIO_TYPE == objectType)
        {
            PinCiruit * pin = dynamic_cast<PinCiruit*>(testObject);
            if(pin)
            {
                int port = pin->getPinPortIndex();
                int dataLen = 8;
                ProtocolMaintainer::add2Bytes(dataLen,data);
                ProtocolMaintainer::add4Bytes(port,data);
                ProtocolMaintainer::add4Bytes(testOperate->data,data);
            }
        }
        else if (COMM_TEST_OBJECT_TYPE == objectType)
        {
            int dataLen = testOperate->auxDataInfo.size();
            ProtocolMaintainer::add2Bytes(dataLen,data);
            ProtocolMaintainer::addArray(testOperate->auxDataInfo,data);
        }
        else /*if(PORT_ETHERNET_TYPE == objectType )*/
        {
            int dataLen = 4;
            ProtocolMaintainer::add2Bytes(dataLen,data);
            ProtocolMaintainer::add4Bytes(testOperate->data,data);
        }
        ProtocolMaintainer::addCrc(data);

        //记录当前测试信息。
        updateCurTestObject(testObject,testIndex,operateIndex);
        qDebug() << "send test "<<data.toHex();

        // 统一处理 由什么方式发出数据
//        emit sendDataSignal(data);
        sendDataHandle(data);
    }
    else if(objectType == COMM_CAN_TYPE)
    {
        CanSendFunParameter parameters;
        if(ToolKitsManager::get()->getCurrentToolFunction() == LIQUID_COOL_BOARD_E)
        {
            canID = 0x12010c02;
            parameters.id = canID;
            sendData.append(0x01);
            sendData.append(0x0c);
            sendData.append(0x03);
            sendData.append(0x0a);
            sendData.append(0x02);
            sendData.append(0x0b);
            sendData.append(0x04);
            sendData.append(0x0d);
            parameters.data.append(sendData);
            qDebug()<<"FlowTestWorker::handleTestObject--"<<"canID--"
                   <<QString::number(canID, 16)<<"sendData--"<<parameters.data.toHex();
            emit sendCanMsgSignal(parameters);
        }
        else if(ToolKitsManager::get()->getCurrentToolFunction() == DPAU_CONTROL_BOARD_E)
        {
            QDataStream headerStream(testOperate->auxDataInfo.left(4));
            headerStream.setByteOrder(QDataStream::BigEndian);
            headerStream >> parameters.id;
            parameters.data = testOperate->auxDataInfo.mid(4,8);
            relayNum = testOperate->auxDataInfo.toHex().mid(24,2).toInt();
            qDebug()<<"parameters.id--"<<QString::number(parameters.id, 16)
                    <<"parameters.data--"<<parameters.data.toHex()
                    <<"relayNum--"<<relayNum;
            QTimer::singleShot(500,[=]()
            {
                emit sendCanMsgSignal(parameters);
            });
        }
    }
    else if(objectType == COMM_MODBUS_TYPE)
    {
        QByteArrayList ctx;
        testOperate->getByteArryData(ctx);
        QByteArray sendData = ctx[0];

        qDebug() << "send modeus data:" << sendData.toHex();
        emit sendDataSignal(sendData);
    }
    else
    {
        QByteArray data;
        handleVoltageAcquisitionTest(*testOperate, data);
        updateCurTestObject(testObject,testIndex,operateIndex);

        qDebug() << "get voltage: "<<data.toHex();
        emit sendVoltageDataSignal(data); //板卡信号
    }

    //电压采集模块的的串口消息第一次发送有概率收不到回复，加一个一秒的小定时器，第一次收不到再发一次，三次都收不到则报错
    if(testOperate->code == SHANG_LIANG_VOLTAGE_STATUS || testOperate->code == SHANG_LIANG_VOLTAGE_RANG)
    {
        shangLiangTimer->start(shangLiangTimeoutMax);
    }
    else
    {
        respondTimer->start(timeoutMax);
    }
}

void FlowTestWorker::resetTestObject()
{
    updateCurTestObject(nullptr,0,0);
    emit updateTestStatusSignal(false);
    delete testResults;
    testResults = nullptr;

    if(testResults == nullptr)
    {
        testResults = new TestResult();
    }
}

void FlowTestWorker::handleRespondTimeout()
{
    respondTimer->stop();
    curentRetryCnt = 0;
    emit pausedTestSignal();
    emit emitTipCtxSignal("无法收到板子信息，请排查插线是否松动，或者重新点击测试，或者联系开发",MODAL,OK,TIPS);
    emit updateTestStatusSignal(false);
    return;
}

void FlowTestWorker::handleVoltageTimeout()
{
    shangLiangTimer->stop();
    VoltageRetryCount++;
    if(VoltageRetryCount < 3)
    {
        qDebug() << "VoltageRetryCount:" <<VoltageRetryCount;
        handleTestObject(curTestObjectIndex,curTestOperateIndex);

    }
    else
    {
        curentRetryCnt = 0;
        emit pausedTestSignal();
        emit emitTipCtxSignal("无法收到板子信息，请排查插线是否松动，或者重新点击测试，或者联系开发",MODAL,OK,TIPS);
        emit updateTestStatusSignal(false);
        return;
    }
}
void FlowTestWorker::startWorker(const QString tool)
{
    startTest(tool);
}
void FlowTestWorker::startTest(const QString & tool)
{
    if(respondTimer==nullptr)
    {
        respondTimer = new QTimer(this);
        connect(respondTimer, &QTimer::timeout, this, &FlowTestWorker::handleRespondTimeout);
    }
    if(shangLiangTimer == nullptr)
    {
        shangLiangTimer = new QTimer(this);
        connect(shangLiangTimer, &QTimer::timeout, this, &FlowTestWorker::handleVoltageTimeout);
    }
    QString equipmentCode;
    ToolKitsManager::get()->getEquipment(equipmentCode);
    if(oldEquipmentCode != equipmentCode)
    {
        createTestObjects(tool);
        oldEquipmentCode = equipmentCode;
    }

    handleTestObject(curTestObjectIndex,curTestOperateIndex);
}

void FlowTestWorker::fillAndSendTipContext(TestObject * testObject, int testSta)
{
    //TODO 其他的
    if(testObjectBuiler)
    {
        tipCtx.objectNameList = nameList;
        tipCtx.curObjIndex = curTestObjectIndex;
        tipCtx.curOperateIndex = curTestOperateIndex;
        tipCtx.testStatus = testSta;
        tipCtx.currentTextName = testObject->getName();
        tipCtx.objectCount = objectCnt;
        tipCtx.operateCnt = testObject->getOperateCount();
        emit sendTipContext(tipCtx);
    }

}

void FlowTestWorker::handleVoltageAcquisitionTest(TestOperate &testOperate, QByteArray &data)
{
    data.append(0x01); //ADDR

    int cmd = ThirdPartyProtocol::get()->getCmd(testOperate.code); //获取功能码
    ThirdPartyProtocol::addByte(cmd,data);

    ThirdPartyProtocol::addArray(testOperate.auxDataInfo, data);//serialData中数据要保存两字节的开始地址，两字节的寄存器个数
    ThirdPartyProtocol::addCrc(data);
}

int FlowTestWorker::parseVoltageData(QByteArray & srcMsg, RespondMsgContext & fillData)
{
    if((unsigned char)srcMsg[0] != 0x01)
    {
        return OPRT_COM_ERROR;
    }

    fillData.messageLen = (unsigned char)srcMsg[2];
    fillData.msgBody.append(srcMsg.data() + 3, fillData.messageLen);

    int checkPos = fillData.messageLen + 3;
    int checkHi = CRC16(reinterpret_cast<uint8*>(srcMsg.data()),fillData.messageLen+3,HI);
    int checkLo = CRC16(reinterpret_cast<uint8*>(srcMsg.data()),fillData.messageLen+3,LO);
    if((checkHi == (uint8)srcMsg[checkPos]) && (checkLo == (uint8)srcMsg[checkPos+1]))
    {
        return OPRT_OK;
    }
    else
    {
        return OPRT_COM_ERROR;
    }
}

void FlowTestWorker::processModbusMsg(QByteArray & data)
{
    TestObject *testObject = testObjectBuiler->getTestObject(curTestObjectIndex);
    TestOperate *testOperate = nullptr;
    testObject->getTestOperate(curTestOperateIndex,&testOperate);
    if(!testOperate)
    {
        return;
    }

    bool result = false;

    QByteArrayList dataList;
    QByteArray compareData;
    testOperate->getByteArryData(dataList);
    compareData = dataList[1];

    switch(compareData[0])  //比较类型
    {
        case VALUE_COMPARE:
        {
            int dataLen = data[2];
            QByteArray hexData = data.mid(3, dataLen);
            float floatData = hexToFloat32(hexData.toHex());

            uint realData = floatData * 1000; //放大1000倍
            uint startRange = ((uint8)compareData[1] << 24) + ((uint8)compareData[2] << 16) +
                              ((uint8)compareData[3] << 8) + (uint8)compareData[4];
            uint endRange = ((uint8)compareData[5] << 24) + ((uint8)compareData[6] << 16) +
                            ((uint8)compareData[7] << 8) + (uint8)compareData[8];

            qDebug() << "compare: " << realData << startRange << endRange;
            if(startRange <= realData && realData <= endRange)
            {
                result = true;
            }
        }
        break;
        case MESSAGE_COMPARE:
        {
            QByteArray realCompare = compareData.mid(1);
            if(compareData == data)
            {
                result = true;
            }
        }
        break;
    }

    RespondMsgContext resultMsg;
    int ret = 0;
    if(result)
    {
        ret = 1;
    }
    else
    {
        ret = 0;
    }
    ThirdPartyProtocol::add4Bytes(ret, resultMsg.msgBody);

    processMsg(resultMsg);
}

void FlowTestWorker::processCANMsg(CAN_OBJ data)
{
    RespondMsgContext result;
    int ret = -1;
    uint32_t canId = data.ID;
    BYTE canData[8];
    memcpy(canData,data.Data,data.DataLen);
    QByteArray byteArray;
    byteArray.append(reinterpret_cast<const char*>(data.Data), data.DataLen);
    if(ToolKitsManager::get()->getCurrentToolFunction() == LIQUID_COOL_BOARD_E)
    {
        if(byteArray == sendData && canID == canId)
        {
            ret = 1;
            qDebug()<<"FlowTestWorker::processCANMsg--"<<"canId--"
                   <<QString::number(canId, 16)<<"byteArray--"<<byteArray.toHex();
        }
        else
        {
            ret = 0;
        }
    }
    else if(ToolKitsManager::get()->getCurrentToolFunction() == DPAU_CONTROL_BOARD_E)
    {
        qDebug()<<"FlowTestWorker::processCANMsg--"<<"canId--"
               <<QString::number(canId, 16)<<"byteArray--"<<byteArray.toHex();
        QByteArray arrayResult = byteArray.toHex().mid(relayNum * 2, 2);
        qDebug()<<"arrayResult"<<arrayResult.toUInt();
        qDebug()<<"relayNum"<<relayNum;
        ret = arrayResult.toUInt() == 1 ? 1 : 0;
    }
    ThirdPartyProtocol::add4Bytes(ret, result.msgBody);

    processMsg(result);
    sendData.clear();
}
void FlowTestWorker::sendDataHandle(const QByteArray &sendData)
{
    if(linkType == LINK_CAN) //使用CAN发送信息
    {
        emit sendCanSiginal(sendData);
    }
    else  //使用串口或网口发送信息
    {
        emit sendDataSignal(sendData);
    }
}

void FlowTestWorker::processVoltageMsg(RespondMsgContext &respondData)
{
    //这里将拿到的电压与期待值比较得到结果
    TestObject *testObject = testObjectBuiler->getTestObject(curTestObjectIndex);
    TestOperate *testOperate = nullptr;
    testObject->getTestOperate(curTestOperateIndex,&testOperate);
    if(!testOperate)
    {
        return;
    }

    int ret = 0;
    RespondMsgContext result;
    if(testOperate->code == SHANG_LIANG_VOLTAGE_RANG)
    {
        voltageRange = (respondData.msgBody[0] << 8) + respondData.msgBody[1];
        qDebug() << "voltage range: " <<voltageRange;
        ret = 1;
    }
    else if(testOperate->code == SHANG_LIANG_VOLTAGE_STATUS)
    {
        int voltage = (((((uint8)respondData.msgBody[0] << 8) + (uint8)respondData.msgBody[1]) * voltageRange) / 10000.0) * 100;
        if(((testOperate->data - 10) <= voltage) && ((testOperate->data + 10) >= voltage)) //比较电压
        {
            ret = 1;
        }
        else
        {
            qDebug() << "Invalid voltage: " <<voltage;
            ret = 0;
        }
    }
    ThirdPartyProtocol::add4Bytes(ret, result.msgBody);

    processMsg(result); //复用这个接口，处理结果，这样就不用在单独写处理函数了，将respondData组装一个数据
}

void FlowTestWorker::recvVoltageAcquisitionData(QByteArray data)
{
    respondTimer->stop();
    shangLiangTimer->stop();
    VoltageRetryCount=0;
    qDebug()<< "shangliang voltage recv data: "<< data.toHex();
    RespondMsgContext respondData;
    int islegalData =parseVoltageData(data,respondData);
    if(islegalData != OPRT_OK)
    {
        qDebug()<< "can not process voltage data";
        return ;
    }
    processVoltageMsg(respondData);
}

void FlowTestWorker::selectTestObjects(QStringList &funList)
{
    //过滤特定机种的测试项目
    QString equipmentCode;
    ToolKitsManager::get()->getEquipment(equipmentCode);
    //维护funList
    if(equipmentCode.contains("0684") ||
       equipmentCode.contains("0697"))
    {
        funList.removeAll(CHECK_METER_COMMUNICATION);
    }
    else if(equipmentCode.contains("1274"))  //三相
    {
        funList.removeAll(CHECK_ONEPHASE_METER_CHIP);
    }
    else if(equipmentCode.contains("1365"))  //单相
    {
        funList.removeAll(CHECK_THREEPHASE_METER_CHIP);
    }
    else if(equipmentCode.contains("1474"))
    {
        funList.removeAll(CHECK_FLASH);
        funList.removeAll(CHECK_ONEPHASE_METER_CHIP);
        funList.removeAll(CHECK_THREEPHASE_METER_CHIP);
        funList.removeAll(CHECK_MAIN_RELAY);
        funList.removeAll(CHECK_PEN_RELAY);

        //追加一个1474专门的pen继电器检测用例
        funList << CHECK_ADAPTER_BOARD_PEN_RELAY;
    }
    else if(equipmentCode.contains("1164") ||
            equipmentCode.contains("0824"))
    {
        funList.removeAll(EMERGRNCY_STOP);
        funList.removeAll(CHECK_PIN_VOLTAGE);
        funList << CHECK_US_PIN_VOLTAGE;

        funList.removeAll(CHECK_METER);
        funList << CHECK_ONEPHASE_METER_CHIP;
    }
    else if(equipmentCode.contains("0941") ||
            equipmentCode.contains("1033"))
    {
        funList.removeAll(CHECK_DRY_NODE);
        funList.removeAll(CHECK_METER_COMMUNICATION);
    }
    else if(equipmentCode.contains("1279"))
    {
        funList.removeAll(CHECK_DRY_NODE);
        funList.removeAll(CHECK_METER);
        funList.removeAll(CHECK_ONEPHASE_METER_CHIP);
        funList.removeAll(CHECK_SCREEN_COMMUNICATION);
    }
    else if(equipmentCode.contains("1326")) //土星交换机板
    {
        funList.removeAll(CHECK_DSO_BOARD_GPIO);
        funList.removeAll(CHECK_DSO_BOARD_UART);
    }
    else if(equipmentCode.contains("1325")) //土星DSO板
    {
        funList.removeAll(CHECK_SWITCH_BOARD_GPIO);
        funList.removeAll(CHECK_SWITCH_BOARD_PWM);
    }
    else if(equipmentCode.contains("1281"))
    {
        funList.removeAll(CHECK_DRY_NODE);
        funList.removeAll(CHECK_FLASH);
        funList.removeAll(CHECK_ONEPHASE_METER_CHIP);
        funList.removeAll(CHECK_METER);
        funList.removeAll(EMERGRNCY_STOP);
        funList.removeAll(CHECK_CP_CIRCUIT);
    }

    return;
}

TestObjectBuilder * FlowTestWorker::selectBuilder()
{
    if(testObjectBuiler)
    {
        delete testObjectBuiler;
        testObjectBuiler = nullptr;
    }

    //这里需要做builder的区分.前置条件是必须绑定机种号
    linkType = LINK_SERIAL_PORT;
    ToolFunctionType funType = ToolKitsManager::get()->getCurrentToolFunction();
    if(funType == GD32_CORE_BOARD_E)
    {
        testObjectBuiler = new ArcCircuitBoardBuilder;
    }
    else if(funType == GD32_AC_DELAY_BOARD_E)
    {
        testObjectBuiler = new RelayBoardBuilder;
    }
    else if(funType == STEPPING_MOTOR_BOARD_E)
    {
        testObjectBuiler = new SteppingMotorBoardBuilder;
    }
    else if (funType == US_WAN_YUE_CORE_BOARD_E)
    {
        testObjectBuiler = new ArtemisCircuitBoardBuilder;
    }
    else if (funType == ENVIRONMENTAL_BOARD_E)
    {
        testObjectBuiler = new EnvironmentalBoardBuilder;
    }
    else if(funType == LIQUID_COOL_BOARD_E)
    {
        linkType = LINK_ETHERNET;
        testObjectBuiler = new LiquidCoolBoardBuilder();
    }
    else if(funType  == AC_METER_CTRL_BOARD_E)
    {
        testObjectBuiler = new ACMeterBoardBuilder();
    }
    else if(funType == SECC_BOARD_E)
    {
        testObjectBuiler = new SECCBoardBuilder();
    }
    else if(funType == SCHNEIDER_POWER_BOARD_E)
    {
        testObjectBuiler = new SchneiderPowerBoardBuilder();
    }
    else if (funType == DC_CONTROL_BOTTOM_BOARD_E)
    {
        testObjectBuiler = new DcControlBoardBuilder();
    }
    else if(funType == SCHNEIDER_CTRL_BOARD_E)
    {
        testObjectBuiler = new SchneiderCtrlBoardBuilder();
    }
    else if(funType == DC_TOP_MAIN_BOARD_E)
    {
        testObjectBuiler = new DcTopMainBoardBuilder();
    }
    else if(funType == BLUETOOTH_BOARD_E)
    {
        testObjectBuiler = new BluetoothBoardBuilder();
    }
    else if(funType == DPAU_CONTROL_BOARD_E)
    {
        testObjectBuiler = new DpauControlBoardBuilder();
    }
    else if(funType == INSULATION_BOARD_E)
    {
        testObjectBuiler = new InsulationBoardBuilder();
    }
    else if(funType == PDU_CONTROL_BOARD_E)
    {
        linkType = LINK_CAN;
        testObjectBuiler = new PDUControlBoardBuilder();
    }
    else if(funType == IONCHI_CCU_BOARD_E)
    {
        testObjectBuiler = new IonchiCcuBoardBuilder();
    }
    else if(funType == XGB_CCU_BOARD_E)
    {
        testObjectBuiler = new XgbCcuBoardBuilder();
    }
    else if(funType == TU_XING_BORAD_CHECK_E)
    {
        testObjectBuiler = new TuXingBoardBuilder();
    }
    else if(funType == SCHNEIDER_NEW_RESI_CTRL_BOARD_E)
    {
        testObjectBuiler = new SchneiderNewResiCtrlBoardBuilder();
    }
    else if(funType == DC_PRE_CHARGE_BOARD_E)
    {
        testObjectBuiler = new DcPreChargeBoardBuilder();
    }
    else if(funType == MCC_BOARD_CHECK_E)
    {
        testObjectBuiler = new MCCBoardBuilder();
    }
    else
    {
        testObjectBuiler = new CircuitBoardBuilder;
    }

    return testObjectBuiler;
}
