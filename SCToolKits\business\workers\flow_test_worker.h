#ifndef FLOWERTESTWORKER_H
#define FLOWERTESTWORKER_H

#include <QObject>
#include <QtEndian>
#include "circuit_board/am62/circuit_board_builder.h"
#include "circuit_board/arc_uk/arc_circuit_board_builder.h"
#include "circuit_board/arc_uk/relay_board_builder.h"
#include "circuit_board/artemis/artemis_circuit_board_builder.h"
#include "circuit_board/stepping_motor/stepping_motor_board_builder.h"
#include "circuit_board/environmental_board/environmental_board_builder.h"
#include "circuit_board/liquid_cool/liquid_cool_board_builder.h"
#include "circuit_board/ac_meter/ac_meter_board_builder.h"
#include "circuit_board/secc_board/secc_board_builder.h"
#include "circuit_board/dc_control_board/dc_control_board_builder.h"
#include "circuit_board/schneider_board/schneider_power_board_builder.h"
#include "circuit_board/schneider_board/schneider_ctrl_board_builder.h"
#include "circuit_board/dc_control_board/dc_top_main_board_builder.h"
#include "circuit_board/bluetooth_interface_board/bluetooth_board_builder.h"
#include "circuit_board/dpau_control_board/dpau_control_board_builder.h"
#include "circuit_board/insulation_board/insulation_board_builder.h"
#include "circuit_board/pdu_control_board/pdu_control_board_builder.h"
#include "circuit_board/ccu_board/ionchi_ccu_board_builder.h"
#include "circuit_board/ccu_board/xgb_ccu_board_builder.h"
#include "circuit_board/tuxing/tuxing_board_builder.h"
#include "circuit_board/schneider_board/schneider_new_resi_ctrl_board_builder.h"
#include "circuit_board/dc_pre_charge_board/dc_pre_charge_board_builder.h"
#include "circuit_board/mcc_board/mcc_board_builder.h"
#include "gui/tip_window.h"
#include "common_share/test_result.h"
#include "data/interface_data/interface_data.h"
#include "test_worker.h"
#include "communication/can/can_worker.h"
#include "test_objects/test_object.h"
#include "toolkits_manager.h"

#define TESTING 0
#define TEST_OK 1
#define TEST_FAILED 2
#define SENDATA 0x010a020b030c040d
typedef struct
{
    QStringList objectNameList;
    QString currentTextName;
    int testStatus;
    int objectCount;
    int curObjIndex;
    int operateCnt;
    int curOperateIndex;
}TipContext;

// 连接类型
typedef enum
{
    LINK_UNKNOW,
    LINK_SERIAL_PORT = 1,
    LINK_ETHERNET,
    LINK_CAN,
}Link_Type;

class FlowTestWorker : public TestWorker
{
    Q_OBJECT
public:
    explicit FlowTestWorker(const QString & toolName);
    ~FlowTestWorker();
public slots:
    void startTest(const QString  &);
    void startWorker(const QString toolName);
    void processMsg(RespondMsgContext &);
    void recvVoltageAcquisitionData(QByteArray data);
    void processVoltageMsg(RespondMsgContext &respondData);
    void processModbusMsg(QByteArray &data);
    int parseVoltageData(QByteArray & srcMsg, RespondMsgContext & fillData);
    void processCANMsg(CAN_OBJ data);
    void resetTestObject();

signals:
    void sendDataSignal(const QByteArray &);
    void sendVoltageDataSignal(const QByteArray &);
    void sendCanMsgSignal(const CanSendFunParameter &);
    void sendCanSiginal(const QByteArray &);
    void sendHumanDataResult(QByteArray &);

    void finishedTestSiganl(QString & toolName,bool ret);
    void pausedTestSignal();
    void updateTestStatusSignal(bool);

    void sendTipCtxSignal(QString tips, MSG_MODE mode, MSG_TYPE ntype, MSG_TIP_TYPE ntiptype);
    bool emitTipCtxSignal(QString tips, MSG_MODE mode, MSG_TYPE ntype, MSG_TIP_TYPE ntiptype);

    void testResultCtxSignal(QString,int,int,bool);
    void sendTipContext(TipContext &);

    void sendAdditionalInfo(QMap<int, QString> &additionalTestInfo);         // 更新附加信息的界面显示

private:
    void handleTestObject(int index,int);
    //TODO 声明为virtual
    bool processResult(RespondMsgContext & fillData);

private:
    void processHumanData(TestOperate & testOperate,int tipResult);

private:
    bool findNextTestOperate(int &testObjectIndex,int &operateIndex,bool needUpdate=true);
    void updateCurTestObject(TestObject * testObject,int testObjectindex,int operateIndex);
    bool isValidTestObject(int testObjectIndex);
    bool isFinishedTestObjectOperate(TestObject * testObject,int operateIndex);
    void fillAndSendTipContext(TestObject * obj, int testSta);
    void handleVoltageAcquisitionTest(TestOperate &testOperate, QByteArray &data);
    void sendDataHandle(const QByteArray & sendData);

private slots:
    void handleRespondTimeout();
    void handleVoltageTimeout();
private:
    void createTestObjects(const QString & tool);
    TestObjectBuilder* selectBuilder();
    void selectTestObjects(QStringList & funList);
private:
    //TODO:
    TestObjectBuilder *testObjectBuiler;
    FCTBoardBuilder * fctTestObjectBuilder;
    TestObject  * curretTestObject;
    int curTestObjectIndex ;
    int curTestOperateIndex ;
    QString curTool;
private:
    const int failedRetryMaxNumber;
    int curentRetryCnt;
    QTimer *respondTimer;
    QTimer *shangLiangTimer;
    const int timeoutMax;
    const int shangLiangTimeoutMax;
    int VoltageRetryCount;
private:
    TestResult * testResults;
    TipContext tipCtx;
    int voltageRange;

    int objectCnt;
    QStringList nameList;
    QString oldEquipmentCode;
    uint32_t canID;
    QByteArray sendData;
    int relayNum;
    CANWorker *CanWorker;

    Link_Type linkType;

    // 保存fct附加的测试结果
    QMap<int, QString> additionalTestResult;
};

#endif // FLOWERTESTWORKER_H
