#ifndef SQLITEMANAGER_H
#define SQLITEMANAGER_H

#include<QString>
#include<QMap>
#include<QSqlDatabase>
#include<QSqlQuery>
#include "data/interface_data/interface_data.h"
#define APP_DISPLYA_EQUIPEMNT_SEARCH_CONTROL "equipmentSearchDiplayContro"
#define LOG_OUT_FILE_CONTROL "logOutToFileFun"
#define ROOT_MODEL_CONTROL "rootModelFun"
#define TOOL_SELECT_ENALE_CONTROL "toolSelectEnableFun"
#define SSH_RECONNECT_TIME_CONTROL "sshReconnectTime"
#define SSH_PRIVATE_KEY_CONTROL "sshPrivateKey"
#define GUI_LANGUAGE_CONTROL "guiLanguageFun"
#define MES_DEPEND_CONTROL "mesDependFun"
#define AUTO_GET_COMMUNICATION_TEST_ITEM_CONTROL "autoGetCommunicationTestItem"
#define FOURTHG_LOSS_RATE_CONFIG_CONTROL "fourthGLossRateConfig"
#define TEST_SOFTWARE_VER_CONTROL "testSoftwareVerFun"
#define DC_CONFIG_FILEPATH_CONTROL "dcConfigFilepathControl"

//通过数字来区分：1：表示evcc老化前充电工作中心。2：表示evcc 老化后充电中心。
#define MES_WORKSTATION_CONTROL "mesWorkstation"
typedef enum
{
    APP_DATABASE_E=0,
    BUSINESS_SIM_DATABBASE_E=1,
    UNKOWN_DATABASE_E
}SupportDataBaseType;

//暂时放在这里，后续放专门的地方保存数据
//易火眼写卡相关数据结构
typedef struct
{
    QString workOrderNo;        //工单号
    QString cardNumber;         //卡号
    QString cardSerialNumber;   //卡序列号
    QString rule;               //写卡规则
    QString prefix;             //前缀
    QString sector;             //扇区
    QString block;              //块区
    QString time;               //写入时间
    QString result;             //写入结果
    QString name;               //写入人

    //宝马
    QString postfix;            //后缀
    QString identification;     //卡标识
}YiHuoYanData;
//易火眼工单相关数据结构
typedef struct
{
    QString workOrderNumber;    //工单号
    int cardCount;              //总卡数
    int remainingNumber;        //剩余卡数
}YiHuoYanWorkOderData;

typedef struct
{
    QString ruleName;           //规则名
    QString oldKey;             //旧密钥
    QString newKey;             //新密钥
    QString prefix;             //前缀
    QString sector;             //扇区
    QString block;              //块区
}YiHuoYanRuleData;

typedef struct
{
    QString ruleName;           //规则名
    QString oldKey;             //旧密钥
    QString newKey;             //新密钥
    QString sector;             //扇区
    QString block;              //块区
    QString prefix;             //前缀
    QString postfix;            //后缀
    QString identification;     //卡标识
}BMWRuleData;

typedef struct
{
   QString toolName;
   int funType;
   int user;
}EquipmentBindToolCtx;

//统一烧录工具记录
typedef struct
{
    QString workOrderNumber;     //工单号
    int planCount;               //工单计划数量
    int finishedCount;           //已经完成数量
}BurnWorkOderData;

typedef struct
{
    QString workOrderNumber;     //工单号
    QString programName;         //程序名称
    int finishedCount;           //该程序烧录完成数量
    int planCount;               //该程序计划烧录数量
}BurnRecordData;

class SQLiteManager
{
public:
    static SQLiteManager * get()
    {
        if(instance == nullptr)
        {
            instance = new SQLiteManager();
        }
        return instance;
    }
public:
    QSqlDatabase getDataBase(SupportDataBaseType type);
    bool createWorkOderTable();
    bool createAllCardDataTable();
    bool createRuleTable();
    bool createBMWRuleTable();
    bool createWifiConfDataTable();
    bool createNetworkTestItemTable();
    int  createNetworkTestToolTable();
    bool createBurnWorkOrderTable();
    bool createBurnRecordDataTable();
    bool createMaterialDataTable();
public:
    bool insert(const QString & ctx, int enable,const QString & aux="");//更新insert用法，如果不存在则插入，存在，则更新。
    bool insert(const QString & ctx, int enable,const QString &equipCode,const QString & aux);
    bool insert(const QString & code, const QString &toolName,int fundId,int userId=0);
    bool insert(YiHuoYanData &data);
    bool insert(YiHuoYanWorkOderData &data);
    bool insert(YiHuoYanRuleData &data);
    bool insert(BMWRuleData &data);
    bool insert(WifiConfData &data);
    bool insert(BurnWorkOderData &data);
    bool insert(BurnRecordData &data);
    bool insert(QMap<QString,int> &);
    bool insert(QString ctx,int enable ,int dataBaseHandler);
    bool insert(const QString &materialName, const QString &materialCode, const QString &bindData);
    bool update(const QString & ctx, int enable);
    bool update(const QString & table, const QString & mainKey, YiHuoYanWorkOderData &data);
    bool update(BurnRecordData &data);
    bool deleting();//不能直接命名成delete,和关键词delete冲突
    bool deleting(const QString &materialName, const QString &materialCode);
    bool select(const QString & ctx, int & enable);
    bool select(const QString & ctx, int & enable,QString & aux,QString & equipmentCode);
    bool select(QMap<QString,EquipmentBindToolCtx>&table);
    bool select(const QString & cmd, QList<YiHuoYanData> &data);
    bool select(const QString & cmd, QList<YiHuoYanWorkOderData> &data);
    bool select(const QString & cmdBody, QList<YiHuoYanRuleData> &dataList);
    bool select(const QString & cmdBody, QList<BMWRuleData> &dataList);
    bool select(WifiConfData &data);
    bool select(QMap<QString,int> &,QString tableName = "NetworkTestItem");
    bool select(const QString & cmdBody, QList<BurnRecordData> &dataList);
    bool select(const QString & cmdBody, QList<BurnWorkOderData> &dataList);
    bool select(const QString & cmdBody, QList<QStringList> &dataList);
private:
    int registerDataBase(const QString &);
private:
    QMap<SupportDataBaseType,QString>dataBaseConnections;
    QMap<int,QString>dataBaseList;
    QSqlDatabase appDB;
    const QString appControlTable;
    const QString equipmentsTable;
private:
    SQLiteManager();
    ~SQLiteManager();
private:
    bool connectAPPDataBase();
private:
    static SQLiteManager *instance;
};

#endif // SQLITEMANAGER_H
