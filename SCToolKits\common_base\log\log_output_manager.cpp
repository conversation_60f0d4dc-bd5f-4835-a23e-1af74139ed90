﻿#include "log_output_manager.h"
#include <QFile>
#include <QDateTime>
#include <QTextStream>
#include <QDebug>
LogOutputManager LogOutputManager::instance;
LogOutputManager::LogOutputManager():logFile("log.txt"),testReport("report.txt")
{
    QDir logPath ("./logs");
    if(!logPath.exists())
    {
        logPath.mkdir(logPath.absolutePath());
    }
    QString fileName = logPath.absolutePath() + QString("/%1.log").arg(QString(QDateTime::currentDateTime().toString("yyyy-MM-dd_hh_mm_ss")));
    logFile.setFileName(fileName);
    logFile.open(QIODevice::WriteOnly | QIODevice::Append);;
}
LogOutputManager::~LogOutputManager()
{

}

void LogOutputManager::outputMessage(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    QString text;
    switch (type)
    {
        case QtDebugMsg:
            text = QString("debug:");
            break;
        case QtWarningMsg:
            text = QString("warning:");
            break;
        case QtCriticalMsg:
            text = QString("critical:");
            break;
        case QtFatalMsg:
            text = QString("fatal:");
            break;
        case QtInfoMsg:
            text = QString("Info:");
            break;
    }
    const char* file = context.file ? context.file : "";
    const char* lastSlash = strrchr(file, '\\');
    const char* fileName = lastSlash ? lastSlash + 1 : file;
    QString context_info = QString("(%1:%2)").arg(QString(fileName)).arg(context.line);
    QString current_date_time = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    QString current_date = QString("[%1]").arg(current_date_time);
    QString message = QString("%1%2%3 %4").arg(current_date).arg(text).arg(context_info).arg(msg);

    QMutexLocker locker(&(instance.logMutex));
     if(instance.logFile.isOpen())
     {
        QTextStream text_stream(&(instance.logFile));
        text_stream << message << "\r\n";

        instance.logFile.flush();
     }
    return;
}

void LogOutputManager::closeLogFile()
{
    QMutexLocker locker(&logMutex);
    if(logFile.isOpen())
    {
        logFile.close();
    }
    return;
}

bool LogOutputManager::creatTestReport(QString &reportName)
{
    QDir logPath ("./logs/TestReport");
    if(!logPath.exists())
    {
        logPath.mkdir(logPath.absolutePath());
    }
//    QString fileName = logPath.absolutePath() + QString("/%1.log").arg(QString(QDateTime::currentDateTime().toString("yyyy-MM-dd_hh_mm_ss").append(reportName)));
    QString fileName = logPath.absolutePath() + QString("/%1.log").arg(reportName);
    testReport.setFileName(fileName);

    if(testReport.open(QIODevice::WriteOnly | QIODevice::Append))
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool LogOutputManager::writeReportData(QString &dataStr)
{
    QMutexLocker locker(&(instance.testReportMutex));
    if(testReport.isOpen())
    {
        QTextStream outFile(&instance.testReport);
        outFile<<dataStr<<endl;
        testReport.close();
        return true;
    }
    else
    {
        if(testReport.open(QIODevice::WriteOnly | QIODevice::Append))
        {
            QTextStream outFile(&instance.testReport);
            outFile<<dataStr<<endl;
            testReport.close();
            return true;
        }
        else
        {
            return false;
        }
    }
}

void LogOutputManager::alignCharacterWidth(QString &dataStr, int width, char cha)
{
    QFont font;
    font.setFamily("微软雅黑");
    font.setPointSize(11);
    font.setBold(false);

    QFontMetrics fontMetrics(font);
    int nFontWidth = fontMetrics.horizontalAdvance(dataStr);
    while(1)
    {
        dataStr.append(cha);
        nFontWidth = fontMetrics.horizontalAdvance(dataStr);
        if(nFontWidth >= width)
        {
            break;
        }
    }
}

bool LogOutputManager::creatLogFile(QFile &logFile, QString &logPathStr, QString &logName)
{
    QDir logPath;
    logPath.setPath(logPathStr);
    if(!logPath.exists())
    {
        logPath.mkdir(logPath.absolutePath());
    }
    QString fileName = logPath.absolutePath().append(QString("/%1").arg(logName));
    logFile.setFileName(fileName);

    if(logFile.open(QIODevice::WriteOnly | QIODevice::Append))
    {
        qDebug()<<"file opened successfully.";
        return true;
    }
    else
    {
        qDebug()<<"Failed to open log file:"<<logFile.errorString();
        return false;
    }
}

bool LogOutputManager::writeLogFile(QFile &logFile, QString &logData)
{
    QMutex mutex;
    QMutexLocker locker(&mutex);

    if(logFile.isOpen())
    {
        QTextStream outFile(&logFile);
        outFile<<logData<<endl;
        logFile.close();
        return true;
    }
    else
    {
        if(logFile.open(QIODevice::WriteOnly | QIODevice::Append))
        {
            QTextStream outFile(&logFile);
            outFile<<logData<<endl;
            logFile.close();
            return true;
        }
        else
        {
            return false;
        }
    }
}
