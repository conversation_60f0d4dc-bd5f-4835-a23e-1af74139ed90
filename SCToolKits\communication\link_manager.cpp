﻿#include <QSharedPointer>
#include "link_manager.h"
#include "communication/safety_version_manager.h"
#include "can/zlg_can/zlg_can_client.h"
#include "can/gc_can/gc_can_client.h"

class SSHCtx
{
public:
    SSHClient * client;
    QString ip;
    QString user;
    QString passwordAndCert;
    bool linked;
    QThread sshClientThread;
    QString endStr;//分割符号
};

class SerialCtx
{
public:
    SerialWorker * serial;
    QThread seralWorkThread;
    QString comName;
    int baud;
    int stopbit;
    int flowctrl;
    int databit;
    int parity;
    bool linked;
};

class CanCtx
{
public:
    CANWorker * canWorker;
    QString sn;
    CANConfigInfo *canConfigInfo;
    bool linked;

    //for new can client
    void * canDeviceInstance;
    uint deviceIndex;
    CANDeviceType  canDeviceType;
    QVector<CANClient*>canClients;
};

class TCPCtx
{
public:
   QString ip;
   int port;
   bool linked;
   QThread * thread;
};

class EthernetCtx
{
public:
   QString ip;
   int port;
   bool linked;
   QThread * thread;
   EthernetClient *client;
};
class ModbusTcpCtx
{
public:
    TCPCtx tcpBaseInfo;
    ModbusTcpClient * client;
};

LinkManager * LinkManager::instance = nullptr;

LinkManager::LinkManager(QObject *parent) : QObject(parent),
                        serial(nullptr),linked(false),canCtx(nullptr)
{
    int enable = -1;
    QString equipmentCode;
    SQLiteManager::get()->select(SSH_PRIVATE_KEY_CONTROL,enable,sshPrivateKeyPath,equipmentCode);
    if(enable != -1)
    {
        sshPrivateKeyPath = QDir::currentPath() + "/privateKey/" + sshPrivateKeyPath;
    }
    else
    {
        sshPrivateKeyPath = QDir::currentPath() + "/privateKey/ssh_login_private";
    }
    DeviceContext::get()->setSSHPrivateKey(sshPrivateKeyPath);
}
LinkManager::~LinkManager()
{
}
SerialWorker * LinkManager::createLinkWithSerial(QString comName,int baud, int stopbit, int flowctrl, int databit, int Parity)
{
    foreach(auto &var,serialCtx)
    {
        if(var->comName == comName)
        {
            if(var->linked == false)
            {
                connect(this, &LinkManager::linkSerialPortSignal, var->serial, &SerialWorker::startOpenSerial);
                emit linkSerialPortSignal(comName,baud,stopbit,flowctrl,databit,Parity);
                disconnect(this, &LinkManager::linkSerialPortSignal, var->serial, &SerialWorker::startOpenSerial);
            }
            return var->serial;
        }
    }

    SerialCtx * serialPort = new SerialCtx();
    serialPort->comName = comName;
    serialPort->baud = baud;
    serialPort->stopbit = stopbit;
    serialPort->flowctrl = flowctrl;
    serialPort->databit = databit;
    serialPort->parity = Parity;

    serialPort->serial = new SerialWorker(nullptr,Standard_Common_MSG);
    serialPort->serial->moveToThread(&serialPort->seralWorkThread);
    serialPort->seralWorkThread.start();

    connect(this, &LinkManager::linkSerialPortSignal, serialPort->serial, &SerialWorker::startOpenSerial);
    connect(this, &LinkManager::disconnectSerialLinkSignal, serialPort->serial, &SerialWorker::serialClosed);

    connect(serialPort->serial, &SerialWorker::serialOpenResultSignal, this, &LinkManager::updateSerialLinkResult);
    emit linkSerialPortSignal(comName,baud,stopbit,flowctrl,databit,Parity);

    int lastKey = 0;
    if(!serialCtx.isEmpty())
    {
        lastKey= serialCtx.lastKey() + 1;
    }
    serialCtx[lastKey] = serialPort;

    disconnect(this, &LinkManager::linkSerialPortSignal, serialPort->serial, &SerialWorker::startOpenSerial);

    return serialPort->serial;
}

void LinkManager::updateSerialLinkResult(QString comName, bool ret)
{
    foreach(auto &var,serialCtx)
    {
        if(var->comName == comName)
        {
            var->linked = ret;
            emit serialLinkResultSignal(ret);
            emit serialPortLinkResult(comName, ret);
            break;
        }
    }
}
void LinkManager::disconnectSerialLink()
{
    emit disconnectSerialLinkSignal();
}
bool LinkManager::getSerialLinkedStatus(QString  comName)const
{
    foreach(auto &var,serialCtx)
    {
        if(var->comName == comName)
        {
            return var->linked;
        }
    }
    return false;
}
SerialWorker * LinkManager::getSerail(QString  comName)const
{
    foreach(auto &var,serialCtx)
    {
        if(var->comName == comName)
        {
            return var->serial;
        }
    }
    return nullptr;
}

void LinkManager::processSSHAvailableSlot(bool ret,QString ip,QString user,int port)
{
    foreach(auto &var,sshCtx)
    {
        if(ip.contains(var->ip) && (var->user == user))
        {
            var->linked = ret;
            emit sshChannelEnableSignal(ret,ip,port);
            break;
        }
    }
}
SSHClient* LinkManager::createLinkWithSSH(const QString &ip, const QString &user,const QString &pssowrdAndcert,SSHLinkedModule linkeModule,SSHLinkedOtherModule otherMolel)
{
    foreach(auto &var,sshCtx)
    {
        if(var->ip == ip && var->user == user)
        {
            return var->client;
        }
    }

    SSHCtx * ssh = new SSHCtx;
    ssh->client =new SSHClient(ip,22,pssowrdAndcert,user);
    ssh->ip = ip;
    ssh->user = user;
    ssh->linked = false;

    //统一使用是名字为ssh_login_private的证书
    //应用层不再区分证书还是密码鉴权，统一使用LOGIN_BASE_PASSWORD_SECRET_KEY
//    QString keyPath(QDir::currentPath() + "/privateKey/ssh_login_private");
    sshModel = LOGIN_BASE_PASSWORD_SECRET_KEY;


    ssh->client->setReconnectTime(sshReconnectTime);
    ssh->client->setPrivateKey(sshPrivateKeyPath);
    ssh->client->start(sshModel);
    qDebug() <<"ssh link model"<< sshModel<<sshPrivateKeyPath;

    connect(ssh->client, &SSHClient::sshEnableSignal,this,&LinkManager::processSSHAvailableSlot);
    connect(ssh->client, &SSHClient::passwordErrorSignal,this,[this](const QString & ip,const QString & user)
    {

        emit sshLoginErrorSignal(0,ip,user);
    });
    connect(ssh->client, &SSHClient::sigConnectStateChanged,this, &LinkManager::sshConnectStateChangedSlot);
    connect(ssh->client,&SSHClient::sigDataArrived,this,&LinkManager::setSSHEndStrSlot);

    int lastKey = 0;
    if(!sshCtx.isEmpty())
    {
        lastKey= sshCtx.lastKey() + 1;
    }
    sshCtx[lastKey] = ssh;

    return ssh->client;
}
bool LinkManager::getSSHLinkedStatus(const QString & ip,const QString & user)const
{
    foreach(auto &var,sshCtx)
    {
        if(var->ip == ip && var->user == user)
        {
            return var->linked;
        }
    }
    return false;
}

SSHClient* LinkManager::getSSH(const QString & ip,const QString & user)const
{
    foreach(auto &var,sshCtx)
    {
        if(var->ip == ip && var->user == user)
        {
            return var->client;
        }
    }
    return nullptr;
}

void LinkManager::setSSHEndStrSlot(QString msg,QString ip, int port)
{
    if(!msg.contains("~") && !msg.contains("#"))
    {
        return;
    }

    foreach(auto &var,sshCtx)
    {
        if(var->ip == ip)
        {
            var->endStr = msg;
            qDebug()<<"end str="<<msg;
            disconnect(var->client,&SSHClient::sigDataArrived,this,&LinkManager::setSSHEndStrSlot);
        }
    }
}
void LinkManager::getSSHEndStr(QString & endStr,const QString &ip )
{
    foreach(auto &var,sshCtx)
    {
        if(var->ip == ip)
        {
            endStr = var->endStr;
        }
    }
}
void LinkManager::sshConnectStateChangedSlot(bool status, QString ip, int port)
{
    foreach(auto &var,sshCtx)
    {
        if(ip.contains(var->ip) && (var->ip == ip))
        {
            var->linked = status;
            break;
        }
    }
    emit sshLinkResultSignal(status, ip, port);
}

bool LinkManager::getCanLinkedStatus()const
{
    if(canCtx == nullptr)
    {
        return false;
    }
    else
    {
        return canCtx->linked;
    }
}

CANWorker* LinkManager::getCan()const
{
    if(canCtx == nullptr)
    {
        return nullptr;
    }
    else
    {
        return canCtx->canWorker;
    }
}

bool LinkManager::getEthernetLinkedStatus(const QString &ip, int port) const
{
    foreach(auto &var,ethernetCtx)
    {
        if(var->ip == ip && var->port == port)
        {
            return var->linked;
        }
    }
    return false;
}

CANWorker* LinkManager::createLinkWithCAN()
{
    if(canCtx == nullptr)
    {
        canCtx = new CanCtx;
        canCtx->canWorker = new CANWorker();
        qRegisterMetaType<CAN_OBJ>("CAN_OBJ");
    }

    canCtx->canWorker->updateChannelSlot(CanConfigInfoDatacenter::get()->getCanConfigInfo()->currentCanChannel);
    QString errorCode = canCtx->canWorker->openCanDevice((CANChannnelConfigure *)CanConfigInfoDatacenter::get()->getCanConfigInfo()->canChannnelConfig, 2);

    if(canCtx->canWorker->isCanDeviceOpen() == false)//启动设备失败
    {
        QString tip=QString("打开设备失败,错误码：");
        tip += errorCode;
        canCtx->linked = false;
        emit openCanDeviceErr(tip);
        return nullptr;

    }
    else//启动设备成功
    {
        canCtx->canWorker->start();//启动子线程//间接调用了run()函数//即接收数据
        canCtx->canConfigInfo = CanConfigInfoDatacenter::get()->getCanConfigInfo();
        canCtx->sn = canCtx->canWorker->getBoadrInfo();
        CanConfigInfoDatacenter::get()->setCanConnectStatus(true);
        CanConfigInfoDatacenter::get()->setCanSn(canCtx->sn);

        CanConfigInfoDatacenter::get()->setSendCanFrameCnt(0);
        CanConfigInfoDatacenter::get()->setRecvCanFrameCnt(0);
        canCtx->linked = true;
        emit updateCanLinkedStatus(canCtx->linked);
        return canCtx->canWorker;
    }
}

void LinkManager::recvDataProcess(CAN_OBJ data)
{
    CanConfigInfoDatacenter::get()->accumulationRecvCanFrameCnt();
    emit recvReplySignal((uint32_t)data.ID, data.Data, data.DataLen);
}

void LinkManager::disConnectCanSlot()
{
    canCtx->canWorker->closeCanDevice();
    canCtx->canWorker->stop();//停止子线程
    canCtx->linked = false;
    CanConfigInfoDatacenter::get()->setCanConnectStatus(false);
    emit updateCanLinkedStatus(canCtx->linked);
}

void LinkManager::connectHost(const QString & ip ,int port)
{
    // 对相同IP和Port判定。
   for(auto iter = ethernetCtx.begin(); iter != ethernetCtx.end();iter++)
   {
       if(iter.value()->ip == ip && iter.value()->port == port)
       {
           if(iter.value()->linked == true)
           {
               return;
           }
       }
   }

    EthernetClient * client =  new EthernetClient;
    QThread * clientThread = new QThread;
    client->moveToThread(clientThread);
    clientThread->start();

    connect(this,&LinkManager::connectHostSignal,client,&EthernetClient::startConnectHostSlot);
    connect(this,&LinkManager::disconnectHostSignal,client,&EthernetClient::disconnectedSlot);

    connect(client,&EthernetClient::connectedSignal,this,&LinkManager::ethernetConnetedSlot);
    connect(client,&EthernetClient::connectErrSignal,this,&LinkManager::ethernetConnetedErrSlot);
    connect(client,&EthernetClient::disconnectedPassivitySignal,this,&LinkManager::clearEthernetClientkContextSlot);

    int lastKey = 0;
    if(!ethernetCtx.isEmpty())
    {
        lastKey = ethernetCtx.lastKey() + 1;
    }

    EthernetCtx * ctx =  new EthernetCtx;
    ctx->ip = ip;
    ctx->port = port;
    ctx->thread = clientThread;
    ctx->client = client;
    ctx->linked = false; //赋值一个初值，避免出现还没连接上，随机出现了true的状态
    ethernetCtx[lastKey] = ctx;

    emit connectHostSignal(ip,port);
    //防止多EthernetClient收到重复的connectHostSignal
    disconnect(this,&LinkManager::connectHostSignal,client,&EthernetClient::startConnectHostSlot);
}

void LinkManager::ethernetConnetedSlot(const QString &ip,int port,bool ret)
{
    for(auto iter = ethernetCtx.begin(); iter != ethernetCtx.end();iter++)
    {
        if(iter.value()->ip == ip && iter.value()->port == port)
        {
            qDebug()<< "ip:port="<<ip<<":"<<port<<" connected ret" << ret;
            iter.value()->linked = ret;
            emit ethernetLinkedSignal(ret,ip,port);
            return;
        }
    }
    qDebug()<< "not found link ip:port="<<ip<<":"<<port;
}

void LinkManager::ethernetConnetedErrSlot(const QString &ip,int port,int err)
{
    Q_UNUSED((QAbstractSocket::SocketError)err);

    qDebug() << "---> ethernet conneted err <---";
    //刷新一下连接状态
    for(auto iter = ethernetCtx.begin(); iter != ethernetCtx.end();iter++)
    {
        if(iter.value()->ip == ip && iter.value()->port == port)
        {
            iter.value()->linked = false;
        }
    }

    emit ethernetLinkedSignal(false,ip,port);
}

void LinkManager::clearEthernetClientkContextSlot(const QString &ip,int port)
{
    for(auto iter = ethernetCtx.begin(); iter != ethernetCtx.end();iter++)
    {
        if(iter.value()->ip == ip && iter.value()->port == port)
        {
            iter.value()->thread->quit();
            iter.value()->thread->wait();
            qDebug()<< "thread exit"<<endl;
            //
            emit ethernetLinkedSignal(false,ip,port);

            free(iter.value());
            ethernetCtx.erase(iter);

            return;
        }
    }
}
void LinkManager::disconnectHost(const QString &ip,int port)
{
    EthernetClient * client = getEthernetClient(ip,port);
    if(client)
    {
        emit disconnectHostSignal();
    }
}

EthernetClient* LinkManager::getEthernetClient(const QString & ip,int port)const
{
    foreach(auto &var,ethernetCtx)
    {
        if(var->ip == ip && var->port == port)
        {
            return var->client;
        }
    }
    return nullptr;
}

SSHLoginModule LinkManager::getSshModel()
{
    return sshModel;
}
bool LinkManager::setSSHReconnectTime(int value)
{
    if(value >= 10 && value <= 100)
    {
        sshReconnectTime = value;
        //todo：ip和port
        SSHClient* client = getSSH();
        if(client)
        {
            connect(this,&LinkManager::setReconnectTimeSignal,client,&SSHClient::setReconnectTime);
//            client->setReconnectTime(value);
            emit setReconnectTimeSignal(value);
        }
        return true;
    }
    else
    {
       //todo 是否需要重置？
       sshReconnectTime = 10;
       return false;
    }

}
int LinkManager::openCanDevice(CANDeviceType deviceType)
{
    if(CANDeviceType::ZLG_CAN_FD_E == deviceType ||
       CANDeviceType::ZLG_CAN_2_E == deviceType)
    {
        QSharedPointer<CANClient> client(new ZLGCANClient());
        CANDevicePtr devicetPtr = client->openDevice(deviceType,zlgCanDeviceId);
        if(devicetPtr==nullptr)
        {
           qDebug()<< "may not plugin can device.can not open can device type "<< (uint)deviceType << "index "<< zlgCanDeviceId;
           return -1;
        }

        CanCtx * canPtr = new CanCtx();
        canPtr->canDeviceInstance = devicetPtr;
        canPtr->canDeviceType = deviceType;
        canPtr->deviceIndex = zlgCanDeviceId;
        zlgCanDeviceId++;

        int lastKey = 0;
        if(!canDeviceCtx.isEmpty())
        {
            lastKey = canDeviceCtx.lastKey() + 1;
        }
        canDeviceCtx[lastKey] = canPtr;
        qDebug()<< "open can device ok ,index ="<<zlgCanDeviceId - 1;
        return lastKey;
    }
    else if (CANDeviceType::GC_CAN_E == deviceType ||
             CANDeviceType::GC_CAN_2_E == deviceType)
    {
        QSharedPointer<CANClient> client(new GCCANClient());
        CANDevicePtr devicetPtr = client->openDevice(deviceType,gcCanDeviceId);
        if(devicetPtr==nullptr)
        {
           qDebug()<< "may not plugin can device.can not open can device type "<< (uint)deviceType << "index "<< gcCanDeviceId;
           return -1;
        }

        CanCtx * canPtr = new CanCtx();
        canPtr->canDeviceInstance = devicetPtr;
        canPtr->canDeviceType = deviceType;
        canPtr->deviceIndex = gcCanDeviceId;
        gcCanDeviceId++;

        int lastKey = 0;
        if(!canDeviceCtx.isEmpty())
        {
            lastKey = canDeviceCtx.lastKey() + 1;
        }
        canDeviceCtx[lastKey] = canPtr;
        qDebug()<< "open can device ok ,index ="<<gcCanDeviceId - 1;
        return lastKey;

    }
    else
    {
         qDebug() << "not support type "<<(uint)deviceType;
         return -1;
    }
    return -1;
}
 CANClient * LinkManager::createLinkWithCAN(int deviceFd,uint channel,void * configPtr)
 {
     auto iter  = canDeviceCtx.find(deviceFd);
     if(iter == canDeviceCtx.end())
     {
         qDebug()<< "can device not open,first call openCanDevice";
         return nullptr;
     }

     if(iter.value()->canDeviceType == CANDeviceType::ZLG_CAN_FD_E ||
        iter.value()->canDeviceType == CANDeviceType::ZLG_CAN_2_E)
     {
         CANDevicePtr devicetPtr = iter.value()->canDeviceInstance;
         CANClient * client = new ZLGCANClient(channel);
         CANChannelPtr channelPtr = nullptr;
         if(configPtr != nullptr)
         {
             channelPtr = client->initCAN(devicetPtr, channel, configPtr);
         }
         else
         {
             CANChannelInitConfig configInfo;
             memset(&configInfo,0x0,sizeof(CANChannelInitConfig));
             configInfo.can_type = 1;
             configInfo.canfd.mode = 0;
             configInfo.canfd.filter = 1;
            channelPtr = client->initCAN(devicetPtr, channel, &configInfo);
         }
         if(channelPtr == nullptr)
         {
             qDebug()<< "can not init zlg can channel"<< channel;
             client->deleteLater();
             return nullptr;
         }

         int ret = client->startCAN(channelPtr);
         if(ret != 0)
         {
             client->resetCAN(channelPtr);
             client->deleteLater();
             qDebug()<< "can not start zlg can channel"<< channel << " error code:"<<ret;
             //need  close INITCAN
             return nullptr;
         }

         iter.value()->canClients.append(client);
         qDebug()<< "create can client is ok,client channel = "<<channel<<channelPtr;
         return client;
     }
     else if (iter.value()->canDeviceType == CANDeviceType::GC_CAN_2_E)
     {
        CANClient * client = new GCCANClient();
        int ret = 0;
        if(configPtr == nullptr)
        {
            CANChannelInitConfig configInfo;
            memset(&configInfo,0x0,sizeof(CANChannelInitConfig));
            configInfo.can_type = 1;
            configInfo.can.mode = 0;
            configInfo.can.filter = 1;
            ret = client->initCAN(CANDeviceType::GC_CAN_2_E,iter.value()->deviceIndex,channel,&configInfo.can);
        }
        else
        {
            ret =client->initCAN(CANDeviceType::GC_CAN_2_E,iter.value()->deviceIndex,channel,configPtr);
        }

        if(ret !=0 )
        {
            qDebug()<< "can not init guangcheng  can channel"<< channel;
            client->deleteLater();
            return nullptr;
        }
        ret = client->startCAN(CANDeviceType::GC_CAN_E,iter.value()->deviceIndex,channel);
        if(ret !=0 )
        {
            qDebug()<< "can not start guangcheng  can channel"<< channel;
            client->deleteLater();
            return nullptr;
        }
        iter.value()->canClients.append(client);
        return client;
     }
     else
     {
         qDebug() << "can device is nullptr ";
     }
    return nullptr;
 }
void LinkManager::updateSSHLoginPassword(const QString &psw,const QString & ip,const QString & user)
{
    SSHClient *client = getSSH(ip,user);
    if(client)
    {
        client->setLoginPassword(psw);
    }
}
bool LinkManager::setSSHPrivateKeyFile(const QString & fileName)
{
    sshPrivateKeyPath = QDir::currentPath() + "/privateKey/" + fileName;
    return true;
}
ModbusTcpClient* LinkManager::getModbusTcpClient(const QString & ip,int port)const
{
    foreach(auto &var,modbusTcpCtx)
    {
        if(var->tcpBaseInfo.ip == ip && var->tcpBaseInfo.port == port)
        {
            return var->client;
        }
    }
    return nullptr;
}
void  LinkManager::disconnectModbus(const QString &ip,int port)
{
    for(auto iter = modbusTcpCtx.begin(); iter != modbusTcpCtx.end();iter++)
    {
        if(iter.value()->tcpBaseInfo.ip == ip && iter.value()->tcpBaseInfo.port == port)
        {
            disconnect(iter.value()->client,&ModbusTcpClient::connectStatus,this,&LinkManager::processModubsTcpConnetedSlot);
            disconnectModbusSignal(ip,port);
            iter.value()->tcpBaseInfo.thread->quit();

            free(iter.value());
            modbusTcpCtx.erase(iter);

            return;
        }
    }
}
int LinkManager::connectModbus(const QString & ip ,int port)
{
    //对相同IP和Port判定。
   for(auto iter = modbusTcpCtx.begin(); iter != modbusTcpCtx.end();iter++)
   {
       if(iter.value()->tcpBaseInfo.ip == ip && iter.value()->tcpBaseInfo.port == port)
       {
           if(iter.value()->tcpBaseInfo.linked == true)
           {
               return iter.key();
           }
       }
   }

    ModbusTcpClient * client =  new ModbusTcpClient;
    QThread * clientThread = new QThread;
    client->moveToThread(clientThread);
    clientThread->start();

    connect(this,&LinkManager::connectModbusSignal,client,&ModbusTcpClient::connectModbus);
    connect(this,&LinkManager::disconnectModbusSignal,client,&ModbusTcpClient::disconnectModbus);

    connect(client,&ModbusTcpClient::connectStatus,this,&LinkManager::processModubsTcpConnetedSlot);

    int lastKey = 0;
    if(!modbusTcpCtx.isEmpty())
    {
        lastKey = modbusTcpCtx.lastKey() + 1;
    }

    ModbusTcpCtx * ctx =  new ModbusTcpCtx;
    ctx->tcpBaseInfo.ip = ip;
    ctx->tcpBaseInfo.port = port;
    ctx->tcpBaseInfo.thread = clientThread;
    ctx->client = client;
    ctx->tcpBaseInfo.linked = false; //赋值一个初值，避免出现还没连接上，随机出现了true的状态
    modbusTcpCtx[lastKey] = ctx;

    emit connectModbusSignal(ip,port);
    //防止多EthernetClient收到重复的connectHostSignal
    disconnect(this,&LinkManager::connectModbusSignal,client,&ModbusTcpClient::connectModbus);
    return lastKey;
}

void LinkManager::processModubsTcpConnetedSlot(const QString &ip,int port,bool ret,const QString & reason)
{
    for(auto iter = modbusTcpCtx.begin(); iter != modbusTcpCtx.end();iter++)
    {
        if(iter.value()->tcpBaseInfo.ip == ip && iter.value()->tcpBaseInfo.port == port)
        {
            qDebug()<< "modbus tcp ip:port="<<ip<<":"<<port<<" connected ret" << ret;
            iter.value()->tcpBaseInfo.linked = ret;
            emit modbusTcpLinkedSignal(ret,ip,port,reason);
            return;
        }
    }
    qDebug()<< "not found link ip:port="<<ip<<":"<<port;
}
