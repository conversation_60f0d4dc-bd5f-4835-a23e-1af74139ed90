﻿#ifndef LINKMANAGER_H
#define LINKMANAGER_H

#include <QObject>
#include <QThread>
#include "serial/serial_worker.h"
#include "ssh/ssh_client.h"
#include "can/can_worker.h"
#include "ethernet/ethernet_client.h"
#include "data/can_config/canconfiginfo_datacenter.h"
#include "can/can_client.h"
#include "modbus/modbus_tcp_client.h"
#include "common_base/database/sqlite_manager.h"
#include "data/devices/device_context.h"
#include <QMap>
#include <QDir>
//前置申明
class SSHCtx;
class SerialCtx;
class CanCtx;
class EthernetCtx;
class ModbusTcpCtx;

typedef enum
{
    //可以组合使用
    SERIAL_PORT_LINK = 1,
    SSH_LINKE = (1<< 1),
    ETHERNET_LINK = (1<<2),
    CAN_LINK = (1<<3),
    MAX_LINK_DEVICE_TYPE=(1<<4)
}LinkDeviceType;

typedef enum
{
    SSH_LINKED_BY_PASSWORD_E = 0,//密码登录
    SSH_LINKED_BY_CERT_E,//证书登录
    SSH_LINKED_BY_PASSWORD_CERT_E,//密码或者证书或者密码加证书
}SSHLinkedModule;

typedef enum
{
    SSH_LINKED_BY_ALTERNAT, //交流
    SSH_LINKED_BY_DIRECT //直流
}SSHLinkedOtherModule;

class LinkManager : public QObject
{
    Q_OBJECT
public:
   static LinkManager* get()
    {
        if(instance == nullptr)
        {
            instance = new LinkManager();
        }
        return instance;
    }
public:
    //for serial port
    bool getSerialLinkedStatus(QString  comName = "")const;
    SerialWorker * getSerail(QString  comName = "")const;
    //for ssh
    bool getSSHLinkedStatus(const QString & ip="**************",const QString & user="root")const;
    SSHClient *getSSH(const QString & ip="**************",const QString & user="root")const;
    void getSSHEndStr(QString & endStr,const QString &ip = "**************");

    //for CAN
    bool getCanLinkedStatus()const;
    CANWorker* getCan()const;

    //for ethernet
    bool getEthernetLinkedStatus(const QString & ip="**************",int port=12233)const;
    EthernetClient *getEthernetClient(const QString & ip="**************",int port=12233)const;

    ///for modbustcp
    ModbusTcpClient * getModbusTcpClient(const QString & ip="**************",int port=12233)const;

    //for check
    SSHLoginModule getSshModel();

    //
    bool setSSHReconnectTime(int);
    bool setSSHPrivateKeyFile(const QString &);
public slots:
    //for serial
    SerialWorker * createLinkWithSerial(QString comName,int baud, int stopbit, int flowctrl, int databit, int Parity);
    void updateSerialLinkResult(QString, bool);
    void disconnectSerialLink();
    //for ssh
    SSHClient* createLinkWithSSH(const QString & ip = "**************", const QString & user="root",const QString & pssowrdAndcert="dh123",
                                 SSHLinkedModule linkeModule=SSH_LINKED_BY_PASSWORD_E, SSHLinkedOtherModule otherModel=SSH_LINKED_BY_ALTERNAT);
    void processSSHAvailableSlot(bool,QString ip,QString user,int port);

    void sshConnectStateChangedSlot(bool, QString, int);

    void setSSHEndStrSlot(QString msg,QString ip, int port);
    void updateSSHLoginPassword(const QString & password,const QString & ip="**************",const QString & user="root");

    //for CAN
    CANWorker * createLinkWithCAN();
    int openCanDevice(CANDeviceType deviceType);
    CANClient * createLinkWithCAN(int deviceFd,uint channel,void * configPtr =nullptr);
    void recvDataProcess(CAN_OBJ);
    void disConnectCanSlot();

    //for tcp conncet
    void connectHost(const QString & ip ,int port);
    void ethernetConnetedSlot(const QString &ip,int port,bool);
    void ethernetConnetedErrSlot(const QString &ip,int port,int);
    void clearEthernetClientkContextSlot(const QString &ip,int port);
    void disconnectHost(const QString & ip ,int port);

    //for modbus tcp connect;
    int connectModbus(const QString & ip ,int port);
    void disconnectModbus(const QString &ip,int port);
    void processModubsTcpConnetedSlot(const QString &ip,int port,bool ret,const QString & reason);

private:
    explicit LinkManager(QObject *parent = nullptr);
    ~LinkManager();
signals:
   void linkSerialPortSignal(QString ,int , int , int , int , int );
   void serialLinkResultSignal(bool);
   void serialPortLinkResult(QString, bool);
   void disconnectSerialLinkSignal();

   void sshLinkResultSignal(bool, QString, int);
   void sshChannelEnableSignal(bool,QString ,int);
   void sshLoginErrorSignal(int errortype,const QString & ip="**************",const QString & user="root");//0:密码错误

   //for CAN
   void openCanDeviceErr(QString);
   void updateCanLinkedStatus(bool);
   void displayRecvFrameSignal(QString str);
   void recvReplySignal(uint, unsigned char *, uint8_t);
   void displaySendFrameSignal(QString);

   //for ethernet
   void connectHostSignal(const QString &,int);
   void disconnectHostSignal();
   void ethernetLinkedSignal(bool,const QString &,int port);

   //for modbustcp
   void connectModbusSignal(const QString &,int);
   void disconnectModbusSignal(const QString &,int);
   void modbusTcpLinkedSignal(bool,const QString &,int port,const QString & error="" );

   void setReconnectTimeSignal(int value);
private:
    static LinkManager * instance;
    //serial context
    SerialWorker * serial;
    bool linked;
    QThread seralWorkThread;
    QMap<int,SSHCtx*>sshCtx;
    QMap<int,SerialCtx*>serialCtx;
    QMap<int,EthernetCtx*>ethernetCtx;
    QMap<int,ModbusTcpCtx*>modbusTcpCtx;
    //QMap<int,CanCtx*>canCtx;//暂时先注销，只使用一个can，暂时还未找到在打开CAN启动前可进行区分的方式
    CanCtx * canCtx;
    QMap<int,CanCtx*>canDeviceCtx;
    SSHLoginModule sshModel;
    int sshReconnectTime = 10;

    //can device Id
    int zlgCanDeviceId=0;
    int gcCanDeviceId=0;
    QString sshPrivateKeyPath;
};

#endif // LINKMANAGER_H
