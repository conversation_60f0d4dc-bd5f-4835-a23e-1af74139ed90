﻿#include "serial_worker.h"
#include <QDebug>
#include <QtEndian>
SerialWorker::SerialWorker(QObject *parent, SerialMsgType type)
    : QObject(parent),sPort(nullptr),serialPortOpened(false),
    readFirst(true),remainLen(1),curLen(0),readLen(0),totalLen(0),bigEnd(true)
{
    if(sPort == nullptr)
    {
        sPort = new QSerialPort(this);
    }
    QObject::connect(sPort, SIGNAL(readyRead()),this, SLOT(serialReadSlot()));
    QObject::connect(sPort, &QSerialPort::errorOccurred, this, &SerialWorker::handleErrorOccurred);

    setSerialMsgType(type);
}
SerialWorker::~SerialWorker()
{
    delete sPort;
    sPort = nullptr;
}

void SerialWorker::setSerialMsgType(SerialMsgType type)
{
    curSerialType = type;
    switch(type)
    {
        case SECC_MSG:
        {
            headLen = 7;
            checkLen = 0;
            dataLenIndex = 6;
            dataLenBitNum = 2;
            endLen = 0;
            break;
        }
        case SECC_RKN_MSG:
        {
            headLen = 4;
            checkLen = 4;
            dataLenIndex = 3;
            dataLenBitNum = 1;
            endLen = 0;
            break;
        }
        case Infrared_MSG:
        {
            headLen = 14;
            checkLen = 1;
            dataLenIndex = 13;
            dataLenBitNum = 1;
            endLen = 1;
            break;
        }
        case MeterVerify_MSG:
        {
            headLen = 5;
            checkLen = 2;
            dataLenIndex = 4;
            dataLenBitNum = 2;
            endLen = 1;
            break;
        }
        case Light_COLOR_CHECK_MSG:
        {
            headLen = 6;
            checkLen = 2;
            dataLenIndex = 5;
            dataLenBitNum = 2;
            endLen = 1;
            break;
        }
        case Write_Card_MSG:
        {
            headLen = 4;
            checkLen = 0;//因为长度包含的校验位
            dataLenIndex = 2;
            dataLenBitNum = 2;
            endLen = 0;
            bigEnd = false;
            break;
        }
        case SHANG_LIANG_VOLTAGE_MSG:
        {
            headLen = 3;
            checkLen = 2;
            dataLenIndex = 2;
            dataLenBitNum = 1;
            endLen = 0;
            bigEnd = false;
            break;
        }
        case Standard_Common_MSG:
        {
            headLen = 10;
            checkLen = 2;
            dataLenIndex = 8;
            dataLenBitNum = 2;
            endLen = 0;
            bigEnd = false;
            break;
        }
        case MODBUS_RTU_MSG:
        {
            headLen = 3;
            checkLen = 2;
            dataLenIndex = 2;
            dataLenBitNum = 1;
            endLen = 0;
            break;
        }
        case GSH_METER_MSG:
        {
            headLen = 6;
            checkLen = 2;
            dataLenIndex = 4;
            dataLenBitNum = 2;
            endLen = -3;
            bigEnd = false;
            break;
        }
        case BYTE_ONE_BY_ONE_MSG:
        {
            remainLen = 128;//及其特殊处理
            break;
        }
        break;
    }
}

void SerialWorker::startOpenSerial(QString comName,int baud, int stopbit, int flowctrl, int databit, int Parity)
{
    sPort->setPortName(comName);
    if(sPort->open(QIODevice::ReadWrite))
    {
        //TODO 丰富 业务和设置分开
        if(baud)
        {
            sPort->setBaudRate(baud);
        }

        switch (databit)
        {
            case 8:
            sPort->setDataBits(QSerialPort::Data8);
            break;
            case 7:
            sPort->setDataBits(QSerialPort::Data7);
            break;
            case 6:
            sPort->setDataBits(QSerialPort::Data6);
            break;
            case 5:
            sPort->setDataBits(QSerialPort::Data5);
            break;
            default:
            break;
        }

        switch (Parity)
        {
            case 0:
            sPort->setParity(QSerialPort::NoParity);
            break;
            case 2:
            sPort->setParity(QSerialPort::EvenParity);
            break;
            case 3:
            sPort->setParity(QSerialPort::OddParity);
            break;
            case 4:
            sPort->setParity(QSerialPort::SpaceParity);
            break;
            case 5:
            sPort->setParity(QSerialPort::MarkParity);
            break;
            default:
            break;
        }

        switch(stopbit)
        {
            case 1:
            sPort->setStopBits(QSerialPort::OneStop);
            break;
            case 3:
            sPort->setStopBits(QSerialPort::OneAndHalfStop);
            break;
            case 2:
            sPort->setStopBits(QSerialPort::TwoStop);
            break;
            default:
            break;
        }

        switch (flowctrl)
        {
            case 0:
            sPort->setFlowControl(QSerialPort::NoFlowControl);
            break;
            case 1:
            sPort->setFlowControl(QSerialPort::HardwareControl);
            break;
            case 2:
            sPort->setFlowControl(QSerialPort::SoftwareControl);
            break;
            default:
            break;
        }
        emit serialOpenSignal(true);
        emit serialOpenResultSignal(sPort->portName(), true);
        serialPortOpened = true;
    }
}
void SerialWorker::serialOpen()
{
    serialPortOpened = true;
}

void SerialWorker::serialClosed()
{
    serialPortOpened = false;
    sPort->close();
    emit serialOpenSignal(false);
    emit serialOpenResultSignal(sPort->portName(), false);
}

void SerialWorker::handleErrorOccurred(QSerialPort::SerialPortError error)
{
    qDebug() << "serial link error code: " << sPort->portName() << error;
    //当前遇到报错就需要断开串口的连接，否则串口无法使用
    if((error != QSerialPort::SerialPortError::NoError) && serialPortOpened)
    {
        serialClosed();
    }
}

void SerialWorker::serialReadSlot()
{
    int dataLen = 0;
    int characterCount = 0;
    QByteArray tempDate;
    if(serialPortOpened)
    {
        while(sPort->bytesAvailable())
        {
            tempDate = sPort->read(remainLen);
            // 瑞凯诺需要特殊处理
            if((curSerialType == SECC_RKN_MSG) && readFirst && tempDate[0] != 'G')
            {
                continue;
            }
            if(curSerialType == BYTE_ONE_BY_ONE_MSG)
            {
                emit transformNetMsg(tempDate);
                readFirst = true;
                remainLen = 128;
                curLen = 0;
                readLen = 0;
                totalLen = 0;
                recvDate.clear();
                dataLen = 0;
                continue;
            }

            readFirst = false;
            recvDate.append(tempDate);
            curLen = tempDate.length();
            readLen += curLen;

            // 读卡器协议特殊处理
            if(curSerialType == Write_Card_MSG)
            {
                characterCount = tempDate.count(0xaa);
                if(characterCount > 0)
                {
                    totalLen += characterCount;
                    remainLen = characterCount;
                    continue;
                }
            }
            else if(curSerialType == GSH_METER_MSG)  //瑞银电表
            {
                characterCount = tempDate.count(0xDB);
                if(characterCount > 0)
                {
                    totalLen += characterCount;
                }
            }

            if(readLen == headLen)
            {
                //qDebug() << "recvDate: " <<recvDate.toHex();
                if(curSerialType == Standard_Common_MSG)
                {
                    QByteArray standerdHead = "\xFF\x6A\xA6";
                    if(((unsigned char)recvDate[0] != (unsigned char)0xff) ||
                      ((unsigned char)recvDate[1] != (unsigned char)0x6a) ||
                      ((unsigned char)recvDate[2] != (unsigned char)0xa6))
                    {
                        int ret = recvDate.indexOf(standerdHead);
                        if(ret < 0)
                        {
                            curLen = 0;
                            readLen = 0;
                            recvDate.clear();
                        }
                        else
                        {
                            recvDate.remove(0, ret);
                            curLen = recvDate.length();
                            readLen = curLen;
                        }
                        remainLen = 1;
                        totalLen = 0;
                        dataLen = 0;
                        continue;
                    }
                }

                if(curSerialType == MODBUS_RTU_MSG)
                {
                    switch(recvDate[1]) //Modbus操作码
                    {
                        case MODBUS_STD_READ_REG:
                        {
                            dataLen = (unsigned char )recvDate[dataLenIndex];
                        }
                        break;
                        case MODBUS_WRITE_SINGLE_REG:
                        {
                            dataLen = 3;
                        }
                        break;
                        case MODBUS_STD_WRITE_REG:
                        {
                            dataLen = 3;
                        }
                        break;
                    }
                    totalLen = dataLen + headLen + checkLen + endLen;
                    remainLen = dataLen + checkLen + endLen;
                    continue;
                }

                int index = dataLenIndex;
                for(int i = 0; i < dataLenBitNum; i++)
                {
                    if(bigEnd)
                    {
                        dataLen += (unsigned char )recvDate[index--] << (0x8 * i);
                    }
                    else
                    {
                        dataLen += (unsigned char )recvDate[index++] << (0x8 * i);
                    }
                }

                totalLen = dataLen + headLen + checkLen + endLen;
                remainLen = dataLen + checkLen + endLen;
                continue;
            }
            else if(readLen < headLen)
            {
                remainLen = headLen - readLen;
                continue;
            }

            if(readLen == totalLen)
            {
                emit transformNetMsg(recvDate);
                readFirst = true;
                remainLen = 1;
                curLen = 0;
                readLen = 0;
                totalLen = 0;
                recvDate.clear();
                dataLen = 0;
                qDebug()<<"serial recv data end";
                break;
            }
            else
            {
                remainLen = totalLen - readLen;
            }
        }
    }
}
#include<QDateTime>
void SerialWorker::sendDataSlot(const QByteArray & msg)
{
    if(serialPortOpened)
    {
        sPort->write(msg);
        emit toSendDataSignal(msg);
    }
}
void SerialWorker::serialSendSlot(QByteArray msg)
{
    if(serialPortOpened)
    {
        sPort->write(msg);
        emit toSendDataSignal(msg);
        qDebug()<<"serial send data end";
    }
}
