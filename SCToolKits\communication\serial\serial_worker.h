#ifndef SERIAL_WORKER_H
#define SERIAL_WORKER_H
#include <QObject>
#include <QtSerialPort/qserialport.h>
#include <QtSerialPort/QSerialPortInfo>
#include "data/protocol/modbus_protocol.h"

enum SerialMsgType
{
    SECC_MSG = 1,
    SECC_RKN_MSG,
    Infrared_MSG,
    MeterVerify_MSG,
    Light_COLOR_CHECK_MSG,
    Write_Card_MSG,
    SHANG_LIANG_VOLTAGE_MSG,
    Standard_Common_MSG,
    MODBUS_RTU_MSG,
    GSH_METER_MSG,//直流使用的一款电表类型。具体名称未知。只知道缩写是GSH
    BYTE_ONE_BY_ONE_MSG,//用于毫无规律的串口消息，只能一个个接收，而且无法推动改动。最大按照128字节来读取。
};

class SerialWorker: public QObject {
    Q_OBJECT
public:
    explicit SerialWorker(QObject *parent = nullptr, SerialMsgType type = SECC_MSG);
    ~SerialWorker();

public slots:
    void serialOpen();
    void serialClosed();
    void serialReadSlot();
    void serialSendSlot(QByteArray);//逐步放弃，使用sendDataSlot
    void startOpenSerial(QString ,int, int, int, int, int);

    void sendDataSlot(const QByteArray &);
    void setSerialMsgType(SerialMsgType type);
    void handleErrorOccurred(QSerialPort::SerialPortError error);
signals:
    void serialOpenSignal(bool);
    void serialOpenResultSignal(QString, bool);
    void serialClose();
    void transformNetMsg(QByteArray &);
    void toSendDataSignal(QByteArray);

private:
    QSerialPort * sPort;
    bool serialOpened;
    bool serialPortOpened;
    bool readFirst;
    int headLen;
    int checkLen;
    int dataLenIndex;
    int dataLenBitNum;
    int remainLen;
    int curLen;
    int readLen;
    int totalLen;
    int endLen;
    bool bigEnd;
    SerialMsgType curSerialType;

    QByteArray recvDate;
};

#endif // SERIAL_WORKER_H
