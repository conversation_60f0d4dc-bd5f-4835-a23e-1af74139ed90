/**************************************************************************
**
** This file is part of QSsh
**
** Copyright (c) 2012 LVK
**
** Contact: <EMAIL>
**
** GNU Lesser General Public License Usage
**
** This file may be used under the terms of the GNU Lesser General Public
** License version 2.1 as published by the Free Software Foundation and
** appearing in the file LICENSE.LGPL included in the packaging of this file.
** Please review the following information to ensure the GNU Lesser General
** Public License version 2.1 requirements will be met:
** http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html.
**
**************************************************************************/

#include "ftp_client.h"

#include <QtDebug>
#include <QFileInfo>

SecureFileUploader::SecureFileUploader(QObject *parent) :
    QObject(parent), m_connection(nullptr)
{
}
SecureFileUploader::~SecureFileUploader()
{
    if(m_connection)
    {
       delete m_connection;
    }
}
void SecureFileUploader::upload(const QString &localFile, const QString &dest, const QString &host,
                                const QString &username, const QString &passwd,SSHLoginModule loginModule ,QString keyFile)
{
    operation = uploadType;//适配新接口

    QFileInfo info(localFile);

    m_localFilename = info.filePath();
    qDebug()<< "m_localFileName" << m_localFilename;
    m_remoteFilename = dest + "/" + info.fileName();

    QSsh::SshConnectionParameters params;
    params.setHost(host);
    params.setUserName(username);
    params.setPassword(passwd);
    params.authenticationType = QSsh::SshConnectionParameters::AuthenticationTypePassword;
    if(loginModule == LOGIN_SECRET_KEY)
    {
        params.authenticationType = QSsh::SshConnectionParameters::AuthenticationTypePublicKey;
        params.privateKeyFile=keyFile;
    }
    else
    {
        params.authenticationType = QSsh::SshConnectionParameters::AuthenticationTypeTryAllPasswordBasedMethods;
        params.privateKeyFile = keyFile;
    }
    params.timeout = 30;
    params.setPort(22);

    m_connection = new QSsh::SshConnection(params, this); // TODO free this pointer!

    connect(m_connection, SIGNAL(connected()), SLOT(onConnected()));
    connect(m_connection, SIGNAL(error(QSsh::SshError)), SLOT(onConnectionError(QSsh::SshError)));

    qDebug() << "SecureUploader: Connecting to host" << host;

    m_connection->connectToHost();
}

void SecureFileUploader::transmit(const QString &localFile, const QString &dest, const QString &host,
                                  const QString &username, const QString &passwd,SSHLoginModule loginModule,
                                  QString keyFile, OperateType type)
{
    operation = type;

    QFileInfo info(localFile);

    m_localFilename = info.filePath();
    qDebug()<< "m_localFileName" << m_localFilename;
    m_remoteFilename = dest + "/" + info.fileName();

    QSsh::SshConnectionParameters params;
    params.setHost(host);
    params.setUserName(username);
    params.setPassword(passwd);
    params.authenticationType = QSsh::SshConnectionParameters::AuthenticationTypePassword;
    if(loginModule == LOGIN_SECRET_KEY)
    {
        params.authenticationType = QSsh::SshConnectionParameters::AuthenticationTypePublicKey;
        params.privateKeyFile=keyFile;
    }
    params.timeout = 30;
    params.setPort(22);

    m_connection = new QSsh::SshConnection(params, this); // TODO free this pointer!

    connect(m_connection, SIGNAL(connected()), SLOT(onConnected()));
    connect(m_connection, SIGNAL(error(QSsh::SshError)), SLOT(onConnectionError(QSsh::SshError)));

    qDebug() << "SecureUploader: Connecting to host" << host;

    m_connection->connectToHost();
}

void SecureFileUploader::onConnected()
{
    qDebug() << "SecureUploader: Connected";
    qDebug() << "SecureUploader: Creating SFTP channel...";

    m_channel = m_connection->createSftpChannel();

    if (m_channel)
    {
        connect(m_channel.data(), SIGNAL(initialized()),SLOT(onChannelInitialized()));
        connect(m_channel.data(), &QSsh::SftpChannel::finished,this,&SecureFileUploader::onOpfinished);

        m_channel->initialize();

    }
    else
    {
        qDebug() << "SecureUploader: Error null channel";
    }

    return;
}

void SecureFileUploader::onConnectionError(QSsh::SshError err)
{
    qDebug() << "SecureUploader: Connection error" << err;
    emit updateDownloadProcessResult("connect error");
    m_connection->deleteLater();
    //IS RIGHT?
    m_connection = nullptr;
}

void SecureFileUploader::onChannelInitialized()
{
    qDebug() << "SecureUploader: Channel Initialized";

    // TODO Choose the overwrite mode: SftpOverwriteExisting, SftpAppendToExisting, SftpSkipExisting
    QSsh::SftpJobId job;
    if(operation == uploadType)
    {
        job = m_channel->uploadFile(m_localFilename, m_remoteFilename,QSsh::SftpOverwriteExisting);
    }
    else
    {
        job = m_channel->downloadFile(m_remoteFilename, m_localFilename,QSsh::SftpOverwriteExisting);
    }

    if (job != QSsh::SftpInvalidJob)
    {
        qDebug() << "SecureUploader: Starting job #" << job;
    }
    else
    {
        qDebug() << "SecureUploader: Invalid Job";
        emit updateDownloadProcessResult("Invalid Job error");
    }
}

void SecureFileUploader::onChannelError(const QString &err)
{
    qDebug() << "SecureUploader: Error: " << err;
    emit updateDownloadProcessResult("channel error");
}
#if defined(SSH_SECURITY_VERSION) && SSH_SECURITY_VERSION == 1
    void SecureFileUploader::onOpfinished(QSsh::SftpJobId job, const QSsh::SftpError errorType , const QString &error )
#else
    void SecureFileUploader::onOpfinished(QSsh::SftpJobId job, const QString & error)
#endif
{
    qDebug() << "SecureUploader: Finished job #" << job << ":" << (error.isEmpty() ? "OK" : error);
    emit endDownloadSignal(job, error.isEmpty() ? "OK" : error);
}





