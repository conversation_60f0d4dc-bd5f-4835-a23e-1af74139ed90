#include "ssh_client.h"
#include <QDebug>
#include <QDateTime>
SSHClient::SSHClient(QString strIp, int nPort, QString strPwd, QString strUser):m_pTimer(nullptr),reconnectTime(10000)
{
    m_strIp = strIp;
    m_nPort = nPort;
    m_strUser = strUser;
    m_strPwd = strPwd;
    m_strIpPort = m_strIp + ":" + QString::number(m_nPort);

}
 
void SSHClient::start(SSHLoginModule  login)
{
    loginModule = login;
    m_pThread = new QThread();
    connect(m_pThread,SIGNAL(finished()),this,SLOT(slotThreadFinished()));
    this->moveToThread(m_pThread);
    m_pThread->start();
 
    //之后的逻辑都得通过信号和槽接通
    connect(this,SIGNAL(sigInitForClild()),this,SLOT(slotInitForClild()));
    emit sigInitForClild();
}
 
void SSHClient::unInit()
{
    m_pThread->quit();
}
 
int SSHClient::send(QString strMessage)
{
    qDebug()<<"SSHClient ssh send "<<strMessage;
 
    int nSize = 0;
    if(m_bConnected && m_bSendAble)
    {
       nSize = m_shell->write(strMessage.toLatin1().data());
    }
    else
    {
       qDebug()<<"SSHClient::send() ssh not connect or shell not ready:"<<getIpPort();
    }
 
    return nSize;
}
 
SSHClient::~SSHClient()
{
    if(nullptr != m_pSshSocket)
    {
        delete m_pSshSocket;
        m_pSshSocket = nullptr;
	}
}
 
void SSHClient::slotResetConnection(QString strIpPort)
{
    if(this->getIpPort() == strIpPort)
    {
        this->slotDisconnected();
    }
}
 
void SSHClient::slotSend(QString strIpPort, QString strMessage)
{
    if(0 != m_strIpPort.compare(strIpPort))
    {
        return;
    }
 
    send(strMessage);
}
 
void SSHClient::slotSendByQByteArray(QString strIpPort, QByteArray arrMsg)
{
    if(0 != m_strIpPort.compare(strIpPort))
    {
        return;
    }
 
    if(m_bConnected)
    {
       m_shell->write(arrMsg);
    }
    else
    {
       qDebug()<<"SSHClient::send(QString strMessage) send failed not conect host port:"<<getIpPort();
    }
}
 
void SSHClient::slotInitForClild()
{
    m_argParameters.setHost(m_strIp);
    m_argParameters.setPort(m_nPort);
    m_argParameters.setUserName(m_strUser);
    m_argParameters.setPassword(m_strPwd);


    m_argParameters.timeout = 10;
    m_argParameters.authenticationType = QSsh::SshConnectionParameters::AuthenticationTypePassword; //密码方式连接
 
    if(loginModule == LOGIN_SECRET_KEY)
    {
        m_argParameters.authenticationType = QSsh::SshConnectionParameters::AuthenticationTypePublicKey; //密码方式连接
        m_argParameters.privateKeyFile = keyFile; //密码方式连接
    }
    if(loginModule == LOGIN_BASE_PASSWORD_SECRET_KEY)
    {
        m_argParameters.authenticationType = QSsh::SshConnectionParameters::AuthenticationTypeTryAllPasswordBasedMethods; //双因子登录或者密码登录
        m_argParameters.privateKeyFile = keyFile; //密码方式连接
    }

    slotCreateConnection(); //连接
    if(m_pTimer==nullptr)
    {
        m_pTimer = new QTimer();
        m_pTimer->setInterval(reconnectTime);
        connect(m_pTimer,SIGNAL(timeout()),this,SLOT(slotCreateConnection()));
    }

    //m_pTimer->start();//启动心跳定时器，每隔一段时间进入slotCreateConnection判断是否需要重连

}
 
int socketNum=0;
void SSHClient::slotCreateConnection()
{
    if(m_pTimer)
    {
        m_pTimer->stop();
    }
    QDateTime time = QDateTime::currentDateTime();
    qDebug();
    qDebug()<<"SSHClient::slotCreateConnection start connect--" << time;

    if(true == m_bConnected)
    {
        return;
    }
 
    if(nullptr == m_pSshSocket)
    {
        m_pSshSocket = new QSsh::SshConnection(m_argParameters);
        connect(m_pSshSocket,SIGNAL(connected()),SLOT(slotConnected()));
        connect(m_pSshSocket,SIGNAL(error(QSsh::SshError)),SLOT(slotSshConnectError(QSsh::SshError)));
    }
    qDebug() << "m_pSshSocket创建次数" << ++socketNum;
    m_pSshSocket->connectToHost();
    qDebug()<<"SSHClient::slotCreateConnection() with ssh connect port:"<<getIpPort();
}
 
void SSHClient::slotConnected()
{
    qDebug()<<"SSHClient::slotConnected ssh connect success:"<<getIpPort();
    m_pTimer->stop();
 
    m_shell = m_pSshSocket->createRemoteShell();
    connect(m_shell.data(), SIGNAL(started()), SLOT(slotShellStart()));
    connect(m_shell.data(), SIGNAL(readyReadStandardOutput()), SLOT(slotDataReceived()));
    connect(m_shell.data(), SIGNAL(readyReadStandardError()), SLOT(slotShellError()));
    m_shell.data()->start();
 
    m_bConnected = true;
    emit sigConnectStateChanged(m_bConnected,m_strIp,m_nPort);
}
 
void SSHClient::slotDisconnected()
{
    m_pSshSocket->disconnectFromHost();
}
 
void SSHClient::slotThreadFinished()
{
    m_pTimer->stop();
    m_pTimer->deleteLater();
    m_pThread->deleteLater();
    this->deleteLater();
    if(nullptr != m_pSshSocket)
    {
        delete m_pSshSocket;
        m_pSshSocket = nullptr;
}
}
 
void SSHClient::slotSshConnectError(QSsh::SshError sshError)
{
    m_bSendAble = false;
    m_bConnected = false;
    emit sigConnectStateChanged(m_bConnected,m_strIp,m_nPort);
    emit sshEnableSignal(m_bSendAble,m_strIpPort,m_strUser,m_nPort);

    m_pTimer->stop();
    QDateTime time = QDateTime::currentDateTime();
    qDebug()<<"SSHClient::slotSshConnectError--" << time;
    switch(sshError)
    {
    case QSsh::SshNoError:
        qDebug()<<"slotSshConnectError SshNoError"<<getIpPort();
        break;
    case QSsh::SshSocketError:
        qDebug()<<"slotSshConnectError SshSocketError"<<getIpPort(); //拔掉网线是这种错误
        break;
    case QSsh::SshTimeoutError:
        qDebug()<<"slotSshConnectError SshTimeoutError"<<getIpPort();
        break;
    case QSsh::SshProtocolError:
        qDebug()<<"slotSshConnectError SshProtocolError"<<getIpPort();
        break;
    case QSsh::SshHostKeyError:
        qDebug()<<"slotSshConnectError SshHostKeyError"<<getIpPort();
        break;
    case QSsh::SshKeyFileError:
        qDebug()<<"slotSshConnectError SshKeyFileError"<<getIpPort();
        break;
    case QSsh::SshAuthenticationError:
        qDebug()<<"slotSshConnectError SshAuthenticationError"<<getIpPort();
        emit passwordErrorSignal(m_strIp,m_strUser);
        break;
    case QSsh::SshClosedByServerError:
        qDebug()<<"slotSshConnectError SshClosedByServerError"<<getIpPort();
        break;
    case QSsh::SshInternalError:
        qDebug()<<"slotSshConnectError SshInternalError"<<getIpPort();
        break;
    default:
        break;
    }

    if(sshError != QSsh::SshNoError)
    {
        if(nullptr != m_pSshSocket)
        {
            m_pSshSocket->disconnectFromHost();
            m_pSshSocket->deleteLater();
            m_pSshSocket = nullptr;
        }
    }

    m_pTimer->start();
}
 
void SSHClient::slotShellStart()
{
    m_bSendAble = true;
    emit startShellSuccessSignal();
    emit sshEnableSignal(m_bSendAble,m_strIpPort,m_strUser,m_nPort);
    qDebug()<<"SSHClient::slotShellStart Shell has connected"<<getIpPort();
}
 
void SSHClient::slotShellError()
{
    qDebug()<<"SSHClient::slotShellError Shell error port"<<getIpPort();
}
 
void SSHClient::sendData(const QString & msg)
{
    send(msg);
}
void SSHClient::slotSend(QString strMessage)
{
    send(strMessage);
}
 
void SSHClient::slotDataReceived()
{
    QByteArray byteRecv = m_shell->readAllStandardOutput();
    QString strRecv = QString::fromUtf8(byteRecv);
 
    if(!strRecv.isEmpty()) //过滤空行
    {
        emit sigDataArrived(strRecv, m_strIp, m_nPort);
    } 
}

void SSHClient::setReconnectTime(int time)
{
    reconnectTime = time*1000;;
    if(m_pTimer)
    {
        //todo:确认是否需要停止？
        m_pTimer->stop();
        m_pTimer->setInterval(reconnectTime);
        m_pTimer->start();
    }
}

void SSHClient::setPrivateKey(const QString & key)
{
    keyFile = key;
}

void SSHClient::setLoginPassword(const QString & password)
{
    if(password.isEmpty())
    {
        return;
    }
    m_strPwd=password;
    m_argParameters.setPassword(password);

    //更新后，立马触发
    emit sigInitForClild();
}
