#ifndef CCONNECTIONFORSSHCLIENT_H
#define CCONNECTIONFORSSHCLIENT_H
 
/* Func:以用户密码的形式连接ssh服务器  发送命令到shell执行  需加\n
 * Note:定时检查连接状态的逻辑是  连接成功关闭定时器检查
 *      连接断开  开启定时器检查并尝试重连  直至连接成功
 *      即关闭定时器检查
 * Use:绑定两个信号
 *      检测状态:sigConnectStateChanged(bState,strIp,nPort);
 *      接收信息:sigDataArrived(QString strMsg,QString strIp, int nPort);
 *     绑定一个槽
 *      发送信息:void slotSend(QString strMsg);
 */
#include "sshconnection.h"
#include <sshremoteprocess.h>
#include <sftpchannel.h>
#include <QTimer>
#include <QHostAddress>
#include <QThread>
 
typedef enum
{
    LOGIN_PASSWORD,
    LOGIN_SECRET_KEY,
    LOGIN_BASE_PASSWORD_SECRET_KEY,//双因子鉴定或者只进行密码鉴定或者证书
}SSHLoginModule;

class  SSHClient : public QObject
{
    Q_OBJECT
public:
    explicit SSHClient(QString strIp, int nPort = 22,QString strPwd = "dh123",QString strUser = "root");
 
    void start(SSHLoginModule loginModule);
 
    ~SSHClient();
private:
    QThread *m_pThread = nullptr;
    bool m_bConnected = false;
    bool m_bIsFirstConnect = true;
    bool m_bSendAble = false;
 
    QTimer *m_pTimer;
 
    QString m_strIp = "";
    int m_nPort = -1;
    QString m_strUser;
    QString m_strPwd;
    QString m_strIpPort;
    int reconnectTime;     //连接状态心跳
 
    QSsh::SshConnectionParameters m_argParameters;
    QSsh::SshConnection *m_pSshSocket = nullptr;
    QSharedPointer<QSsh::SshRemoteProcess> m_shell;
signals:
    void sigInitForClild();
    void sigConnectStateChanged(bool bState,QString strIp,int nPort);
    void sigDataArrived(QString strMsg,QString strIp, int nPort);
    void startShellSuccessSignal();
    void sshEnableSignal(bool ret,QString ip,QString user,int port);
    void sshChannelEnableResultSignal(bool ret);
    void passwordErrorSignal(const QString & ip,const QString & user);
private:
    int send(QString strMessage);
    QString getIpPort(){return m_strIp + ":" + QString::number(m_nPort);}
public slots:
    void slotResetConnection(QString strIpPort);
    void slotSend(QString strIpPort,QString strMessage);
    void slotSend(QString strMsg);
    void slotSendByQByteArray(QString strIpPort,QByteArray arrMsg);
    void slotDisconnected();
    void slotDataReceived();
    void unInit();

    void setReconnectTime(int );
    void setPrivateKey(const QString & keyPath);
    void setLoginPassword(const QString & password);
public slots:
    void  sendData(const QString & );
private slots:
    void slotInitForClild();
    void slotCreateConnection();
    void slotConnected();
 
    void slotThreadFinished();
 
    void slotSshConnectError(QSsh::SshError sshError);
    void slotShellStart();
    void slotShellError();
private:
    SSHLoginModule loginModule;
    QString keyFile;
};
 
#endif // CCONNECTIONFORSSHCLIENT_H
 
