#include "bess_cabinet_controller.h"
#include "common_share/test_manager.h"
#include "mes/mes_manager.h"


BESSCabinetController::BESSCabinetController():
    toolName("bessCabinetTool")
{
    // 新建测试任务
    TestManager * testManager = TestManager::get();

    // 提示信息
    connect(this, &BESSCabinetController::tipCtxSignal, testManager, &TestManager::tipCtxSignal);
    connect(this, &BESSCabinetController::closeTipSignal, testManager, &TestManager::closeTipSignal);
    connect(testManager, &TestManager::slectTestStatusSignal, this, &BESSCabinetController::checkV2ResultSlot);

    MesManager *mesInstance = MesManager::get();
    connect(mesInstance, &MesManager::uploadFinishSignal, this, &BESSCabinetController::uploadResultSlot);
    // connect(mesInstance,&MesManager::checkV2FinshedSignal,this,&BESSCabinetController::checkV2ResultSlot);

    LinkManager * linkManager = LinkManager::get();

//    // 修改SSH连接的相关信息
//    DeviceContext::get()->setDeviceLinkIP("**************");
//    DeviceContext::get()->setSSHLoginUser("root");
//    DeviceContext::get()->setSSHLoginPassword("4rF@j6*X0nC&hR9mK%lP3sZ^jO8tY6aU");

    connect(linkManager, &LinkManager::sshLinkResultSignal, this, &BESSCabinetController::processSSHLinkSta);

    // 开始任务
    connect(this, &BESSCabinetController::startTest, testManager, &TestManager::startTestSlot);
}

void BESSCabinetController::showWindow(QWidget * parent)
{
    buildWindow();
    buildBusiness();

    bessCabinetWindow->show();
}


QWidget * BESSCabinetController::buildWindow(QWidget * parent)
{
    if( bessCabinetWindow== nullptr)
    {
        bessCabinetWindow = new BESSCabinetWindow(parent);
        connectWindowWithController();
    }

    return bessCabinetWindow;
}

bool BESSCabinetController::buildBusiness()
{
    if(bessCabinetWorker == nullptr)
    {
        bessCabinetWorker = new BESSCabinetWorker();
        connectWorkerWithController();
    }

    TestManager::get()->addTestWorker(bessCabinetWorker);

    // 关闭检测
    TestManager::get()->updateCrossMistakeProofing(BESS_SECURE_CORRESPONDENCE_E,false);
    TestManager::get()->updateSelectWorkerCtx(BESS_SECURE_CORRESPONDENCE_E);

    return true;
}



void BESSCabinetController::connectWindowWithController()
{
    connect(this, &BESSCabinetController::updateSSHSignal, bessCabinetWindow, &BESSCabinetWindow::updateSSHStatus);
    connect(bessCabinetWindow, &BESSCabinetWindow::closeSSHRequest, this, &BESSCabinetController::startTestSlot);
}

void BESSCabinetController::connectWorkerWithController()
{
    connect(bessCabinetWorker, &BESSCabinetWorker::testResultSignal, this, &BESSCabinetController::processTestResultSlot);
}

void BESSCabinetController::startTestSlot()
{

    bool mesLogSta = MesManager::get()->isMesLogined();
    if(!mesLogSta)
    {
        emit tipCtxSignal("请先登录MES！", MODAL, OK, TIPS);
        return;
    }

    if(!sshStatus)
    {
        emit tipCtxSignal("请先连接SSH！", MODAL, OK, TIPS);
        return;
    }

    if(!snCheckResult)
    {
        emit tipCtxSignal("请输入正确的SN号！", MODAL, OK, TIPS);
        return;
    }

    qDebug() << "加密并关闭SSH";

    emit startTest(toolName);

}


void BESSCabinetController::processTestResultSlot(bool ret)
{
    if(ret)
    {
        emit tipCtxSignal("SSH断开成功", MODAL, OK, TIPS);
        emit updateSSHSignal(!ret);
    }
    else
    {
       emit tipCtxSignal("SSH断开失败", MODAL, OK, TIPS);
    }
}


void BESSCabinetController::uploadResultSlot(bool success, const QString &message)
{
    if(success)
    {
        qDebug() << "MES上传成功";
        emit tipCtxSignal("MES上传成功", MODAL, OK, TIPS);

        snCheckResult = false;
        QString snCode = DeviceContext::get()->getSN();

        QString tipContent = QString("%1测试完成，上传MES成功！").arg(snCode);
        emit tipCtxSignal(tipContent, MODAL, OK, TIPS);

        // 清理测试数据
        TestManager::get()->clearSnCodeSignal();

    }
    else
    {
        qDebug() << "MES上传失败:" << message;
        QString errorMsg = QString("MES上传失败！\n原因：%1").arg(message);
        emit tipCtxSignal(errorMsg, MODAL, OK, WARNING);
    }
}

void BESSCabinetController::checkV2ResultSlot(bool sta)
{
     snCheckResult = sta;
}

void BESSCabinetController::processSSHLinkSta(bool sta, const QString &ip, int port)
{
    sshStatus = sta;
    emit updateSSHSignal(sta);
}


