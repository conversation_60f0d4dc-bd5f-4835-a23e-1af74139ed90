#ifndef BESSCABINETCONTROLLER_H
#define BESSCABINETCONTROLLER_H

#include "interface_controller.h"
#include "tip_controller.h"
#include "bess_cabinet/bess_cabinet_window.h"
#include "bess_cabinet/bess_cabinet_worker.h"

class BESSCabinetController: public IController
{
    Q_OBJECT
public:
    void showWindow(QWidget * parent=nullptr);
    QWidget * buildWindow(QWidget * parent=nullptr);

signals:
    void startTest(QString &);
    bool tipCtxSignal(QString tips, MSG_MODE mode, MSG_TYPE msgType, MSG_TIP_TYPE tipType);
    void closeTipSignal();
    void updateSSHSignal(bool status);


public slots:
    void startTestSlot();
    void processTestResultSlot(bool ret);
    void uploadResultSlot(bool success, const QString &message);
    void checkV2ResultSlot(bool sta);
    void processSSHLinkSta(bool sta, const QString &ip, int port);


private:
    bool buildBusiness();
    void connectWindowWithController();
    void connectWorkerWithController();

public:
    BESSCabinetController();

private:
    TipController * tipController;
    BESSCabinetWindow *bessCabinetWindow = nullptr;
    BESSCabinetWorker *bessCabinetWorker = nullptr;
    QString toolName;

    bool sshStatus = false;
    bool snCheckResult = false;

};

#endif // BESSCABINETCONTROLLER_H
