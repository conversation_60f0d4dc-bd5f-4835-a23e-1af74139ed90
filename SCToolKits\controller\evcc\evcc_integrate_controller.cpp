#include <QSharedPointer>
#include "evcc_integrate_controller.h"
#include "common_share/test_manager.h"
#include "mes/mes_manager.h"
#include "tip_controller.h"
EVCCIntegrateController::EVCCIntegrateController()
{

}
void EVCCIntegrateController::getControlWindowInfo(bool &setMax, bool &hideMes, bool &hideCom, bool &hideSn, bool &hideCan)
{
    setMax=false;
    hideMes=false;
    hideCom=true;
    hideSn=true;
    hideCan= true;
}
QWidget * EVCCIntegrateController::buildWindow(QWidget *parent)
{
    if(evccWindow == nullptr)
    {
        evccWindow = new EVCCIntegrateWindow(parent);
    }

    return evccWindow;
}
void EVCCIntegrateController::showWindow(QWidget *parent)
{
    buildWindow();
    buildBusiness();
    connectWorkerWithWindow();
    evccWindow->show();
    return;
}

bool EVCCIntegrateController::buildBusiness()
{
    if(evccChargeFct == nullptr)
    {
        evccChargeFct = new EVCCChargeFCT();
        TestManager::get()->setMultiDevicesParallelModel();
        int workStaionId = -1;
        SQLiteManager::get()->select(MES_WORKSTATION_CONTROL,workStaionId);
        if(workStaionId != -1)
        {
            MesManager::get()->setMesWorkstion(workStaionId);
        }
        TestManager::get()->addTestWorker(evccChargeFct);
        int enable;
        QString ver;
        QString other;
        SQLiteManager::get()->select(TEST_SOFTWARE_VER_CONTROL,enable,ver,other);
        evccChargeFct->setVersion(ver);
    }
    return true;
}

void EVCCIntegrateController::connectWorkerWithWindow()
{
    if(evccChargeFct && evccWindow)
    {
        connect(evccWindow,&EVCCIntegrateWindow::startTestSignal,this,&EVCCIntegrateController::handleStart);
        connect(evccChargeFct,&EVCCChargeFCT::displayInfoSignal,evccWindow,&EVCCIntegrateWindow::displayInteractionInfoSlot);
        connect(evccChargeFct,&EVCCChargeFCT::testResultSignal,evccWindow,&EVCCIntegrateWindow::displayResultInfoSlot);
        connect(evccChargeFct,&EVCCChargeFCT::testResultSignal,this,&EVCCIntegrateController::handleTestResult);

        connect(this,&EVCCIntegrateController::startBusiniessSignal,evccChargeFct,&EVCCChargeFCT::startTestSlot);
        connect(this,&EVCCIntegrateController::resetGuiSignal,evccWindow,&EVCCIntegrateWindow::resetGuiSlot);
        TestManager * testManger = TestManager::get();
        connect(testManger,&TestManager::mesUploadInfoSignal,evccChargeFct,&EVCCChargeFCT::handleMesUploadInfo);
    }
}

void EVCCIntegrateController::handleStart()
{
    if(MesManager::get()->getMesWorkstation().isEmpty())
    {
        QSharedPointer<TipController> tipController(new TipController(evccWindow->parentWidget()));
        tipController->showTipWindowSlot("请联系生产线调试长选择工作中心",MODAL,OK,TIPS);

        QTimer::singleShot(10000,evccWindow,[&](){emit resetGuiSignal();});
        return;
    }
    emit startBusiniessSignal();
}
void EVCCIntegrateController::handleTestResult(int ret,const QString & reason)
{
    if(ret == -1)
    {
        QTimer::singleShot(10000,evccWindow,[&](){emit resetGuiSignal();});
    }
    else if (ret == 0)
    {
        QTimer::singleShot(5000,evccWindow,[&](){emit resetGuiSignal();});
    }
}
int EVCCIntegrateController::syncConfigInfoSlot(ConfigInfosType type, int intValue, QString stringValue )
{
    if(ConfigInfosType::EVCC_CAN_DEVICES_NUM_E == type)
    {
        bool ret = evccChargeFct->setCANDeviceNum(intValue);
        if(ret == false)
        {
            QSharedPointer<TipController> tip = QSharedPointer<TipController>(new TipController(this->evccWindow->parentWidget()));
            tip->showTipWindowSlot("不支持测试后更改工装数量。请关闭上位机重新设置",MODAL,OK,TIPS);
        }
        else
        {
            QSharedPointer<TipController> tipController = QSharedPointer<TipController>(new TipController(this->evccWindow->parentWidget()));
            tipController->showTipWindowSlot("设置成功",MODELESS,OK,TIPS);
            QTimer::singleShot(1000,[&]()
            {
                tipController->closeTipWinowSlot();
            });
        }
    }
    else if (ConfigInfosType::EVCC_TEST_SOFTWARE_VER_E == type)
    {
        evccChargeFct->setVersion(stringValue);
    }

    return 0;
}
