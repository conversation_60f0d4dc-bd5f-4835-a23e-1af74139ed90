#ifndef EVCCINTEGRATECONTROLLER_H
#define EVCCINTEGRATECONTROLLER_H

#include <QObject>
#include "interface_controller.h"
#include "evcc/evcc_integrate_window.h"
#include "evcc/evcc_charge_fct.h"
class EVCCIntegrateController : public IController
{
    Q_OBJECT
public:
    EVCCIntegrateController();
    virtual void showWindow(QWidget *parent = nullptr) override;
    virtual QWidget * buildWindow(QWidget *parent = nullptr) override;
    virtual bool buildBusiness()override;
    virtual void getControlWindowInfo(bool &setMax, bool &hideMes, bool &hideCom, bool &hideSn, bool &hideCan) override;
signals:
    void resetGuiSignal();
    void startBusiniessSignal();
public slots:
    void handleTestResult(int ret,const QString & reason);
    void handleStart();
    virtual int syncConfigInfoSlot(ConfigInfosType, int intValue, QString stringValue = "") override;
private:
    void connectWorkerWithWindow();
private:
    EVCCIntegrateWindow * evccWindow =nullptr;
    EVCCChargeFCT * evccChargeFct = nullptr;

};

#endif // EVCCINTEGRATECONTROLLER_H
