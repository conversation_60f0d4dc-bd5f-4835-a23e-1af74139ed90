#ifndef INTERFACE_HANDLER_H
#define INTERFACE_HANDLER_H
#include <QObject>
#include <QWidget>

class IController;
class IHandler: public QObject
{
    Q_OBJECT
public:
    IHandler(IController* c=nullptr,QWidget *parentWindow=nullptr):QObject(parentWindow),parentController(c)
    {

    }
    virtual ~IHandler(){};
public:
    virtual int start(const QString & ctx ="")=0;
protected:
    IController * parentController {nullptr};

};

#endif // INTERFACE_HANDLER_H
