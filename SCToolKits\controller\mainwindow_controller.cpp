﻿#include "mainwindow_controller.h"
#include "gui/mainwindow.h"
#include "toolkits_manager.h"
#include "gui/secc/secc_mac/mac_window.h"
#include "controller_manager.h"
#include "business/common_share/test_manager.h"
#include "mes/mes_manager.h"
#include "tip_controller.h"
#include "controller/menu/setting_menu_controller.h"
#include "controller/menu/equipment_menu_controller.h"
#include "controller/menu/help_controller.h"
#include "controller/menu/config_controller.h"
#include "controller/menu/debug_controller.h"
#include "app_info.h"
#include "devices/device_context.h"
#include "link_manager.h"
#include "units_aux/tip_speaker.h"
#include "rules/material_rules.h"
MainwindowController::MainwindowController(IController * curToolController)
                     : curWorkController(curToolController),toolSelectable(true),
                       serialPortPtr(nullptr),
                       mesLoginWindow(nullptr),
                       commWindow(nullptr),
                       settingMenuController(nullptr),
                       equipmentMenuController(nullptr),
                       helpMenuController(nullptr),
                       configMenuController(nullptr),
                       debugController(nullptr),
                       linkDeviceType(SERIAL_PORT_LINK),
                       isCustomLinkMethod(false),
                       canCfgWindow(nullptr)
{
    TestManager * testManager =  TestManager::get();
    connect(testManager,&TestManager::switchTest,this,&MainwindowController::updateToolSlot);
    connect(this,&MainwindowController::snInputFinishedSignal, testManager,&TestManager::processSNSlot);
    connect(this,&MainwindowController::materialCodeInputFinishedSignal, testManager,&TestManager::processMaterialSlot);
    connect(testManager,&TestManager::clearSnCodeSignal,this,&MainwindowController::clearSNCodeSignal);
    connect(this,&MainwindowController::tipWindowSignal,testManager,&TestManager::tipCtxSignal);
    connect(this,&MainwindowController::updateToolResultSignal,testManager,&TestManager::updateCurrentToolName);
    //
    TipSpeaker * tipSpeaker = TipSpeaker::get();
    connect(this,&MainwindowController::tipSpeakSingal,tipSpeaker,&TipSpeaker::speekSlot);
}
MainwindowController::~MainwindowController()
{

}
void MainwindowController::setToolSelectable(bool attrSelect)
{
    toolSelectable = attrSelect;
}
void MainwindowController::setWorkController(IController *controller)
{
    if(controller)
    {
        curWorkController = controller;
        emit workControllerChangedSignal();
    }
}
void MainwindowController::updateToolSlot(const QString & toolName)
{
    QString toolKeyName = ToolKitsManager::get()->getToolEnglishName(toolName);
    if(toolKeyName.isEmpty())
    {
        emit updateToolResultSignal(toolName,false);
        return ;
    }

    //显示当前更新的
    IController * workController = ControllerManager::getInstance()->buildController(toolKeyName);
    if(workController)
    {
        curWorkController = workController;
    }
    else
    {
        emit updateToolResultSignal(toolName,false);
        return ;
    }

    QWidget * tool = curWorkController->buildWindow(window);
    auto mainWin = dynamic_cast<MainWindow*>(window);
    mainWin->setToolName(toolName);
    mainWin->addTool(tool);

    curWorkController->showWindow(window);

    toolEnglishName = toolKeyName;
    selectCommunicationType(toolKeyName);

    setWindowComponetShow();

    syncShareInfo(toolKeyName);

    emit tipSpeakSingal(QString("切换%1成功").arg(toolName));
    emit updateToolResultSignal(toolEnglishName,true);
}
void MainwindowController::showWindow(QWidget * parent)
{
    MainWindow * w = new MainWindow(parent,toolSelectable);
    setWindow(w);

    TestManager * testManager =  TestManager::get();
    testManager->setTipController(new TipController(window));

    QStringList toolsList;
    ToolKitsManager::get()->getToolsChineseName(toolsList);
    w->setToolsList(toolsList);

    QString windTile = "Easy Test Kits " + AppInfo::getAppVersion();
    w->setWindowTitleSlot(windTile);

    QString readableName;
    QString toolName = ControllerManager::getInstance()->getToolName(curWorkController);
    if(!toolName.isEmpty())
    {
        readableName = ToolKitsManager::get()->getToolChineseName(toolName);
        w->setToolName(readableName);
    }
    connect(this, SIGNAL(showMainWinMaxSignal(bool)), w, SLOT(showMainWinMaxSlot(bool)));
    connect(this, &MainwindowController::hideCanConfigSignal, w, &MainWindow::hideCanConfigSlot);
    connect(this, &MainwindowController::hideComConfigSignal, w, &MainWindow::hideComConfigSlot);
    connect(this, &MainwindowController::hideMesConfigSignal, w, &MainWindow::hideMesConfigSlot);
    connect(this, &MainwindowController::hideSNInfoSignal, w, &MainWindow::hideSNInfoSlot);

    updateToolSlot(readableName);

    connect(w,&MainWindow::updateToolSignal,this,&MainwindowController::updateToolSlot);
    connect(this,&MainwindowController::updateToolNameSignal,w,&MainWindow::setToolName);

    //for comm
    connect(w,&MainWindow::showCommWindowSignal,this,&MainwindowController::showCommWindwowSlot);
    connect(w,&MainWindow::commConnectSignal,this,&MainwindowController::linkCommunicationSlot);
    connect(w,&MainWindow::commDisconnectSignal,this,&MainwindowController::disconnectCommSlot);
    connect(this,&MainwindowController::sendSerialPortLinkReultSignal,w,&MainWindow::updateSerialLinkResultSlot);
    connect(this,&MainwindowController::sendSSHLinkResultSIgnal,w,&MainWindow::updateSSHLinkResultSlot);
    connect(this,&MainwindowController::ethernetLinkResultSignal,w,&MainWindow::updateEthernetLinkResultSlot);
    connect(this,&MainwindowController::sendLinkStatusCtxSIgnal,w,&MainWindow::updateLinkBtnTextSLot);
    connect(this,&MainwindowController::clearSNCodeSignal,w,&MainWindow::clearSnCodeSlot);

    //for menu action
    connect(w,&MainWindow::configActionTriggeredSignal,this,&MainwindowController::showSettingActionSlot);
    connect(w,&MainWindow::equipmentSearchSignal,this,&MainwindowController::showEquipmentSearchSlot);
    connect(w,&MainWindow::equipmentAddSignal,this,&MainwindowController::showEquipmentAddSlot);
    connect(w,&MainWindow::openMenuConfigSignal,this,&MainwindowController::showToolConfigSlot);

    connect(w,&MainWindow::openAppInfoSignal,this,&MainwindowController::showHelpInfoSlot);
    connect(w,&MainWindow::openDebugSignal,this,&MainwindowController::showDebugSlot);
    connect(w,&MainWindow::openTutorialMnanualSignal,this,&MainwindowController::showTutorialManualInfoSlot);
    //for mes
    MesManager *mesManager = MesManager::get();
    connect(mesManager,&MesManager::loginMesResultSignal, w,&MainWindow::updateMesLoginResultSlot);
    connect(mesManager,&MesManager::loginMesResultSignal, this,&MainwindowController::parseMesLoginResult);
    connect(w,&MainWindow::openMesLoginWindowSigal,this,&MainwindowController::openMesWindowSlot);
    //for sn input
    connect(w, &MainWindow::editSnFinishedSignal,this, &MainwindowController::processSnSlot);
    connect(this,&MainwindowController::updateSNCodeSiganl,w,&MainWindow::updateSNCodeSlot);
    //for CAN
    connect(w, &MainWindow::showCanCfgWinSignal, this, &MainwindowController::showCanCfgWinSlot);
    connect(w, &MainWindow::connectCanSignal, this, &MainwindowController::openCanSolt);
    connect(w, &MainWindow::disConnectCanSignal, this, &MainwindowController::disConnectCanSlot);
    connect(this, &MainwindowController::sendCanLinkResultSignal, w, &MainWindow::updateCanConnectStatusSlot);

   //for 工具设置
    connect(this,&MainwindowController::toolSelectEnableSignal,w,&MainWindow::updateToolSelectEnableSlot);

    connect(testManager,&TestManager::updateToolSelectEnableSignal,this,&MainwindowController::toolSelectEnableSignal);

    w->show();
}

QWidget * MainwindowController::buildWindow(QWidget * parent)
{
    return nullptr;
}

void MainwindowController::setWindow(QWidget * w)
{
    if(w)
    {
        window = w;
    }
    else
    {
        return ;
    }
    return;
}

void MainwindowController::sendMACToolResultSlot()
{
    emit sendMacBurnedResultSignal(result);
}

void MainwindowController::setMACToolResultSlot(const QString & str,const QString & sn)
{
    result = str;
    snCode = sn;
}

void MainwindowController::sendSECCSNCodeSlot()
{
    emit sendSECCSNCodeSignal(snCode);
}
//用于通信配置界面有默认的数据
void MainwindowController::selectCommunicationType(const QString & tool)
{
    // 先按照具体的功能进行选择
    LinkDeviceType type =ToolKitsManager::get()->getToolLinkDeviceType(ToolKitsManager::get()->getCurrentToolFunction());
    if(type == MAX_LINK_DEVICE_TYPE )
    {
        // 具体功能未设置,再按照工具进行选择
        type = ToolKitsManager::get()->getToolLinkDeviceType(tool);
        if(type == MAX_LINK_DEVICE_TYPE)
        {
            emit toolCommunicationTypeSignla(SERIAL_PORT);
            return;
        }
    }
    {
        //todo:
        int index = 0;
        if(type & SSH_LINKE)
        {
            index = 1;
        }
        if(type  & SERIAL_PORT_LINK)
        {
            index = 0;
        }
        if(type & ETHERNET_LINK)
        {
            index = 2;
        }

        emit toolCommunicationTypeSignla((LinkType)(index));
        if(type & SSH_LINKE)
        {
            int model = 0;
            ToolFunctionType funtype  = ToolKitsManager::get()->getCurrentToolFunction();
            if(funtype != UNKOWN_TOOL_FUNCTION_E)
            {
                model = ToolKitsManager::get()->getSShAuthModel(funtype);
            }
            emit sshLoginModelSigal(model);

            if(model == 1)
            {
                emit sshCertLoginUserSignal((int)TestManager::get()->getUser());
            }
        }
        else if (type & SERIAL_PORT_LINK)
        {
            int baud = 0;
            ToolFunctionType funtype  = ToolKitsManager::get()->getCurrentToolFunction();
            if(funtype != UNKOWN_TOOL_FUNCTION_E)
            {
                baud = ToolKitsManager::get()->getSerialBaund(funtype);
                if(baud==-1)
                {
                    //默认使用9600
                    baud = 9600;
                }
            }
            emit serialBaudSignal(baud);
        }
    }

}
void MainwindowController::setWindowComponetShow()
{
    bool setMax=false;
    bool hideMes=false;
    bool hideCom =false;
    bool hideSn = false;
    bool hideCan=true;
    curWorkController->getControlWindowInfo(setMax,hideMes,hideCom,hideSn,hideCan);
    emit showMainWinMaxSignal(setMax);
    emit hideMesConfigSignal(hideMes);
    emit hideComConfigSignal(hideCom);
    emit hideSNInfoSignal(hideSn);
    emit hideCanConfigSignal(hideCan);
}
void MainwindowController::syncShareInfo(QString &toolName)
{
    //nothing to do
    if(ethernetIP.isEmpty())
    {
        return;
    }
    //用于从一个工具切到另一个工具的。可能没有用。但先保留
    DeviceContext::get()->setDeviceLinkIP(ethernetIP);
    DeviceContext::get()->setDeviceLinkPort(ethernetPort);
}
void MainwindowController::buildEquipmentMenuController()
{
    if(equipmentMenuController == nullptr)
    {
        auto controller = new EquipmentMenuController();

        connect(this,&MainwindowController::equipmentSearchSignal,controller,&EquipmentMenuController::showSearchSlot);
        connect(this,&MainwindowController::equipmentAddSignal,controller,&EquipmentMenuController::showAddSlot);

        //connect(controller,&EquipmentMenuController::equipmentAddInfoSignal,this,&MainwindowController::equipmentCodeInfoSignal);
        connect(controller,&EquipmentMenuController::equipmentMapFunsSignal,this,&MainwindowController::displayTool);

        equipmentMenuController = controller;
    }
}

void MainwindowController::showEquipmentSearchSlot()
{
    buildEquipmentMenuController();

    emit equipmentSearchSignal(window);
}
void MainwindowController::showEquipmentAddSlot()
{

    buildEquipmentMenuController();

    emit equipmentAddSignal(window);
}
void MainwindowController::showSettingActionSlot()
{
    if(settingMenuController == nullptr)
    {
        settingMenuController = new SettingMenuController;
        //sobad perfomer for cast
        auto setController = dynamic_cast<SettingMenuController*>(settingMenuController);
        if(setController)
        {
            connect(this,&MainwindowController::configActionTriggeredSignal,setController,&SettingMenuController::showWindow);
        }
    }
    emit configActionTriggeredSignal(window);
}
void MainwindowController::showToolConfigSlot()
{
    if(configMenuController == nullptr)
    {
        configMenuController = new ConfigController;
        //sobad perfomer for cast
        auto setController = dynamic_cast<ConfigController*>(configMenuController);
        if(setController)
        {
            connect(this,&MainwindowController::showToolConfigSignal,setController,&ConfigController::showMenuConfigSlot);
        }
    }
    emit showToolConfigSignal(window);
}

void MainwindowController::buildHelpMenuController()
{
    if(helpMenuController == nullptr)
    {
        auto controller = new HelpController();

        connect(this,&MainwindowController::helpInfoSignal,controller,&HelpController::showAPPInfoWindwSlot);
        connect(this,&MainwindowController::tutorialManualSignal,controller,&HelpController::showTutorialManualWindwSlot);

        helpMenuController = controller;
    }
}

void MainwindowController::buildDebugMenuController()
{
    if(debugController == nullptr)
    {
        auto controller = new DebugController();

        connect(this,&MainwindowController::debugSignal,controller,&DebugController::showWindow);
//        connect(this,&MainwindowController::tutorialManualSignal,controller,&HelpController::showTutorialManualWindwSlot);

        debugController = controller;
    }
}
void MainwindowController::showHelpInfoSlot()
{
    buildHelpMenuController();

    emit helpInfoSignal(window);
}
void MainwindowController::showTutorialManualInfoSlot()
{
    buildHelpMenuController();

    emit tutorialManualSignal(window);
}

void MainwindowController::showDebugSlot()
{
     if(TestManager::get()->isRootModel())
     {
         buildDebugMenuController();
         emit debugSignal(window);
     }
     else
     {
         QSharedPointer<TipController> tip = QSharedPointer<TipController>(new TipController(window));
         tip->showTipWindowSlot("请使用管理员权限设置",MODAL,OK,TIPS);
     }
}
bool MainwindowController::isSSHType()
{
   LinkDeviceType type =  ToolKitsManager::get()->getToolLinkDeviceType(toolEnglishName);
   return type == (type&SSH_LINKE)?true:false;
}
bool MainwindowController::isSCQrCode(const QString & code)
{
    QRegularExpression regex("https://qrcode\\.starcharge\\.com/#/XPACHOME/(\\d+)");
    QRegularExpressionMatch match = regex.match(snCode);
    if(match.hasMatch())
    {
        return true;
    }
    return false;
}

bool MainwindowController::isFCTStandardSnFormat(const QString &code)
{
    QSharedPointer<FCTSNRules> snRules;
    QStringList out;
    QString reason;
    snRules.reset(new FCTSNRules());
    return snRules->parse(code,out,reason);
}
bool MainwindowController::isStandardSnFormat(const QString & snCode)
{
    QSharedPointer<SNRules> snRules;
    QStringList out;
    QString reason;
    snRules.reset(new SNRules());
    return snRules->parse(snCode,out,reason);
}
//复用处理:标准的SN;星充的二维码;标准的物料处理
void MainwindowController::processSnSlot(const QString & snCode)
{
    QSharedPointer<TipController> tipPtr= QSharedPointer<TipController>(new TipController(this->window->parentWidget()));
    tipPtr->showTipWindowSlot(QString(tr("SN处理中，请稍后")),MODELESS,NO,TIPS);
    if(APPConfig::get()->isDependMes())
    {
        if(MesManager::get()->isMesLogined() == false)
        {
            QSharedPointer<TipController> tip = QSharedPointer<TipController>(new TipController(this->window->parentWidget()));
            tip->showTipWindowSlot(QString(tr("请先登录MES")),MODAL,OK,TIPS);
            return;
        }
        if(isStandardSnFormat(snCode) || isFCTStandardSnFormat(snCode))
        {
            emit snInputFinishedSignal(snCode);
        }
        else if(isSCQrCode(snCode))
        {
            parseQrCode(snCode);
        }
        else
        {
            QSharedPointer<TipController> tip = QSharedPointer<TipController>(new TipController(this->window->parentWidget()));
            tip->showTipWindowSlot(QString(tr("非法的输入")),MODAL,OK,TIPS);
            return;
        }
    }
    else
    {
        DeviceContext::get()->setSN(snCode);
        tipPtr->closeTipWinowSlot();
    }
    //todo:是否是各类物料号,当前只处理了国际直流的
    QSharedPointer<MaterialRules> materialRules(new INTLDCMaterialRules);
    if(materialRules->isValid(snCode))
    {
        emit materialCodeInputFinishedSignal(snCode);
        tipPtr->closeTipWinowSlot();
    }

    if(isSSHType())
    {
        if(TestManager::get()->needAutoLinkDevice())
        {
            if(LinkManager::get()->getSSH())
            {
                //有SSH 不需要再登录。
                return;
            }
            connect(this,&MainwindowController::requstLinkWindowSignal,this,&MainwindowController::linkCommunicationSlot,Qt::UniqueConnection);
            if(TestManager::get()->hasDeviceLinkCondition())
            {
                emit requstLinkWindowSignal();
            }
            else
            {
                connect(TestManager::get(),&TestManager::deviceLinkConditionOkSignal,this,&MainwindowController::requstLinkWindowSignal,Qt::UniqueConnection);
            }
        }
    }
}

void MainwindowController::parseQrCode(const QString &qrCode)
{
    QSharedPointer<DeviceMesWorker> deviceMesWorker = QSharedPointer<DeviceMesWorker>(new DeviceMesWorker);
    connect(MesManager::get(),&MesManager::snCodeSignal, this, &MainwindowController::parseSNCodeSlot);

    QSharedPointer<SCQRSNRules> qrCodeRules;
    qrCodeRules.reset(new SCQRSNRules());
    QStringList out;
    QString reason;
    bool ret = qrCodeRules->parse(qrCode,out,reason);
    if(ret)
    {
        if(!out.isEmpty())
        {
            deviceMesWorker->getParseSNInfo(out[0]);
        }
    }
}

void MainwindowController::openMesWindowSlot()
{
    if(mesLoginWindow == nullptr)
    {
        mesLoginWindow = new MesLoginWindow();
        TestManager * testManager =  TestManager::get();
        connect(mesLoginWindow, &MesLoginWindow::mesLoginInfoSinal,testManager,&TestManager::updateMesCtxSlot);
        connect(mesLoginWindow,&MesLoginWindow::mesLoginInfoSinal,this,&MainwindowController::mesLoginSlot);
    }

    QString deviceId;
    QString account = APPConfig::get()->getStringValue("MES/account");
    MesManager::get()->getCurrToolMesDeviceId(deviceId);
    mesLoginWindow->setMesConfigInfo(deviceId, account,TestManager::get()->isMesDeviceModifyEnable());
    mesLoginWindow->showMesLoginWindow();
}

void MainwindowController::mesLoginSlot(const QString & deviceId, const QString & account, const QString & pwd)
{
    QString password = pwd;
    APPConfig::get()->setKeyValueString("MES/account",account);
    MesManager::get()->setPWDInfo(password);
    MesManager::get()->loginMes(account,pwd,deviceId);
}

void MainwindowController::parseMesLoginResult(bool res, QString msg)
{
    if(!res)
    {
        QString tip = QString("MES登录失败，原因：%1").arg(msg);
        emit tipWindowSignal(tip, MODAL, OK, WARNING);
    }
}
void MainwindowController::buildCommmWindow()
{
    commWindow = new SerialPortDialog();
    connect(commWindow,&SerialPortDialog::changeLinkTypeSignal,this,&MainwindowController::changeLinkTypeSlot);
    connect(commWindow,&SerialPortDialog::ethernetInfoSignal,this,&MainwindowController::setEthernetInfoSlot);
    connect(commWindow,&SerialPortDialog::sshInfoSignal,this,&MainwindowController::setSSHInfoSlot);
    connect(commWindow,&SerialPortDialog::serialPortInfoSignal,this,&MainwindowController::setSerialPortInfoSlot);
    connect(commWindow,&SerialPortDialog::openCanDevcieSignal,this,&MainwindowController::processCanSolt);

    connect(this,&MainwindowController::toolCommunicationTypeSignla,commWindow,&SerialPortDialog::setLinkType);
    connect(this,&MainwindowController::sshLoginModelSigal,commWindow,&SerialPortDialog::setSSHModel);
    connect(this,&MainwindowController::sshCertLoginUserSignal,commWindow,&SerialPortDialog::setSSHCertLoginUser);
    connect(this,&MainwindowController::serialBaudSignal,commWindow,&SerialPortDialog::setBaud);
}
void MainwindowController::showCommWindwowSlot()
{
    if(commWindow == nullptr)
    {
        buildCommmWindow();
    }
    selectCommunicationType(toolEnglishName);

    commWindow->show();
}

void MainwindowController::parseSNCodeSlot(bool res, const QString & msg, const QJsonObject & info)
{
    if(res)
    {
        QString snCode = info.value("sn").toString();
        emit updateSNCodeSiganl(snCode);
        emit snInputFinishedSignal(snCode);
    }
    else
    {
        QString tip = QString("根据二维码获取SN错误，原因：%1").arg(msg);
        emit tipWindowSignal(tip, MODAL, OK, WARNING);
    }
    disconnect(MesManager::get(),&MesManager::snCodeSignal, this, &MainwindowController::parseSNCodeSlot);
}

void MainwindowController::linkCommunicationSlot()
{
    linkDeviceType =  TestManager::get()->getLinkDeviceType();
    if(linkDeviceType & SERIAL_PORT_LINK)
    {
        openSerialPortSolt();
    }
    if(linkDeviceType & SSH_LINKE)
    {
        openSSHLinkSlot();
    }
    if(linkDeviceType & ETHERNET_LINK)
    {
        openEthernetLinkSlot();
    }
}
void MainwindowController::disconnectEthernetSlot()
{
    LinkManager * linkManager = LinkManager::get();
    linkManager->disconnectHost(ethernetIP,ethernetPort);
}
void MainwindowController::disconnectCommSlot()
{
    linkDeviceType =  TestManager::get()->getLinkDeviceType();
    if(linkDeviceType & SERIAL_PORT_LINK)
    {
        disconnectSerialSlot();
    }
    if(linkDeviceType & SSH_LINKE)
    {
        disconnectSSHSlot();
    }
    if(linkDeviceType & ETHERNET_LINK)
    {
        disconnectEthernetSlot();
    }
}

void MainwindowController::changeLinkTypeSlot(int type)
{
    if(type >= 3)
    {
        return;
    }
    linkDeviceType =(LinkDeviceType) (1<<type);

    LinkManager * linkManager = LinkManager::get();
    bool linkStatus = false;
    if(linkDeviceType & SERIAL_PORT_LINK)
    {
        linkStatus = linkManager->getSerialLinkedStatus(DeviceContext::get()->getSerialPortName());
        emit sendSerialPortLinkReultSignal(linkStatus);
    }
    if(linkDeviceType & SSH_LINKE)
    {
        linkStatus = linkManager->getSSHLinkedStatus();
    }
    if(linkDeviceType & ETHERNET_LINK_E)
    {
        linkStatus = (linkManager->getEthernetClient(ethernetIP,ethernetPort)) == nullptr?false:true;
    }

    isCustomLinkMethod = true;

    emit sendLinkStatusCtxSIgnal(linkStatus ? "断开" : "连接");
}

void MainwindowController::linkSerialPortSlot(QString comName,int baud, int stopbit, int flowctrl, int databit, int parity)
{
    LinkManager * linkManager = LinkManager::get();
    connect(linkManager,&LinkManager::serialLinkResultSignal, this, &MainwindowController::updateSerialPortLinkResultSlot);

    serialPortPtr = linkManager->createLinkWithSerial(comName,baud,stopbit,flowctrl,databit,parity);
}

void MainwindowController::disconnectSerialSlot()
{
    LinkManager * linkManager = LinkManager::get();
    linkManager->disconnectSerialLink();
}

void MainwindowController::updateSerialPortLinkResultSlot(bool result)
{
    emit sendSerialPortLinkReultSignal(result);
}

void MainwindowController::linkSSHSlot(QString ip, QString user, QString password, int model)
{
    LinkManager * linkManager = LinkManager::get();
    connect(linkManager, &LinkManager::sshLinkResultSignal, this, &MainwindowController::sendSSHLinkResultSIgnal);

    linkManager->createLinkWithSSH(ip, user, password, (SSHLinkedModule)model);
}

void MainwindowController::disconnectSSHSlot()
{

}

void MainwindowController::openSerialPortSolt()
{
    QString port;
    int baud;
    int dataBit;
    int stopBit;
    int checkBit;
    int flowCtrl;
    DeviceContext::get()->getSerialPortInfo(port,baud,dataBit,stopBit,checkBit,flowCtrl);

    linkSerialPortSlot(port,baud,stopBit,flowCtrl,dataBit,checkBit);
}

void MainwindowController::openSSHLinkSlot()
{

//    TODO:如何更好的维护。
//    if(type == INTERNAL_DC_CONFIGE_E)
//    {
//        //国际后期的固件，都会使用这个密码。但无论是固件安全，还是非安全,在生产线都可以用密码的形式登录。
//        DeviceContext::get()->setSSHLoginPassword("!&n#gVjFAQG!Vztwv9x3C2w&A*bJLkxH");
//        DeviceContext::get()->setSSHLoginModel(0);
//    }

    QString ip = DeviceContext::get()->getDeviceLinkIP();
    QString user = DeviceContext::get()->getSSHLoginUser();
    QString password = DeviceContext::get()->getSSHLoginPassword();
    int model = DeviceContext::get()->getSSHLoginModel();

    linkSSHSlot(ip, user, password, model);
}

void MainwindowController::openCanSolt()
{
    //配置数据在配置界面已经传入数据中心进行存放，这里直接调用连接就行
    linkCanSlot();
}

void MainwindowController::linkCanSlot()
{
    LinkManager * linkManager = LinkManager::get();
    connect(linkManager, &LinkManager::updateCanLinkedStatus, this, &MainwindowController::sendCanLinkResultSignal);

    linkManager->createLinkWithCAN();
}

void MainwindowController::disConnectCanSlot()
{
    LinkManager * linkManager = LinkManager::get();

    linkManager->disConnectCanSlot();
}

void MainwindowController::showCanCfgWinSlot()
{
    if(canCfgWindow == nullptr)
    {
        canCfgWindow = new CanConfigWindow();
    }
    canCfgWindow->show();
}

void MainwindowController::processCanSolt(const QString &deviceType,int index)
{
    if(deviceType.contains("ZLG_USBCANFD_200U"))
    {
        //todo:先判定index是否打开过。
        int fd  = LinkManager::get()->openCanDevice(CANDeviceType::ZLG_CAN_FD_E);
        LinkManager::get()->createLinkWithCAN(fd,0);
        LinkManager::get()->createLinkWithCAN(fd,1);
    }
    else if(deviceType.contains("GC_USBCAN_2"))
    {
        int fd  = LinkManager::get()->openCanDevice(CANDeviceType::GC_CAN_E);
        LinkManager::get()->createLinkWithCAN(fd,0);
        LinkManager::get()->createLinkWithCAN(fd,1);
    }
}

void MainwindowController::openEthernetLinkSlot()
{
    ethernetIP = DeviceContext::get()->getDeviceLinkIP();
    ethernetPort = DeviceContext::get()->getDeviceLinkPort();
    linkEthernetSlot(ethernetIP,ethernetPort);
}
void MainwindowController::linkEthernetSlot(const QString &ip, int port)
{
    LinkManager * linkManager = LinkManager::get();
    connect(linkManager, &LinkManager::ethernetLinkedSignal, this, &MainwindowController::ethernetLinkResultSignal);
    linkManager->connectHost(ip, port);
}
void MainwindowController::setSerialPortInfoSlot(const QString & port,int baud,  int dataBit,int stopBit, int parity,int flowctrl)
{
    isCustomLinkMethod = true;
    DeviceContext::get()->setSerialPortInfo(port,baud,dataBit,stopBit,parity,flowctrl);
    TestManager::get()->setLinkDeviceType(SERIAL_PORT_LINK);
}
void MainwindowController::setSSHInfoSlot(const QString & ip,const QString & usr,const QString & psw,int sshModel,const QStringList &auxInfo)
{
    isCustomLinkMethod = true;
    DeviceContext::get()->setDeviceLinkIP(ip);
    DeviceContext::get()->setSSHLoginUser(usr);
    DeviceContext::get()->setSSHLoginModel(sshModel);
    if(sshModel == 0)
    {
         DeviceContext::get()->setSSHLoginPassword(psw);
    }
    if(!auxInfo.isEmpty())
    {
        DeviceContext::get()->setDepartment(auxInfo[0]);
        //定制信息,后续考虑这些信息使用其他的方式呈现.当前保存了web的账户和密码
        if(auxInfo.size()>=3)
        {
            DeviceContext::get()->setWebLoginInfo(auxInfo[1],auxInfo[2]);
        }
    }
    TestManager::get()->setLinkDeviceType(SSH_LINKE);

}
void MainwindowController::setEthernetInfoSlot(const QString &ip, int port)
{
    isCustomLinkMethod = true;

    ethernetIP = ip;
    ethernetPort = port;
    DeviceContext::get()->setDeviceLinkIP(ip);
    DeviceContext::get()->setDeviceLinkPort(port);
    if(ToolKitsManager::get()->isMassEnergyStoreTool(toolEnglishName))
    {
        ControllerManager::getInstance()->syncShareInfo(toolEnglishName,ethernetIP,ethernetPort);
    }
    TestManager::get()->setLinkDeviceType(ETHERNET_LINK);
    return;
}
void MainwindowController::displayTool(const QString &tool,const QStringList & funs,bool ret)
{
    //更新MES登录界面信息
    MesManager::get()->updateMesApiInfo();

    updateToolSlot(ToolKitsManager::get()->getToolChineseName(tool));
}
