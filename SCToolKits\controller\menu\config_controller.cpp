#include "config_controller.h"
#include "controller_manager.h"
#include "mes/mes_manager.h"
ConfigController::ConfigController() : tool(nullptr), tipController(nullptr)
{

}
ConfigController::~ConfigController()
{
}
void ConfigController::showSettingTimeout(const QString & tip,int msc)
{
    QSharedPointer<TipController> tipController = QSharedPointer<TipController>(new TipController(this->tool->parentWidget()));
    tipController->showTipWindowSlot(tip,MODELESS,OK,TIPS);
    QTimer::singleShot(msc,[&]()
    {
        tipController->closeTipWinowSlot();
    });

}
void ConfigController::connectSignalWithSlot()
{
   ConfigWindow * realTool = dynamic_cast<ConfigWindow*>(tool);
   ToolFunctionType funType = ToolKitsManager::get()->getCurrentToolFunction();
   if(funType == EVCC_CHARGE_FCT_E)
   {
       connect(realTool, &ConfigWindow::mesWorkstationSignal,this, &ConfigController::setMesWorkstationId);
       connect(realTool, &ConfigWindow::softwareVersionSignal,this, &ConfigController::setTestSoftwareVersion);
       connect(realTool, &ConfigWindow::evccfctNumSignal,this,[&](int num)
       {
           emit configInfoSignal(ConfigInfosType::EVCC_CAN_DEVICES_NUM_E,num);
       });
   }
   else if (funType == PCS_M215_AGEING_E)
   {
       connect(realTool, &ConfigWindow::ageingTimeSignal,this,[&](int num)
       {
           emit configInfoSignal(ConfigInfosType::PCS_AGE_TIME_E,num);
       });

       connect(realTool, &ConfigWindow::ageingPowerSignal,this,[&](int num)
       {
           emit configInfoSignal(ConfigInfosType::PCS_FULL_POWER_RATE_E,num);
       });
   }
   else if(funType == INTERNAL_DC_CONFIGE_E)
   {
       connect(realTool, &ConfigWindow::updateXmlFileSignal,this, &ConfigController::updateXmlFileSlot);
   }

}
void ConfigController::showWindow(QWidget * parent)
{
    // TODO parent 变更的情况
    if(tool == nullptr)
    {
        tool = new ConfigWindow(parent);
        ConfigWindow * realTool = dynamic_cast<ConfigWindow*>(tool);

        connect(this, &ConfigController::setCurrConfigToolSignal, realTool, &ConfigWindow::updateConfigToolGUI);
        connect(realTool, &ConfigWindow::settingNetworkInfoSignal,this, &ConfigController::settingNetworkInfoSlot);

        connectSignalWithSlot();
    }
    if(tipController == nullptr)
    {
        tipController = new TipController(parent);
        connect(this,&ConfigController::tipContexSignal, tipController, &TipController::showTipWindowSlot);
        connect(this,&ConfigController::closeTipWindow, tipController, &TipController::closeTipWinowSlot);
    }

    int type = ToolKitsManager::get()->getCurrentToolFunction();
    QString readableName;
    QString toolName = ControllerManager::getInstance()->getToolName();
    if(!toolName.isEmpty())
    {
        readableName = ToolKitsManager::get()->getToolChineseName(toolName);
    }

    emit setCurrConfigToolSignal(readableName,type);

    IController* businessController = ControllerManager::get()->getCurrentController();
    if(businessController)
    {
         connect(this, &ConfigController::configInfoSignal,businessController,&IController::syncConfigInfoSlot);
    }
    loadData(type);

    tool->show();
}
QWidget * ConfigController::buildWindow(QWidget * parent)
{
    if(tool == nullptr)
    {
        tool = new ConfigWindow(parent);
    }

    return tool;
}
void ConfigController:: setWindow(QWidget * )
{

}

void ConfigController::loadData(int type)
{
    
}

void ConfigController::showMenuConfigSlot(QWidget *parent)
{
    if(TestManager::get()->isRootModel())
    {
        showWindow(parent);
    }
    else
    {
        QSharedPointer<TipController> tip = QSharedPointer<TipController>(new TipController(parent));
        tip->showTipWindowSlot("请使用管理员权限设置",MODAL,OK,TIPS);
    }
}

void ConfigController::settingNetworkInfoSlot(int ret, int hasLossRate)
{
    emit configInfoSignal(ConfigInfosType::NETWORK_TEST_CUSTOME_ENABLE_E,ret);
    SQLiteManager::get()->insert(AUTO_GET_COMMUNICATION_TEST_ITEM_CONTROL,ret);
    qDebug()<<"AUTO_GET_COMMUNICATION_TEST_ITEM_CONTROL"<<ret;
    SQLiteManager::get()->insert(FOURTHG_LOSS_RATE_CONFIG_CONTROL,hasLossRate);
    qDebug()<<"FOURTHG_LOSS_RATE_CONFIG_CONTROL"<<hasLossRate;
}

//用于工作中心配置：
void ConfigController::setMesWorkstationId(int ret)
{
    SQLiteManager::get()->insert(MES_WORKSTATION_CONTROL,ret);
    MesManager::get()->setMesWorkstion(ret);
    QSharedPointer<TipController> tip = QSharedPointer<TipController>(new TipController(this->tool->parentWidget()));
    showSettingTimeout("mes中心设置成功");
}

void ConfigController::setTestSoftwareVersion(const QString & ver)
{
    SQLiteManager::get()->insert(TEST_SOFTWARE_VER_CONTROL,1,ver);
    emit configInfoSignal(ConfigInfosType::EVCC_TEST_SOFTWARE_VER_E,-1,ver);

    showSettingTimeout(tr("版本设置成功"));
}

void ConfigController::updateXmlFileSlot(const QString & path)
{
    SQLiteManager::get()->insert(DC_CONFIG_FILEPATH_CONTROL,1,path);
    InterfaceData::get()->setCustomFile(path);
    showSettingTimeout(tr("xml文件配置成功"));
}
