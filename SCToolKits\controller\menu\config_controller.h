#ifndef CONFIGCONTROLLER_H
#define CONFIGCONTROLLER_H

#include <QObject>
#include <QJsonParseError>
#include <QJsonDocument>
#include <QJsonObject>
#include "interface_controller.h"
#include "gui/menus/settings/config_window.h"
#include "toolkits_manager.h"
#include "database/sqlite_manager.h"
#include "tip_controller.h"
#include "meter_verify_tool/ac_meter/ac_meter_material_data.h"
#include "common_share/test_manager.h"

class ConfigController : public IController
{
    Q_OBJECT
public:
    ConfigController();
    ~ConfigController();

public:
    void showWindow(QWidget * parent);
    void setWindow(QWidget * );
    QWidget * buildWindow(QWidget * parent=nullptr);

private:
    void loadData(int type);
    void connectSignalWithSlot();
    void showSettingTimeout(const QString &,int mse=1000);

signals:
    void setCurrConfigToolSignal(const QString & ,int);
    void tipContexSignal(QString tips, MSG_MODE mode, MSG_TYPE msgType, MSG_TIP_TYPE tipType);
    void closeTipWindow();
    int configInfoSignal(ConfigInfosType,int,QString value="" );
public slots:
    void showMenuConfigSlot(QWidget * parent);
    void settingNetworkInfoSlot(int,int);
    void setMesWorkstationId(int ret);
    void setTestSoftwareVersion(const QString & ver);
    void updateXmlFileSlot(const QString &);
private:
    QWidget *tool;
    TipController * tipController;
};

#endif // CONFIGCONTROLLER_H
