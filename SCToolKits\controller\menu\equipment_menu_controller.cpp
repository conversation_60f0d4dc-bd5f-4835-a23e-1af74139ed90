#include "equipment_menu_controller.h"
#include "menus/equipments/equipment_search.h"
#include "menus/equipments/equipment_add_window.h"
#include "toolkits_manager.h"
#include <QDebug>
#include "tip_controller.h"
#include "database/sqlite_manager.h"
#include "common_share/test_manager.h"
EquipmentMenuController::EquipmentMenuController():tool(nullptr),
                         searchWindow(nullptr),
                         searchWorker(nullptr),
                         addWindow(nullptr),
                         addWorker(nullptr),
                         currentAction(EQUIPMENT_SEARCH_CODE)
{

}
void EquipmentMenuController::handleEquipmentSearchResultSlot(bool ret,const QString & equipCode,const QString & tool,const QStringList & funs)
{
    emit equipmentMapFunsSignal(tool,funs,ret);
    if(ret)
    {
        QSharedPointer<TipController> tip = QSharedPointer<TipController>(new TipController(this->tool->parentWidget()));
        bool accept = tip->showTipWindowSlot("固定当前工具为每次打开的界面",MODAL,OKCANCEL,TIPS);
        if(accept)
        {
            SQLiteManager::get()->insert(APP_DISPLYA_EQUIPEMNT_SEARCH_CONTROL,1,equipCode,tool);
        }
    }
}
void EquipmentMenuController::selectEquipmentMapFunSlot(const QString & code)
{
    QStringList funs;
    QString tool;
    ToolFunctionType funType = ToolKitsManager::get()->getFunType(code);
    if(funType != UNKOWN_TOOL_FUNCTION_E)
    {
        QString chineseName;
        ToolKitsManager::get()->getFunName(funType,chineseName);

        QStringList readableFuns(chineseName);
        emit equipmentCodeMapReadablelName(readableFuns);
    }
    else
    {
        QStringList readableFuns("没有找到对应机种功能");
        emit equipmentCodeMapReadablelName(readableFuns);
    }
}
void EquipmentMenuController::processEquipmentUnifyInputInfoSlot(const QString &equipmentCode,const QString & tool,const QStringList & funs ,int catery )
{
//    QStringList convertFuns;
//    //funs只有一个数据，不需要用QStringList。
//    foreach(auto &var,funs)
//    {
//        QString chineseName;
//        ToolKitsManager::get()->getFunsEnglisName(var,chineseName);
//        convertFuns<<chineseName;
//    }

    emit equipmentAddInfoSignal(equipmentCode,tool,funs,catery);
}
void EquipmentMenuController::showSearchSlot(QWidget * parent)
{
    if(TestManager::get()->isRootModel())
    {
        currentAction = EQUIPMENT_SEARCH_CODE;
        buildWindow(parent);
    }
    else
    {
        QSharedPointer<TipController> tip = QSharedPointer<TipController>(new TipController(parent));
        tip->showTipWindowSlot("请使用管理员权限设置",MODAL,OK,TIPS);
    }

}
void EquipmentMenuController::showAddSlot(QWidget*parent)
{
    if(TestManager::get()->isRootModel())
    {
        currentAction = EQUIPMENT_ADD_CODE;
        buildWindow(parent);
    }
    else
    {
        QSharedPointer<TipController> tip = QSharedPointer<TipController>(new TipController(parent));
        tip->showTipWindowSlot("请使用管理员权限设置",MODAL,OK,TIPS);
    }
}

QWidget * EquipmentMenuController::buildWindow(QWidget * parent)
{
    // TODO parent 变更的情况
    if(currentAction == EQUIPMENT_SEARCH_CODE)
    {
        if(searchWindow == nullptr)
        {
            searchWindow = new EquipmentSearch(parent);
            EquipmentSearch * realTool = dynamic_cast<EquipmentSearch*>(searchWindow);
            if(realTool)
            {
                //机种相关
                connect(realTool,&EquipmentSearch::equipmentCodeInfoSignal,
                        this,&EquipmentMenuController::equipmentCodeInfoSignal);
                connect(this,&EquipmentMenuController::equipmentsListSignal,
                                     realTool,&EquipmentSearch::setEquipmentList);
                //机种对应的功能显示
                connect(realTool,&EquipmentSearch::equipmentCodeTriggerSignal,
                        this,&EquipmentMenuController::selectEquipmentMapFunSlot);
                connect(this,&EquipmentMenuController::equipmentCodeMapReadablelName,
                        realTool,&EquipmentSearch::displayEquipmenmtMapFunsSlot);
            }

            if(searchWorker == nullptr)
            {
                searchWorker =  new EquipmentSearchWorker();
                connect(searchWorker,&EquipmentSearchWorker::equipmentMapFunsSignal,
                        this,&EquipmentMenuController::handleEquipmentSearchResultSlot);
                connect(this,&EquipmentMenuController::equipmentCodeInfoSignal,searchWorker,&EquipmentSearchWorker::selectFunctionSlot);
                if(realTool)
                {
                    connect(this,&EquipmentMenuController::equipmentMapFunsSignal,realTool,&EquipmentSearch::handleSearchEquipmentResultSlot);
                }
            }

            QStringList equipments;
            ToolKitsManager::get()->getToolKitsEquipments(equipments);
            emit equipmentsListSignal(equipments);
        }

        tool = searchWindow;
    }
    else if(currentAction == EQUIPMENT_ADD_CODE)
    {
        if(addWindow == nullptr)
        {
            addWindow = new EquipmentAddWindow(parent);
            EquipmentAddWindow * w = dynamic_cast<EquipmentAddWindow*>(addWindow);
            if(w)
            {
                connect(w,&EquipmentAddWindow::equipmentAddInfoSignal,
                        this,&EquipmentMenuController::processEquipmentUnifyInputInfoSlot);
                connect(this,&EquipmentMenuController::toolContainFunsSignal,
                                     w,&EquipmentAddWindow::setToolandFunsSlot);
            }
            if(addWorker == nullptr)
            {
                addWorker =  new EquipmentAddWorker();
                connect(this,&EquipmentMenuController::equipmentAddInfoSignal,addWorker,&EquipmentAddWorker::addEquipmentSlot);
                connect(addWorker,&EquipmentAddWorker::addEquipmentResultSignal,w,&EquipmentAddWindow::handleAddEquipmentResultSlot);
            }
        }

        QMap<QString,QStringList>toolContainsFun;
        ToolKitsManager::get()->getToolAndFuns(toolContainsFun);
        emit toolContainFunsSignal(toolContainsFun);

        tool = addWindow;
    }
    else
    {
        qDebug()<< "will to add ";
    }

    tool->show();
    return tool;
}
