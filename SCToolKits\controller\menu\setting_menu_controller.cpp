#include "setting_menu_controller.h"
#include "menus/settings/authority.h"
#include "app_start/app_config.h"
#include "common_share/test_manager.h"
#include <QDebug>
#include "app_authority_level.h"
#include "app_authority_manager.h"
SettingMenuController::SettingMenuController():tool(nullptr)
{
}
SettingMenuController::~SettingMenuController()
{
}
void SettingMenuController::showWindow(QWidget * parent)
{
    // TODO parent 变更的情况
    if(tool == nullptr)
    {
        tool = new Authority(parent);
        Authority * realTool = dynamic_cast<Authority*>(tool);
        if(realTool)
        {
            connect(realTool, &Authority::loginConfigSignal, this, &SettingMenuController::checkAuthoritySlot);
            connect(realTool, &Authority::enableLogOutFileSignal, this, &SettingMenuController::setLogOutEnableSlot);
            connect(realTool, &Authority::enableRootModelSignal, this, &SettingMenuController::setRootModelSlot);
            connect(realTool, &Authority::enableToolSwitchSignal, this, &SettingMenuController::setToolSelectEnableSlot);
            connect(realTool, &Authority::sshReconnectTimeSignal, this, &SettingMenuController::setSSHRecontTimeSlot);
            connect(realTool, &Authority::guiDisplayLanguageSignal, this, &SettingMenuController::switchGuiLanguageSlot);
            connect(realTool, &Authority::dependMesSignal, this, &SettingMenuController::setMesDependSlot);
            connect(realTool, &Authority::setSshPrivateKeyFileSignal, this, &SettingMenuController::setSSHPrivateKeyFileSlot);
            connect(realTool, &Authority::enableMesDeviceIDModifySingal, this, &SettingMenuController::setMesDeviceIdModifyAuth);

            connect(this, &SettingMenuController::authorityResultSignal, realTool, &Authority::loginResultSlot);
            connect(this, &SettingMenuController::logOutToFileStatusSignal, realTool, &Authority::updateLogOutToFileSlot);
        }
    }
    tool->show();
}
QWidget * SettingMenuController::buildWindow(QWidget * parent)
{
    if(tool == nullptr)
    {
        tool = new Authority(parent);
    }

    return tool;
}
void SettingMenuController:: setWindow(QWidget * )
{

}
void SettingMenuController::checkAuthoritySlot(const QString &acc, const QString &pwd)
{
    //TODO:账号和密码判定
    bool authorityEnalbe = false;
    int type = -1;
    if(APPConfig::get()->isDebugModel())
    {
        authorityEnalbe = true;
        type = 0;
    }
    else
    {
        AuthorityLevel level = AppAuthorityManager::get()->getAuthority(acc,pwd);
        if(level==AuthorityLevel::ROOT_AUTHORITY_E)
        {
            authorityEnalbe = true;
            type = 0;
        }
        else if (level==AuthorityLevel::SUPER_USER_AUTHORITY_E)
        {
            authorityEnalbe = true;
            type = 1;
        }
    }

    emit authorityResultSignal(authorityEnalbe,type);
    //TODO
    bool enalbe =  APPConfig::get()->getLogOutToFileEnable();
    emit logOutToFileStatusSignal(enalbe);
}
void SettingMenuController::setLogOutEnableSlot(bool enable)
{
    qDebug()<< "log out enable "<< enable;
    APPConfig::get()->setLogOutToFileEnable(enable);
}
void SettingMenuController::setRootModelSlot(bool enable)
{
    qDebug()<< "root model "<< enable;
    TestManager::get()->setRootModel(enable);
}
void SettingMenuController::setToolSelectEnableSlot(bool enable)
{
    qDebug()<< "tool select "<< enable;
    TestManager::get()->setToolsSelectEnable(enable);
}
void SettingMenuController::setSSHRecontTimeSlot(int value)
{
    qDebug()<< "ssh set reconnect time "<< value;
    if(value < 3)
    {
        QSharedPointer<TipController> tip = QSharedPointer<TipController>(new TipController(this->tool->parentWidget()));
        tip->showTipWindowSlot("不能设置小于3",MODAL,OK,TIPS);
        return;
    }
    if(value > 100)
    {
        QSharedPointer<TipController> tip = QSharedPointer<TipController>(new TipController(this->tool->parentWidget()));
        tip->showTipWindowSlot("不能设置大于100",MODAL,OK,TIPS);
        return;
    }
    QSharedPointer<TipController> tip = QSharedPointer<TipController>(new TipController(this->tool->parentWidget()));
    bool accept = tip->showTipWindowSlot(QString("确定设置时间为%1秒？").arg(value),MODAL,OKCANCEL,TIPS);
    if(accept)
    {
        LinkManager::get()->setSSHReconnectTime(value);
        SQLiteManager::get()->insert(SSH_RECONNECT_TIME_CONTROL,value);
    }
    else
    {

    }
    return ;
}
// 0:中文。1英文
#define LANGUAGE_MAX_TYPE 2
void SettingMenuController::switchGuiLanguageSlot(int languageType)
{
    qDebug()<< "set language type  "<< languageType;
    if(languageType >= LANGUAGE_MAX_TYPE)
    {
        return;
    }
    APPConfig::get()->setGuiLanguage(languageType);
    return;
}
void SettingMenuController::setMesDependSlot(bool depend,bool isOne)
{
    APPConfig::get()->setMesDepend(depend);
    TestManager::get()->setMesDepend(depend);
    if(!isOne)
    {
        SQLiteManager::get()->insert(MES_DEPEND_CONTROL,(depend==false?1:0));
    }
}

void SettingMenuController::setSSHPrivateKeyFileSlot(const QString & fileName)
{
    QSharedPointer<TipController> tip = QSharedPointer<TipController>(new TipController(this->tool->parentWidget()));
    bool accept = tip->showTipWindowSlot(QString("确定设置证书为该文件？"),MODAL,OKCANCEL,TIPS);
    if(accept)
    {
        LinkManager::get()->setSSHPrivateKeyFile(fileName);
        SQLiteManager::get()->insert(SSH_PRIVATE_KEY_CONTROL,1,fileName);
    }
    else
    {

    }
}

void SettingMenuController::setMesDeviceIdModifyAuth(bool enable)
{
    qDebug()<< "mes id can modify"<<enable;
    TestManager::get()->setMesDeviceIdModifyEnable(enable);
}
