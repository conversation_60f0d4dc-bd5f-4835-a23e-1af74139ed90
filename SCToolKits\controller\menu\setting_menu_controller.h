#ifndef SETTINGMENUCONTROLLER_H
#define SETTINGMENUCONTROLLER_H
#include "interface_controller.h"
#include "menu_action_def.h"
#define ADMIN_ACCOUNT "Admin"
#define ADMIN_PASSWORD "Wb123456"

class SettingMenuController:public IController
{
    Q_OBJECT
public:
    SettingMenuController();
    ~SettingMenuController();
public:
    void showWindow(QWidget * parent);
    void setWindow(QWidget * );
    QWidget * buildWindow(QWidget * parent=nullptr);

signals:
    void authorityResultSignal(bool ret,int type);
    void logOutToFileStatusSignal(bool ret);
private slots:
    void setLogOutEnableSlot(bool enable);
    void setRootModelSlot(bool enable);
    void setToolSelectEnableSlot(bool enable);
    void checkAuthoritySlot(const QString &acc, const QString &pwd);
    void setSSHRecontTimeSlot(int );
    void switchGuiLanguageSlot(int languageType);
    void setMesDependSlot(bool notDepend,bool isOne=false);
    void setSSHPrivateKeyFileSlot(const QString &);
    void setMesDeviceIdModifyAuth(bool enable);

private:
    QWidget *tool;
};

#endif // SETTINGMENUCONTROLLER_H
