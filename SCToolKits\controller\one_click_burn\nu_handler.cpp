#include "nu_handler.h"
#include<QThread>
#include "interface_controller.h"
#include "flash_writer/nu_writer/nu_security_a35_writer.h"
#include "one_click_burn/one_click_burn_window.h"
NuHandler::Nu<PERSON>andler(IController *c,QWidget*w):IBurnHandler(c,w)
{
    if(writer == nullptr)
    {
         writer = new NuSecurityA35Writer();
//         writer->setValue(0x55aaaa55);

         connect(this,&NuHandler::startIdentityAuthSignal,writer,&NuSecurityA35Writer::startIdentityAuth);
         connect(this,&NuHandler::startWiretSignal,writer,&NuSecurityA35Writer::startWriter);
         connect(writer,&NuSecurityA35Writer::identityAuthResultSignal,this,[this](bool ret)
         {
             hasAuth=ret;
             emit identityAuthResultSignal(ret);
         });
         connect(writer,&NuSecurityA35Writer::testResultSignal,this,&NuHandler::processTestSlot);
         OneClickBurnWindow * realWindow = dynamic_cast<OneClickBurnWindow*>(w);
         if(realWindow)
         {
             connect(writer,&NuSecurityA35Writer::displayInfoSignal,realWindow,&OneClickBurnWindow::displayNUwriteInfo);
             connect(this,&NuHandler::progressSignal,realWindow,&OneClickBurnWindow::updateUpgradeProgress);
             connect(this,&NuHandler::finishTestSignal,realWindow,&OneClickBurnWindow::upgradeFinished);
         }
         writerThread = new QThread();
         writer->moveToThread(writerThread);

    }
}
int NuHandler::start(const QString & ctx)
{
    Q_UNUSED(ctx);
    if(!writerThread->isRunning())
    {
         writerThread->start();
    }
    if(progressTimer ==nullptr)
    {
        progressTimer = new QTimer();
        connect(progressTimer,&QTimer::timeout,[this]()
        {
            if(progressValue<90)
            {
                emit progressSignal(progressValue++);
            }
            else
            {
                emit progressSignal(progressValue);
            }
        });
    }

    emit progressSignal(progressValue++);
    emit startWiretSignal();
    return 0;
}
void NuHandler::processTestSlot(int ret,const QString & msg )
{
    if(ret == -2)
    {
       //正式烧录
       emit progressSignal(progressValue++);
       progressTimer->start(1000);

    }
    else
    {
        if(ret == 1)
        {
            emit progressSignal(100);
        }
        else
        {
            emit progressSignal(progressValue);
        }
        progressTimer->stop();
        progressValue=5;
        emit finishTestSignal(ret,msg);
    }
}
int NuHandler::processIdentiyAuth()
{
    if(!writerThread->isRunning())
    {
         writerThread->start();
    }
    if(needIdentityAuth())
    {
        emit freezeGuiSignal();
        emit startIdentityAuthSignal();
        return 0;
    }
   return 0;
}
bool NuHandler::needIdentityAuth()
{
    return hasAuth==false?true:false;
}

