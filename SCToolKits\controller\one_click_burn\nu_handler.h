#ifndef NUHANDLER_H
#define NUHANDLER_H
#include<QWidget>
#include "interface_burn_handler.h"

class IController;
class NuSecurityA35Writer;
class NuHandler : public IBurnHandler
{
    Q_OBJECT
public:
    NuHandler(IController *c=nullptr,QWidget*w=nullptr);
    int start(const QString & ctx ="")override;
    int processIdentiyAuth()override;

signals:
    void startIdentityAuthSignal();
    void freezeGuiSignal();
    void identityAuthResultSignal(bool,int type =0);
    void startWiretSignal();
    void progressSignal(int value);
    void finishTestSignal(bool,const QString &);
private:
    bool needIdentityAuth();
private slots:
    void processTestSlot(int,const QString &);
private:

    bool hasAuth {false};
    NuSecurityA35Writer *writer {nullptr};
    QThread*  writerThread {nullptr};
    QTimer * progressTimer {nullptr};
    int progressValue {5};
};

#endif // NUHANDLER_H
