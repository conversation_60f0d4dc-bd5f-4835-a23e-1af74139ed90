#include "one_click_burn_controller.h"
#include "flash_writer/JLink/jlink_write_worker.h"
#include "flash_writer/usb/usb_write_worker.h"
#include "flash_writer/light_board_upgrade/light_board_upgrade_worker.h"
#include "flash_writer/flash_writer_worker.h"
#include "factory_line/factory_line_context.h"
#include "nu_handler.h"
#include "interface_burn_handler.h"
OneClickBurnController::OneClickBurnController():tool(nullptr),
                                                 inputAddressOk(false),
                                                 JLinkIsReady(false),
                                                 selectFileOk(false),
                                                 taskStart(false),
                                                 selectBurnFileOk(false),
                                                 isReConstructTest(false),
                                                 upgradePLCWorker(nullptr)
{
    TestManager * testManager = TestManager::get();
    connect(this,&OneClickBurnController::startBurnSignal,testManager, &TestManager::startTestSlot);
    connect(this,&OneClickBurnController::tipWindowSignal,testManager,&TestManager::tipCtxSignal);
    connect(this,&OneClickBurnController::mesUplostDataInfoCtx, testManager,&TestManager::updateMesUploadDataInfoCtxSlot);
    connect(testManager, &TestManager::finishedResult, this, &OneClickBurnController::flashWriteResultSlot);
}

OneClickBurnController::~OneClickBurnController()
{

}
void OneClickBurnController::getControlWindowInfo(bool &setMax, bool &hideMes, bool &hideCom, bool &hideSn, bool &hideCan)
{
    if(ToolKitsManager::get()->getCurrentToolFunction() == UNIFIED_FLASH_WRITE_E)
    {
        setMax=true;
        hideMes=false;
        hideCom = true;
        hideSn=true;
        hideCan=true;
    }
    else if(ToolKitsManager::get()->getCurrentToolFunction() == SCHNEIDER_FLASH_WRITE_E)
    {
        setMax=false;
        hideMes=false;
        hideCom = true;
        hideSn=true;
        hideCan=true;
    }
    else if(ToolKitsManager::get()->getCurrentToolFunction() == A35_NU_WRITER_SECURITY_E)
    {
        setMax=true;
        hideMes=true;
        hideCom = false;
        hideSn=true;
        hideCan=true;
    }
}
void OneClickBurnController::showWindow(QWidget * parent)
{
    // TODO parent 变更的情况
    if(tool == nullptr)
    {
        tool = new OneClickBurnWindow(parent);
    }

    QString eqiupmentCode;
    ToolKitsManager::get()->getEquipment(eqiupmentCode);

    funType = ToolKitsManager::get()->getFunType(eqiupmentCode);
    FlashWriteType writeType = UNIFIED_WRITE;
    if(funType == LIGHT_BOARD_FLASH_WRITE_E)
    {
        writeType = LIGHT_BOARD;
    }
    else if(funType == SCHNEIDER_FLASH_WRITE_E)
    {
        writeType = SND_WRITE;
        srcPath = "//**********/ShareDrive/文控中心/02 技术文件/内部文件/11-1.车间软件/1.正式/";
        dstPath = QDir::currentPath() + "/firmware/";
//        srcPath = "D://TEST/From/";
//        dstPath = "D://TEST/To/";
    }
    else if(funType == UPGRADE_EVCC_PLC_FIRMWARE_E)
    {
        writeType = UPGRADE_EVCC_PLC;
    }
    else if(funType == A35_NU_WRITER_SECURITY_E)
    {
        writeType = A35_SECUR_NU_WRITER;
    }

    emit flashWriteTypeSignal(writeType);
    tool->show();
}
QWidget * OneClickBurnController::buildWindow(QWidget * parent)
{
    if(tool == nullptr)
    {
        OneClickBurnWindow * realTool = new OneClickBurnWindow(parent);

        connect(realTool,&OneClickBurnWindow::selectFileSignal,this,&OneClickBurnController::selectFileSlot);
        connect(realTool,&OneClickBurnWindow::updateAddress,this,&OneClickBurnController::saveAddress);
        connect(realTool,&OneClickBurnWindow::startUpgradeSignal,this,&OneClickBurnController::startUpgradeSlot);
        connect(realTool,&OneClickBurnWindow::startJLinkBurnSignal,this,&OneClickBurnController::startJLinkBurnSlot);
        connect(realTool,&OneClickBurnWindow::startSchneiderBurnSignal,this,&OneClickBurnController::startSchneiderBurnSlot);
        connect(realTool,&OneClickBurnWindow::snCodeInputFinishedSignal,this,&OneClickBurnController::handleSnCodeSlot);
        connect(realTool,&OneClickBurnWindow::stmCodeInputFinishedSignal,this,&OneClickBurnController::handleStmCodeSlot);
        connect(realTool,&OneClickBurnWindow::getProjectIdInfo,this,&OneClickBurnController::getProjectIdInfoSlot);
        connect(realTool,&OneClickBurnWindow::checkSnSignal,this,&OneClickBurnController::checkSNSlot);
        connect(realTool,&OneClickBurnWindow::getProjectInfoSiganl,this,&OneClickBurnController::getProjectInfoSlot);
        connect(realTool,&OneClickBurnWindow::selectBurnFileSignal,this,&OneClickBurnController::parseSelectBurnFileSlot);

        //
        connect(realTool,&OneClickBurnWindow::coreBoardFirmwareFileSginal,this,&OneClickBurnController::setCoreFirmware);
        //通过secc给evcc_plc升级
        connect(realTool,&OneClickBurnWindow::startProcessSignal,this,&OneClickBurnController::startProcessSignal);

        connect(this,&OneClickBurnController::fileSelectSuccessSignal,realTool,&OneClickBurnWindow::updateSelectFileName);
        connect(this,&OneClickBurnController::throwUpgradeProgress,realTool,&OneClickBurnWindow::updateUpgradeProgress);
        connect(this,&OneClickBurnController::dispalyJLinkWriteData,realTool,&OneClickBurnWindow::updateJLinkWriteDataDisplay);
        connect(this,&OneClickBurnController::flashWriteFinishedSignal,realTool,&OneClickBurnWindow::updateJLinkWriteResult);
        connect(this,&OneClickBurnController::flashWriteProgressCtx,realTool,&OneClickBurnWindow::updateJLinkWriteProgress);
        connect(this,&OneClickBurnController::flashWriteTypeSignal,realTool,&OneClickBurnWindow::switchGUIDisplay);
        connect(this,&OneClickBurnController::getFirmwareResultSignal,realTool,&OneClickBurnWindow::getFirmwareResultSlot);
        connect(this,&OneClickBurnController::checkStmQRCodeResultSignal,realTool,&OneClickBurnWindow::checkStmQRCodeResult);
        connect(this,&OneClickBurnController::updateBrunLogSignal,realTool,&OneClickBurnWindow::updateLogSlot);
        connect(this,&OneClickBurnController::mesUploadResult,realTool,&OneClickBurnWindow::uploadMesUploadResult);
        connect(this,&OneClickBurnController::updateWorkOrderInfo,realTool,&OneClickBurnWindow::updateWorkOrderInfoSlot);
        connect(this,&OneClickBurnController::updateBurnInfo,realTool,&OneClickBurnWindow::updateBurnInfoSlot);
        connect(this,&OneClickBurnController::updateMaterialCodeInfo,realTool,&OneClickBurnWindow::updateMaterialCodeInfoSlot);
        connect(this,&OneClickBurnController::updateGuiTableData,realTool,&OneClickBurnWindow::updateGuiTableDisplay);

        //通过secc给evcc_plc升级
        connect(this,&OneClickBurnController::updateVersion,realTool,&OneClickBurnWindow::updateVersion);
        connect(this,&OneClickBurnController::updateAttenuationValue,realTool,&OneClickBurnWindow::updateAttenuationValue);
        connect(this,&OneClickBurnController::updateEVCCStatus,realTool,&OneClickBurnWindow::updateEVCCStatus);
        connect(this,&OneClickBurnController::progressIncreaseFinished,realTool,&OneClickBurnWindow::updateProgressBar);
        connect(this,&OneClickBurnController::updateLogMsgSignal,realTool,&OneClickBurnWindow::updateLogMsg);
        connect(this,&OneClickBurnController::upgradeFinishedSignal,realTool,&OneClickBurnWindow::upgradeFinished);
        connect(this,&OneClickBurnController::sshLinkStaSignal,realTool,&OneClickBurnWindow::updateSSHStatus);
        connect(this,&OneClickBurnController::tcpLinkStaSignal,realTool,&OneClickBurnWindow::updateTCPStatus);
        connect(this,&OneClickBurnController::updateTestStatusSignal,realTool,&OneClickBurnWindow::restEnv);


        MesManager * mesManager = MesManager::get();
        connect(mesManager,&MesManager::schneiderBurnInfoSignal,this,&OneClickBurnController::parseSchneiderBrunInfoSlot);
        connect(mesManager,&MesManager::loginMesResultSignal,realTool,&OneClickBurnWindow::updateMesLogStatus);
        connect(mesManager,&MesManager::uploadFinishSignal,this,&OneClickBurnController::uploadResultSlot);
        connect(mesManager,&MesManager::burnInfoSignal,this,&OneClickBurnController::parseBurnInfoByMes);
        connect(mesManager,&MesManager::materialInofSignal,this,&OneClickBurnController::parseMaterialInfoByMes);
        connect(mesManager,&MesManager::workOrderInfoSignal,this,&OneClickBurnController::parseWorkOrderInfoByMes);
        connect(mesManager,&MesManager::checkSNInfoSignal,this,&OneClickBurnController::parseCheckSNInfoByMes);
        connect(mesManager,&MesManager::equipmentsInfoSignal,this,&OneClickBurnController::parseEquipmentsInfoByMes);

        tool = realTool;
        buildBusiness();
    }

    return tool;
}

bool OneClickBurnController::buildBusiness()
{
    ToolFunctionType funType = ToolKitsManager::get()->getCurrentToolFunction();
    if(funType == SCHNEIDER_FLASH_WRITE_E)
    {
        USBWriteWorker * usbWorker = new USBWriteWorker();
        TestManager::get()->updateCrossMistakeProofing(SCHNEIDER_FLASH_WRITE_E,false);
        TestManager::get()->updateSelectWorkerCtx(SCHNEIDER_FLASH_WRITE_E);

        connect(usbWorker,&USBWriteWorker::tipCtxSignal,TestManager::get(),&TestManager::tipCtxSignal);
        connect(usbWorker,&USBWriteWorker::burnProgressCtx,this, &OneClickBurnController::flashWriteProgressCtx);
        connect(usbWorker,&USBWriteWorker::processCtxSignal,this, &OneClickBurnController::updateBrunLogSignal);
        connect(usbWorker,&USBWriteWorker::finished, TestManager::get(), &TestManager::finishedResult);

        if(isReConstructTest)
        {
            FlashWriterWorker * writerWorker = new FlashWriterWorker();
            TestManager::get()->addTestWorker(writerWorker);
            connect(this,&OneClickBurnController::writerPreStepSignal,writerWorker,&FlashWriterWorker::handleWriterPreStepSlot);
        }
        else
        {
            TestManager::get()->addTestWorker(usbWorker);
        }

    }
    else if(funType == UNIFIED_FLASH_WRITE_E)
    {
        JLinkWriteWorker * jlinkWorker = new JLinkWriteWorker();

        TestManager::get()->updateCrossMistakeProofing(UNIFIED_FLASH_WRITE_E,false);
        TestManager::get()->updateSelectWorkerCtx(UNIFIED_FLASH_WRITE_E);
        connect(jlinkWorker,&JLinkWriteWorker::tipCtxSignal,TestManager::get(),&TestManager::tipCtxSignal);
        connect(jlinkWorker,&JLinkWriteWorker::jlinkWriteProgressCtx,this, &OneClickBurnController::flashWriteProgressCtx);

        //
        if(isReConstructTest)
        {
            FlashWriterWorker * writerWorker = new FlashWriterWorker();
            TestManager::get()->addTestWorker(writerWorker);
            connect(this,&OneClickBurnController::writerPreStepSignal,writerWorker,&FlashWriterWorker::handleWriterPreStepSlot);
        }
        else
        {
            TestManager::get()->addTestWorker(jlinkWorker);
        }

        SQLiteManager::get()->createBurnWorkOrderTable();
        SQLiteManager::get()->createBurnRecordDataTable();
    }
    else if (funType == LIGHT_BOARD_FLASH_WRITE_E)
    {
        LightBoardUpgradeWorker * upgradeWorker = new LightBoardUpgradeWorker();
        TestManager::get()->addTestWorker(upgradeWorker);
        TestManager::get()->updateCrossMistakeProofing(LIGHT_BOARD_FLASH_WRITE_E,false);
        TestManager::get()->updateSelectWorkerCtx(LIGHT_BOARD_FLASH_WRITE_E);
    }
    else if (funType == COMMON_NU_WRITER_E)
    {
        FlashWriterWorker * writerWorker = new FlashWriterWorker();
        TestManager::get()->addTestWorker(writerWorker);
        connect(this,&OneClickBurnController::writerPreStepSignal,writerWorker,&FlashWriterWorker::handleWriterPreStepSlot);
        TestManager::get()->updateCrossMistakeProofing(COMMON_NU_WRITER_E,false);
        TestManager::get()->updateSelectWorkerCtx(COMMON_NU_WRITER_E);
    }
    else if(funType == UPGRADE_EVCC_PLC_FIRMWARE_E)
    {
        upgradePLCWorker = new UpgradeEVCCPLCWorker();
        TestManager::get()->addTestWorker(upgradePLCWorker);
        TestManager::get()->updateCrossMistakeProofing(UPGRADE_EVCC_PLC_FIRMWARE_E,false);
        TestManager::get()->updateSelectWorkerCtx(UPGRADE_EVCC_PLC_FIRMWARE_E);

        //业务相关
        connect(this, &OneClickBurnController::startProcessSignal, upgradePLCWorker, &UpgradeEVCCPLCWorker::preparationStep);

        //通过secc给evcc_plc升级
        connect(upgradePLCWorker,&UpgradeEVCCPLCWorker::parseVersionFinished,this,&OneClickBurnController::updateVersion);
        connect(upgradePLCWorker,&UpgradeEVCCPLCWorker::parseAttenuationValueFinished,this,&OneClickBurnController::updateAttenuationValue);
        connect(upgradePLCWorker,&UpgradeEVCCPLCWorker::connectEVCCFinishedSignal,this,&OneClickBurnController::updateEVCCStatus);
        connect(upgradePLCWorker,&UpgradeEVCCPLCWorker::progressIncreaseFinished,this,&OneClickBurnController::progressIncreaseFinished);
        connect(upgradePLCWorker,&UpgradeEVCCPLCWorker::logMsgSignal,this,&OneClickBurnController::updateLogMsgSignal);
        connect(upgradePLCWorker,&UpgradeEVCCPLCWorker::upgradeFinishedSignal,this,&OneClickBurnController::upgradeFinishedSignal);
        connect(upgradePLCWorker,&UpgradeEVCCPLCWorker::sshLinkStaSignal,this,&OneClickBurnController::sshLinkStaSignal);
        connect(upgradePLCWorker,&UpgradeEVCCPLCWorker::tcpLinkStaSignal,this,&OneClickBurnController::tcpLinkStaSignal);

        //更新testmanager中的标志
        TestManager *testManager = TestManager::get();
        connect(upgradePLCWorker,&UpgradeEVCCPLCWorker::updateTestStatusSignal,testManager,&TestManager::updateTestStatusSignal);

        //
        connect(upgradePLCWorker,&UpgradeEVCCPLCWorker::updateTestStatusSignal,this,&OneClickBurnController::updateTestStatusSignal);

    }
    else if (funType == A35_NU_WRITER_SECURITY_E)
    {
        if(businessHandler == nullptr)
        {
            NuHandler * handler = new NuHandler(this,tool);

            OneClickBurnWindow * realTool = dynamic_cast<OneClickBurnWindow*>(tool);
            if(realTool)
            {
                connect(realTool,&OneClickBurnWindow::identityAuthSingal,handler,&IBurnHandler::processIdentiyAuth);
                connect(handler,&NuHandler::identityAuthResultSignal,realTool,&OneClickBurnWindow::processAuthResult);
            }
            businessHandler =handler;
        }
    }
    return true;
}
// 灯板升级相关函数
void OneClickBurnController::selectFileSlot()
{
    QString fileName = QFileDialog::getOpenFileName(tool, tr("Open File"),
                                                    "/home",
                                                    tr("All Files (*)"));
    if(fileName.isEmpty())
    {
        return;
    }

    emit fileSelectSuccessSignal(fileName);
    DeviceContext::get()->setMCUFirmware(fileName);

    filePath = fileName;
    selectFileOk = true;
    return;
}
void OneClickBurnController::setCoreFirmware(const QString & file)
{
    DeviceContext::get()->setCoreBoardFirmware(file);
    filePath = file;
    selectFileOk = true;
}
void OneClickBurnController::startUpgradeSlot()
{
    if(businessHandler)
    {
        businessHandler->start();
        return;
    }
    ToolFunctionType funType = ToolKitsManager::get()->getCurrentToolFunction();
    if(funType == LIGHT_BOARD_FLASH_WRITE_E)
    {
        if(!inputAddressOk)
        {
            tipWindowSignal("请输入灯板拨码对应的地址", MODAL, OK, WARNING);
            return;
        }
    }

    if(!selectFileOk)
    {
        emit updateTestStatusSignal(false);
        tipWindowSignal("请先选择固件包！", MODAL, OK, WARNING);
        return;
    }

    WriteFirmwareData otaFileData;
    otaFileData.path = filePath;
    otaFileData.address = lampBoardAddress;
    InterfaceData::get()->setData(otaFileData);
    QString toolName = "oneClickBurnTool";
    emit startBurnSignal(toolName);
}

void OneClickBurnController::saveAddress(int address)
{
    qDebug()<<"lp address:"<<address;
    if((address > LP_ADDRESS_LOWER_LIMIT) && (address <= LP_ADDRESS_UPPER_LIMIT))
    {
        lampBoardAddress = address;
        inputAddressOk = true;
    }
}

// 统一烧录相关
void OneClickBurnController::startJLinkBurnSlot()
{
    if(taskStart)
    {
        emit tipWindowSignal("烧录已经开始，请勿重复点击！", MODAL, OK, WARNING);
        return;
    }

    if(!selectBurnFileOk)
    {
        emit tipWindowSignal("选择的烧录固件不符合当前上位机，请重新选择 ！", MODAL, OK, WARNING);
        return;
    }

//    QString firmwareSrcPath = "//**********/ShareDrive/文控中心/02 技术文件/内部文件/11-1.车间软件/1.正式/";
    QString firmwareSrcPath = "D://TEST/From/";

    QStringList data;
    writeData.firmwareSrcPath = firmwareSrcPath;
    writeData.scriptPath = QDir::currentPath() + "/script/";
    writeData.firmwareDstPath = QDir::currentPath() + "/firmware/";
    InterfaceData::get()->setData(writeData);

    emit tipWindowSignal("烧录中，请耐心等待·····", MODELESS, NO, TIPS);

    taskStart = true;
    QString toolName = "oneClickBurnTool";
    emit startBurnSignal(toolName);
}

void OneClickBurnController::uploadResultToMes(bool result)
{
    QString res = (result ? "OK" : "NG");
    MesLoginContext mesInfo;

    QJsonObject object;
    object.insert("DEVICE_SN", DeviceContext::get()->getMesLogicDeviceId());
    object.insert("SN", deviceSN);
    object.insert("EMPID", DeviceContext::get()->getMesLoginAccount());
    object.insert("RESULT", res);
    QDateTime time = QDateTime::currentDateTime();
    long timeStr = time.toTime_t();
    object.insert("TEST_TIME",QJsonValue(QString::number(timeStr)));

    QJsonObject obj;
    obj.insert("PROJECT_ID", projectId);
    obj.insert("BURN_RESULT", res);
    obj.insert("TEST_RESULT", res);
    obj.insert("MAIN_VERSION_NAME", "");
    obj.insert("MAIN_FILE_PATH", "");
    obj.insert("MAIN_BURN_RESULT", "");
    obj.insert("MAIN_BURN_TIME", "");
    obj.insert("SUB_VERSION_NAME", "");
    obj.insert("SUB_FILE_PATH", "");
    obj.insert("SUB_BURN_RESULT", "");
    obj.insert("SUB_BURN_TIME", "");
    QJsonArray jsonARRAY;
    jsonARRAY.append(QJsonValue(obj));
    object.insert("OP_DATA", jsonARRAY);

    TestManager::get()->setSnCode(deviceSN);
    emit mesUplostDataInfoCtx(QString(QJsonDocument(object).toJson(QJsonDocument::Compact)));

//    TestManager::get()->uploadToMesSlot();
}

void OneClickBurnController::getProjectIdInfoSlot(QString & sapCode)
{
    //参数判定的空，直接抛出异常。
    FactoryLineContext::get()->setSAPCode(sapCode);
    if(isReConstructTest)
    {
        emit writerPreStepSignal(0);
    }
    else
    {
        deviceMesWorker.getWorkeOrderInfoSlot(sapCode);

    }
}

void OneClickBurnController::getProjectInfoSlot(QString & projectSn)
{
    projectSN = projectSn;
    DeviceContext::get()->setSN(projectSN);
    deviceMesWorker.getEquipmentsInfoFromMes();
}

void OneClickBurnController::parseEquipmentsInfoByMes(bool ret, const QString & msg, const QJsonObject & info)
{
    qDebug() << "Equipments info :" << info;
    if(ret)
    {
        businessOrderNo = info.value("businessOrderNo").toString();
        projectId = info.value("projectId").toString();
        if(businessOrderNo.isEmpty())
        {
            deviceMesWorker.getBurnInfoSlot(businessOrderNo, projectSN);
        }
        else
        {
            deviceMesWorker.getWorkeOrderInfoSlot(businessOrderNo);
        }
    }
    else
    {
        QString tip = QString("产品信息获取失败，原因：%1").arg(msg);
        emit tipWindowSignal(tip, MODAL, OK, TIPS);
    }
}

void OneClickBurnController::checkSNSlot(QStringList & infoList)
{
    qDebug() << infoList << infoList.count();

    if(infoList.count() >= 6)
    {
        businessOrderNo = infoList[0];
        materialCode = infoList[1];
        itemCode =infoList[2];
        deviceSN = infoList[3];
        actionType = infoList[4];
        currProgramName = infoList[5];
        if(isReConstructTest)
        {
            DeviceContext::get()->setMCUFirmware("WB-01_GZF_A001_S11_ADQCPN1006_BC_V214.016.000_20240127_CCU.mot");
//            DeviceContext::get()->setMCUFirmware(infoList[5]);
        }
        else
        {
             DeviceContext::get()->setMCUFirmware(infoList[5]);
        }

        FactoryLineContext::get()->setMaterialCode(infoList[3]);
        if(isReConstructTest)
        {
            QString toolName = "oneClickBurnTool";
            emit startBurnSignal(toolName);
        }
        else
        {
            deviceMesWorker.getCheckSNInfoSlot(infoList);
        }
    }
}

void OneClickBurnController::parseCheckSNInfoByMes(bool ret, const QString & msg, const QJsonObject & info)
{
    qDebug() << "check sn info: " << ret << msg << info;
    checkSnOk = ret;
    if(ret)
    {
        if(!isReConstructTest)
        {
            startJLinkBurnSlot();
        }
    }
    else
    {
        QString tip = QString("物料SN检测失败，原因：%1").arg(msg);
        emit tipWindowSignal(tip, MODAL, OK, TIPS);
    }
}

void OneClickBurnController::parseWorkOrderInfoByMes(bool ret, const QString & msg, const QJsonArray & info)
{
    qDebug() << "work order info :" << ret << msg << info;
    if(ret)
    {
        if(!info.isEmpty())
        {
            QJsonObject obj = info.at(0).toObject();
            QString productMaterial = obj.value("productMaterial").toString();
            QString productName = obj.value("productName").toString();
            projectId = obj.value("projectId").toString();
            QString businessOrder = obj.value("businessOrderNo").toString();
            burnPlanCount = obj.value("productCount").toInt();

            emit updateWorkOrderInfo(productMaterial, productName);
            businessOrderNo = businessOrder;
            if(isReConstructTest)
            {

            }
            else
            {
                deviceMesWorker.getBurnInfoSlot(businessOrderNo, "");
            }
        }
    }
    else
    {
        QString tip = QString("MES获工单信息失败，原因：%1").arg(msg);
        emit tipWindowSignal(tip, MODAL, OK, TIPS);
    }

}

void OneClickBurnController::parseMaterialInfoByMes(bool ret, const QString & msg, const QJsonArray & info)
{
    qDebug() << "material info :" << ret << msg << info;
    if(ret)
    {
        int index = 0;
        for(int i = 0; i < info.count(); i++)
        {
            QJsonObject obj = info.at(i).toObject();
            QString itemCode = obj.value("modelCode").toString();
            QString burn = obj.value("burn").toString();
            QString materialName = obj.value("materialName").toString();

            if(burn == "1") //1:烧录，2:不烧录，暂时只处理烧录物料
            {
                emit updateMaterialCodeInfo(index++, itemCode, materialName);
            }
        }
    }
    else
    {
        QString tip = QString("MES获取管控物料失败，原因：%1").arg(msg);
        emit tipWindowSignal(tip, MODAL, OK, TIPS);
    }
}

void OneClickBurnController::parseBurnInfoByMes(bool ret, const QString & msg, const QJsonArray & info)
{
    qDebug() << "burn info :" << ret << msg << info;
    if(ret)
    {
        burnParameterMap.clear();
        QMap<int, QStringList> burnInfoMap;
        for(int i = 0; i < info.count(); i++)
        {
            QJsonObject obj = info.at(i).toObject();
            QString softwareName = obj.value("softwareName").toString();
            QString softwareFile = obj.value("softwareFile").toString();
            QString burnParameter = obj.value("burnParameter").toString();
            QString burnDetail = obj.value("burnDetail").toString();
            QStringList infoList;
            infoList << softwareName << softwareFile<<QString::number(burnPlanCount);//暂时使用工单总数

            burnParameterMap.insert(softwareFile, burnDetail);
            burnInfoMap.insert(i, infoList);
//            emit updateBurnInfo(i, infoList);
        }
        handleBurnRecord(burnInfoMap);
        if(isReConstructTest)
        {

        }
        else
        {
            deviceMesWorker.getMaterialInfoSlot(businessOrderNo, projectId);
        }
    }
    else
    {
        deviceMesWorker.getMaterialInfoSlot(businessOrderNo, projectId);
        QString tip = QString("MES获取烧录程序失败，原因：%1").arg(msg);
        emit tipWindowSignal(tip, MODAL, OK, TIPS);
    }
}

void OneClickBurnController::parseSelectBurnFileSlot(QString & fileName)
{
    QString burnParameter = burnParameterMap.find(fileName).value();

    if(burnParameter.contains("jflash"))
    {
        selectBurnFileOk = true;
    }
    else
    {
        selectBurnFileOk = false;
    }
    if(isReConstructTest)
    {
        selectBurnFileOk = true;
    }

    burnParameter = "xmc4700.jflash";  //测试用
    fileName = "WB-01_GZF_A001_S11_ADQCPN1006_BC_V214.016.000_20240127_CCU.mot";
    DeviceContext::get()->setMCUFirmware(fileName);

    writeData.firmwareName = fileName;
    writeData.projectFileName = burnParameter;
}

void OneClickBurnController::handleBurnRecord(QMap<int, QStringList> & infoMap)
{
    // 1. 烧录记录表更新
    QString cmd;
    BurnWorkOderData workOrderData;
    int burnFinishedCount = 0;
    for(auto iter=infoMap.begin(); iter!=infoMap.end(); iter++)
    {
        QList<BurnRecordData> data;
        QStringList burnInfo;
        cmd = QString("workordernumber='%1' and programName='%2'").arg(businessOrderNo).arg(iter.value()[1]);
        burnInfo << iter.value()[0] << iter.value()[1];
        SQLiteManager::get()->select(cmd, data);
        if(!data.isEmpty())
        {
            burnFinishedCount += data[0].finishedCount;
            burnInfo << QString::number(data[0].finishedCount);
            burnInfo << QString::number(data[0].planCount);
        }
        else
        {
            BurnRecordData burnData;
            burnData.workOrderNumber = businessOrderNo;
            burnData.programName = iter.value()[1];
            burnData.planCount = iter.value()[2].toInt();
            burnData.finishedCount = 0;
            SQLiteManager::get()->insert(burnData);

            burnInfo << QString::number(0);
            burnInfo << QString::number(iter.value()[2].toInt());
        }

        // 2. 更新界面显示
        emit updateBurnInfo(iter.key(), burnInfo);
    }
    // 3. 工单表更新
    workOrderData.workOrderNumber = businessOrderNo;
    workOrderData.planCount = burnPlanCount;
    workOrderData.finishedCount = burnFinishedCount;
    SQLiteManager::get()->insert(workOrderData);
}

void OneClickBurnController::updateBurnRecord()
{
    // 1. 烧录记录表更新
    BurnRecordData recordData;
    BurnWorkOderData workOrderData;
    QList<BurnRecordData> recordList;
    QList<BurnWorkOderData> workOrderList;

    QStringList burnInfo;
    QString cmd = QString("workordernumber='%1' and programName='%2'").arg(businessOrderNo).arg(currProgramName);
    SQLiteManager::get()->select(cmd, recordList);
    if(!recordList.isEmpty())
    {
        recordData.workOrderNumber = businessOrderNo;
        recordData.programName = currProgramName;
        recordData.finishedCount = recordList[0].finishedCount + 1;
        recordData.planCount = recordList[0].planCount;
    }
    else
    {
        qDebug() << "烧录记录获取失败!";
        return;
    }

    if(SQLiteManager::get()->update(recordData))
    {
        //更新界面显示
        emit updateGuiTableData(currProgramName, recordData.finishedCount);
    }

    // 3. 工单表更新
    cmd = QString("workordernumber='%1'").arg(businessOrderNo);
    SQLiteManager::get()->select(cmd, workOrderList);
    if(!workOrderList.isEmpty())
    {
        workOrderData.workOrderNumber = businessOrderNo;
        workOrderData.planCount = workOrderList[0].planCount;
        workOrderData.finishedCount = workOrderList[0].finishedCount + 1;
    }
    else
    {
        qDebug() << "烧录记录获取失败!";
        return;
    }

    SQLiteManager::get()->insert(workOrderData);
}

// 交流网安版本烧录相关
void OneClickBurnController::startSchneiderBurnSlot()
{
//    QString exePath = "D://STM32Cube/STM32CubeProgrammer/bin/STM32_Programmer_CLI.exe";
    QString exePath("");
    getSTM32ProgrammerPath(exePath);
    if(exePath.isEmpty())
    {
        emit tipWindowSignal("未找到STM32_Programmer_CLI程序！", MODAL, OK, WARNING);
        return;
    }

    writeData.exePath = exePath;
    InterfaceData::get()->setData(writeData);

    //构建MES上传数据
    QJsonObject subjson[1];
    for(int i =0;i<1;i++)
    {
        subjson[i].insert("PROJECT_ID", stmQRcode);
        subjson[i].insert("VERSION", version);
    }
    QJsonArray jsonARRAY;
    for(int i = 0;i<1;i++)
    {
        jsonARRAY.append(QJsonValue(subjson[i]));
    }
    emit mesUplostDataInfoCtx(QString(QJsonDocument(jsonARRAY).toJson(QJsonDocument::Compact)));

    QString toolName = "oneClickBurnTool";
    emit startBurnSignal(toolName);
}

void OneClickBurnController::getJLinkPath(QString &path)
{
    // 环境变量方式
    QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
    path = env.value("JLinkPath");

    path.replace("\\", "/");
    qDebug() << path;
}

void OneClickBurnController::getSTM32ProgrammerPath(QString & path)
{
    path = "D:\\STM32CubeProgrammer\\bin\\STM32_Programmer_CLI.exe";
    QFile file(path);
    if(!file.exists())
    {
        path = "";
    }
    else
    {
        return;
    }

    QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
    path = env.value("STM32CLI");

    path.replace("\\", "/");
}

void OneClickBurnController::flashWriteResultSlot(bool result)
{
    if(!result)
    {
//        switchOK = tipWindowSignal("烧录失败，是否需要手动上传MES",MODAL,OKCANCEL,TIPS);

//        if(switchOK==true)
//        {
//            uploadResultToMes(result);
//        }
    }
    else
    {
        uploadResultToMes(result);
    }

    taskStart = false;
    checkSnOk = false;
    emit flashWriteFinishedSignal(result);
}

//施耐德烧录
void OneClickBurnController::handleSnCodeSlot(QString & snCode)
{
    if(snCode.isEmpty())
    {
        emit tipWindowSignal("PBC码不能为空！", MODAL, OK, WARNING);
        return;
    }
    DeviceContext::get()->setSN(snCode);
    if(isReConstructTest)
    {
        emit writerPreStepSignal(0);
    }
    else
    {
        deviceMesWorker.getSchneiderBurnInfoSlot();
    }
}

void OneClickBurnController::handleStmCodeSlot(QString & stmCode)
{
    if(stmCode.isEmpty())
    {
        emit checkStmQRCodeResultSignal(false);
        emit tipWindowSignal("核心板二维码不能为空！", MODAL, OK, WARNING);
        return;
    }
    stmQRcode = stmCode;
    emit checkStmQRCodeResultSignal(true);
}

void OneClickBurnController::parseSchneiderBrunInfoSlot(bool ret, const QString & ctx, const QJsonArray & data)
{
    qDebug() << "----->burn Info: " << ret << ctx << data;
    if(ret)
    {
        QJsonObject obj;
        for(int i = 0; i < data.size(); i++)
        {
            obj = data.at(i).toObject();
            if(obj.value("SOFTWARE_NAME") == QString::fromUtf8("主板程序"))
            {
                //处理烧录程序
                QString firmwareName = obj.value("SOFTWARE_FILE").toString();
//                QString firmwareName = "0.1.8_BuringPackage_20240716";
//                version = "V1.0.0";
                version = firmwareName.mid(firmwareName.indexOf("#")+1);
                QString firmwareDstPath = dstPath + firmwareName;
                QString firmwareSrcPath = srcPath + firmwareName;
                if(checkDirExist(firmwareDstPath))
                {
                    emit getFirmwareResultSignal(true, firmwareDstPath);
                }
                else
                {
                    qDebug()<<"fromPath"<< firmwareSrcPath;
                    qDebug()<<"toPath"<< firmwareDstPath;

                    if(copyDirectoryFiles(firmwareSrcPath,firmwareDstPath,1))
                    {
                        qDebug()<<"烧录文件下载成功！请扫描核心板二维码！";
                        writeData.firmwareSrcPath = firmwareSrcPath;
                        writeData.firmwareDstPath = firmwareDstPath;
                        emit getFirmwareResultSignal(true, firmwareDstPath);
                        return;
                    }
                    else
                    {
                        qDebug()<<"配置文件下载失败！";
                        return;
                    }
                }
                break;
            }
        }
    }
    else
    {
        QString tip = QString("MES获取烧录程序失败，原因：%1").arg(ctx);
        emit tipWindowSignal(tip, MODAL, OK, TIPS);
    }
}

void OneClickBurnController::uploadResultSlot(bool sta, const QString &ctx)
{
    QString str = ctx;
    if(sta)
    {
        emit mesUplostDataInfoCtx("");
        emit tipWindowSignal("上传成功", MODAL, OK, TIPS);

        //数据库更新
        if(funType == UNIFIED_FLASH_WRITE_E)
        {
            updateBurnRecord();
        }
    }
    else
    {
        if(str.isEmpty())
        {
            str = QString::fromUtf8("mes通信错误,请排查问题!");
        }
        str = "上传MES失败！原因："+str;
        emit tipWindowSignal(str, MODAL, OK, TIPS);
    }
    emit mesUploadResult(sta);
}

bool OneClickBurnController::checkDirExist(QString & dirName)
{
    QDir dir(dirName);
    if(dir.exists(dirName))
    {
        return true;
    }
    return false;
}

bool OneClickBurnController::copyDirectoryFiles(QString & fromPath, QString & toPath, bool coverEnable)
{
    QDir sourceDir(fromPath);
    QDir targetDir(toPath);
    if(!targetDir.exists())
    {
        if(!targetDir.mkdir(targetDir.absolutePath()))
        {
            return false;
        }
    }

    QFileInfoList fileInfoList = sourceDir.entryInfoList();
    if (!sourceDir.exists())
    {
        return false;
    }

    foreach(QFileInfo fileInfo, fileInfoList)
    {
        if(fileInfo.fileName() == "." || fileInfo.fileName() == "..")
        {
            continue;
        }

        if(fileInfo.isDir())
        {
            QString fileFromPath = fileInfo.filePath();
            QString fileToPath = targetDir.filePath(fileInfo.fileName());
            if(!copyDirectoryFiles(fileFromPath,fileToPath,coverEnable))
            {
                return false;
            }
        }
        else
        {
            if(coverEnable && targetDir.exists(fileInfo.fileName()))
            {
                targetDir.remove(fileInfo.fileName());
            }
            if(!QFile::copy(fileInfo.filePath(),
                targetDir.filePath(fileInfo.fileName())))
            {
                return false;
            }
        }
    }
    return true;
}
