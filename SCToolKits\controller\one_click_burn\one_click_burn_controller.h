﻿#ifndef ONECLICKBURNCONTROLLER_H
#define ONECLICKBURNCONTROLLER_H

#include <QObject>
#include "interface_controller.h"
#include "data/interface_data/interface_data.h"
#include "gui/tip_window.h"
#include "gui/one_click_burn/one_click_burn_window.h"
#include <QDir>
#include "interface_data/interface_data.h"
#include "business/common_share/test_manager.h"
#include "mes/mes_manager.h"
#include <QFileDialog>
#include <QMessageBox>
#include <QDebug>
#include "database/sqlite_manager.h"
#include "devices/device_context.h"
#include "common_share/device_mes_worker.h"
#include "secc/upgrade_evcc_plc/upgrade_evcc_plc_worker.h"
#include "communication/link_manager.h"
//#include "nu_handler.h"
class IBurnHandler;
class OneClickBurnController : public IController
{
    Q_OBJECT

public:
    QWidget * buildWindow(QWidget * parent=nullptr)override;
    void showWindow(QWidget * parent)override;
    virtual void getControlWindowInfo(bool &setMax, bool &hideMes, bool &hideCom, bool &hideSn, bool &hideCan) override;

private:
    bool buildBusiness() override;
public slots:
    void selectFileSlot();
    void startJLinkBurnSlot();
    void startSchneiderBurnSlot();
    void saveAddress(int);
    void startUpgradeSlot();
    void flashWriteResultSlot(bool result);

    //schneider
    void handleSnCodeSlot(QString &);
    void handleStmCodeSlot(QString &);

    void parseSchneiderBrunInfoSlot(bool, const QString &, const QJsonArray &);
    void uploadResultSlot(bool sta, const QString &ctx);

    void getProjectIdInfoSlot(QString & sapCode);
    void getProjectInfoSlot(QString & projectSN);
    void checkSNSlot(QStringList &);
    void parseWorkOrderInfoByMes(bool, const QString &, const QJsonArray &);
    void parseBurnInfoByMes(bool, const QString &, const QJsonArray &);
    void parseMaterialInfoByMes(bool, const QString &, const QJsonArray &);
    void parseCheckSNInfoByMes(bool, const QString &, const QJsonObject &);
    void parseEquipmentsInfoByMes(bool, const QString &, const QJsonObject &);
    void uploadResultToMes(bool);
    void parseSelectBurnFileSlot(QString &);
    void setCoreFirmware(const QString &);

private:
    void getJLinkPath(QString &);
    void getSTM32ProgrammerPath(QString &);

    bool checkDirExist(QString &);
    bool copyDirectoryFiles(QString &, QString &, bool);

    void handleBurnRecord(QMap<int, QStringList> &);
    void updateBurnRecord();
signals:
    void fileSelectSuccessSignal(QString &);
    void startBurnSignal(QString &);
    bool tipWindowSignal(QString tips, MSG_MODE mode, MSG_TYPE msgType, MSG_TIP_TYPE tipType);
    void throwUpgradeProgress(int);
    void dispalyJLinkWriteData(QStringList & data);
    void flashWriteFinishedSignal(bool);
    void flashWriteProgressCtx(const QString &, int, int);
    void flashWriteTypeSignal(FlashWriteType type);

    void getFirmwareResultSignal(bool, QString &);
    void checkStmQRCodeResultSignal(bool);
    void updateBrunLogSignal(const QString &);
    void mesUploadResult(bool);
    void mesUplostDataInfoCtx(const QString &);

    void updateWorkOrderInfo(QString &, QString &);
    void updateBurnInfo(int, QStringList &);
    void updateMaterialCodeInfo(int, QString &, QString &);
    void updateGuiTableData(QString &, int);

    //
    void writerPreStepSignal(int );

    //通过secc给evcc_plc升级
    void startProcessSignal(int );

    void updateVersion(int, QString &);
    void updateAttenuationValue(QString &);
    void updateEVCCStatus(bool );
    void progressIncreaseFinished(int);
    void updateLogMsgSignal(const QString &, const QString &);
    void upgradeFinishedSignal(bool result,const QString &ctx);
    void sshLinkStaSignal(bool );
    void tcpLinkStaSignal(bool );
    void updateTestStatusSignal(bool );
public:
    OneClickBurnController();
    ~OneClickBurnController();
private:
    QWidget * tool;
    bool inputAddressOk;
    ToolFunctionType funType;
    // 烧录
    bool JLinkIsReady;
    QString JLinkPath;
    WriteFirmwareData writeData;

    // 施耐德
    bool getFirmwareResult;
    QString srcPath;
    QString dstPath;
    QString stmQRcode;
    QString version;

    // 升级
    bool selectFileOk;
    QString filePath;
    int lampBoardAddress;

    //通用
    bool taskStart;
    QString businessOrderNo;
    QString materialCode;
    QString itemCode;
    QString projectSN;
    QString deviceSN;
    QString actionType;
    QString currProgramName;
    QMap<QString, QString> burnParameterMap;
    QString projectId;
    bool selectBurnFileOk;
    bool checkSnOk;
    int burnPlanCount;

    //mes
    DeviceMesWorker deviceMesWorker;
    bool isReConstructTest;//用于重构测试。

    //
    UpgradeEVCCPLCWorker * upgradePLCWorker;


    //
    IBurnHandler * businessHandler {nullptr};
};

#endif // ONECLICKBURNCONTROLLER_H
