#ifndef INTERFACE_HANDLER_H
#define INTERFACE_HANDLER_H
#include<QObject>
#include "interface_controller.h"
#include "workers/test_worker.h"
class IOneClickConfigHandler:public QObject
{
    Q_OBJECT
public:
    IOneClick<PERSON>onfigHand<PERSON>(IController* c):controller(c),worker(nullptr)
    {

    }
    virtual ~IOneClickConfigHandler()
    {

    }
    void setBusinessWorker(TestWorker *w) {worker = w;}
    virtual bool buildBusiness(const QString & ctx)=0;

    virtual bool handleSSHLinkResultSlot(bool status,QString ip,int port) {return true;}
    virtual int handleMaterialCodeInputSlot(const QString & code){return 0;}
    virtual int handleSnInputSlot(const QString & sn){return 0;}

    // 返回值-1，异常，返回0，同步结果正常。返回1，还有后面的数据，可能是通过信号发送
    virtual bool isPreTestConditionOk(const QString & info){return true;}
    virtual int handlePreStepInfo(const QString & data, QString & reason ){return 0;}
    virtual int handleTestResult(bool,const QString & aux=""){return 0;}
signals:
    void runEnvCtxStatusSignal(bool,const QString & aux="");
    void widgetShowControlSignal(int,bool,const QString & aux="");
protected:
    IController * controller;
    TestWorker * worker;
};

#endif // INTERFACE_HANDLER_H
