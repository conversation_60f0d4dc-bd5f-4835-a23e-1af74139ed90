#include "intl_dc_config_handler.h"
#include "one_click_config/international_dc/interantional_dc.h"
#include "rules/sn_rules.h"
#include "link_manager.h"

INTLDCConfigHandler::INTLDCConfigHandler(IController *c):
    IOneClickConfigHandler(c)

{
}
INTLDCConfigHandler::~INTLDCConfigHandler()
{
}

bool INTLDCConfigHandler::buildBusiness(const QString &ctx)
{
    Q_UNUSED(ctx);
    return true;
}

bool INTLDCConfigHandler::requestProductInfo(bool status)
{
    if(status)
    {
        INTLDCConfig * dcConfigWorker = dynamic_cast<INTLDCConfig*>(worker);
        if(dcConfigWorker)
        {
            connect(this,&INTLDCConfigHandler::sshLinkResultSignal,dcConfigWorker,&INTLDCConfig::requestProductInfoSlot,Qt::UniqueConnection);
            connect(dcConfigWorker,&INTLDCConfig::productInfoSignal,this,&INTLDCConfigHandler::handleProductConfirmInfo,Qt::UniqueConnection);
        }
        emit sshLinkResultSignal(status);
    }
    return true;
}
int INTLDCConfigHandler::handleMaterialCodeInputSlot(const QString & code)
{
    bool sshLinkStatus = LinkManager::get()->getSSHLinkedStatus();
    if(sshLinkStatus)
    {
        requestProductInfo(true);
    }

    return 0;
}
bool INTLDCConfigHandler::handleSSHLinkResultSlot(bool status,QString ip,int port)
{
    //处理SSH连接侯，获取产品信息
    if(status)
    {
        requestProductInfo(status);
    }
    return true;
}
void INTLDCConfigHandler::handleProductConfirmInfo(bool isBaseProduct)
{
    QString tip;
    if(isBaseProduct)
    {
        tip="请点击一键配置按键进行测试";
    }
    else
    {
        tip="请扫描二维码";
    }

    emit widgetShowControlSignal(1,isBaseProduct?false:true,tip);
    emit runEnvCtxStatusSignal(true);
}

int INTLDCConfigHandler::handlePreStepInfo(const QString & data,QString & reason)
{
    if(DeviceContext::get()->isBaseProductType())
    {
        return 0;
    }
    else
    {
        //国际直流暂时使用SN号来区分是标品还是定制品。当前施耐德的Sn属于定制品范畴
        QSharedPointer<SNRules> snRules(new SNDSNRules);
        QStringList out,evdOut;
        bool ret = snRules->parse(data,out,reason);
        if(ret == false)
        {
            return -1;
        }
        DeviceContext::get()->setCustomSN(out[1]);
        DeviceContext::get()->setCustomMaterialCode(out[0]);
        DeviceContext::get()->setBaseProductType(false);
        QSharedPointer<SNRules> evdRules(new SNDEVDRules);
        ret = evdRules->parse(out[0],evdOut,reason);
        if(ret == false)
        {
            return -1;
        }
        INTLDCConfig * dcConfigWorker = dynamic_cast<INTLDCConfig*>(worker);
        if(dcConfigWorker)
        {
            connect(this,&INTLDCConfigHandler::updateGroupIdSignal,dcConfigWorker,&INTLDCConfig::updateGroupIdSlot,Qt::UniqueConnection);
        }
        qDebug()<<"groupId is"<<evdOut[0];
        emit updateGroupIdSignal(evdOut[0]);
        return 0;
      }
}
int INTLDCConfigHandler::handleTestResult(bool  ret,const QString & aux)
{
    emit runEnvCtxStatusSignal(false);
    if(DeviceContext::get()->isBaseProductType()==false)
    {
        emit widgetShowControlSignal(1,true,"请输入SN号和扫描二维码");
    }
    else
    {
        emit widgetShowControlSignal(1,false,"请输入SN号或者物料号");
    }

    return 0;
}
