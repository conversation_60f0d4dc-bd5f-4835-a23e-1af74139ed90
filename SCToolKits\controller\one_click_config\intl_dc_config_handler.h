#ifndef INTLDCCONFIGHANDLER_H
#define INTLDCCONFIGHANDLER_H

#include <QObject>
#include "interface_one_click_config_handler.h"
#include "interface_controller.h"

class INTLDCConfigHandler:public IOneClickConfigHandler
{
    Q_OBJECT
public:
    INTLDCConfigHandler(IController * c);
    ~INTLDCConfigHandler();
    bool buildBusiness(const QString &ctx) override;

    int handlePreStepInfo(const QString &data, QString & reason) override;

    bool handleSSHLinkResultSlot(bool status, QString ip, int port) override;
    int handleMaterialCodeInputSlot(const QString &code) override;
    int handleTestResult(bool, const QString &aux = "") override;

public slots:
    void handleProductConfirmInfo(bool isBaseProduct);
private:
    bool requestProductInfo(bool status);

signals:
    void sshLinkResultSignal(bool ret);
    void updateGroupIdSignal(const QString &);
};

#endif // INTLDCCONFIGHANDLER_H
