#include "one_click_config_controller.h"
#include "one_click_config/one_click_config_window.h"
#include "business/common_share/test_manager.h"
#include "mes/mes_manager.h"
#include "common_share/device_mes_worker.h"
#include "data/devices/device_context.h"
#include <QTimer>
#include <QDebug>
#include <QSharedPointer>
#include <rules/sn_rules.h>
#include "one_click_config/gb/gb_config.h"
#include "one_click_config/iso15118/iso15118_config.h"
#include "one_click_config/pileId/pileId_config.h"
#include "one_click_config/schneider_config/schneider_config.h"
#include "one_click_config/international_dc/interantional_dc.h"
#include "one_click_config/international_dc/authority_key_config.h"

OneClickConfigController::OneClickConfigController():tool(nullptr),
                                                     sshLinkStatus(false),
                                                     isRunConfig(false),
                                                     sn<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(false),
                                                     upload<PERSON>esR<PERSON>ult(false),
                                                     isF<PERSON>shed(false),
                                                     successNumber(0)
{
    TestManager * testManager = TestManager::get();
    connect(this,&OneClickConfigController::startConfigSignal,testManager, &TestManager::startTestSlot);
    connect(this,&OneClickConfigController::tipWindowSignal,testManager,&TestManager::tipCtxSignal);
    connect(this,&OneClickConfigController::closeTipWindowSignal,testManager,&TestManager::closeTipSignal);
    connect(testManager,&TestManager::finishedResult,this,&OneClickConfigController::configResultSlot);
    connect(testManager,&TestManager::materialCodeInputFinishedSignal,this,&OneClickConfigController::hanldeMaterialCodeSlot);

    LinkManager * linkManager = LinkManager::get();
//    connect(linkManager,&LinkManager::sshLinkResultSignal,this,&OneClickConfigController::updateSSHLinkResult);
    connect(linkManager,&LinkManager::sshChannelEnableSignal,this,&OneClickConfigController::updateSSHLinkResult);
    //注册处理函数
    setHandles(ISO15118_ENABLE_E,&OneClickConfigController::buildISO15118Business,&OneClickConfigController::startCommonPreProcess);
    setHandles(GB_ONE_KEY_CONFIGURE_E,&OneClickConfigController::buildGBConfigureBusiness,&OneClickConfigController::startGBConfigPreProcess);
    setHandles(BMW_CONFIG_E,&OneClickConfigController::buildBWMConfigureBusiness,&OneClickConfigController::startBWBConfigPreProcess);
    setHandles(VIN_FAST_CONFIGE_E,&OneClickConfigController::buildVinFastConfigureBusiness,&OneClickConfigController::startVinfastConfigPreProcess);
    setHandles(SCHNEIDER_CONFIG_E,&OneClickConfigController::buildSNDConfigureBusiness,&OneClickConfigController::starSNDConfigPreProcess);
    setHandles(SCHNEIDER_FANCTORY_CONFIG_E,&OneClickConfigController::buildSNDFactoryConfigureBusiness,&OneClickConfigController::startSNDFactoryConfigPreProcess);
    setHandles(INTERNAL_DC_CONFIGE_E,&OneClickConfigController::buildINTLConfigBusiness,&OneClickConfigController::startInternalDCPreProcess);
    setHandles(TEST_TOOL_BARCODE_CONFIG_E,&OneClickConfigController::buildBarcodeConfigBusiness,&OneClickConfigController::startBarcodeConfigPreProcess);
    setHandles(VIN_FAST_DC_CONFIG_E,&OneClickConfigController::buildVinFastDcConfigBussiness,&OneClickConfigController::startVinfastDCConfigPreProcess);
}
void OneClickConfigController::setHandles(ToolFunctionType type,handle build,startPrehandle preProcess)
{
    configBusinessHandle handles;
    handles.buildBusiness = build;
    handles.startPreProcess = preProcess;
    businessList[type] = handles;
}
OneClickConfigController::~OneClickConfigController()
{

}
void OneClickConfigController::handleWidgetShowSlot(int widgetId,bool st,const QString &  aux)
{
    emit showWidgetSignal(1,st,aux);
}
void OneClickConfigController::processTestRunEnvCtx(bool st,const QString &)
{
    setTestConditionStatus(st);
}
void OneClickConfigController::setTestConditionStatus(bool st)
{
    testConditionStauts = st;
    emit testConditionStausSignal(st);
}

void OneClickConfigController::showWindow(QWidget * parent)
{
    // TODO parent 变更的情况
    if(tool == nullptr)
    {
        tool = new OneClickConfigWindow(parent);
    }

    displayControlHandle();

    tool->show();
    return;
}
QWidget * OneClickConfigController::buildWindow(QWidget * parent)
{
    if(tool == nullptr)
    {
        OneClickConfigWindow * realTool = new OneClickConfigWindow(parent);
        connect(realTool,&OneClickConfigWindow::startConfigSignal,this,&OneClickConfigController::startConfigSlot);
        connect(realTool,&OneClickConfigWindow::updateMaterialNumber,this,&OneClickConfigController::updateMaterialNumberSlot);
        connect(realTool,&OneClickConfigWindow::uploadMesSignal,this,&OneClickConfigController::uploadMesSlot);
        connect(realTool,&OneClickConfigWindow::setpileCodeSignal,this,&OneClickConfigController::setPileCode);

        connect(this,&OneClickConfigController::configFinishedSignal,realTool,&OneClickConfigWindow::updateConfigResultShow);
        connect(this,&OneClickConfigController::mesUploadResult,realTool,&OneClickConfigWindow::uploadMesUploadResult);
        connect(this,&OneClickConfigController::finishCfgNumberSignal,realTool,&OneClickConfigWindow::updateFinishedNumber);
        connect(this,&OneClickConfigController::configTypeSignal, realTool, &OneClickConfigWindow::showFunWindow);
        connect(this,&OneClickConfigController::changConfigType,realTool,&OneClickConfigWindow::changeConfigType);
        connect(this,&OneClickConfigController::setSnCodeOnWindow,realTool,&OneClickConfigWindow::setSnCodeOnWindow);
        connect(this,&OneClickConfigController::clearWindowSiganl,realTool,&OneClickConfigWindow::clearWindowSlot);
        connect(this,&OneClickConfigController::generatePinCodeFinishedSingal,realTool,&OneClickConfigWindow::setPinCodeOnWindow);
        connect(this,&OneClickConfigController::generateWIFINameFinishedSingal,realTool,&OneClickConfigWindow::setWIFINameOnWindow);
        connect(this,&OneClickConfigController::generateWIFIPassWordFinishedSingal,realTool,&OneClickConfigWindow::setWIFIPassWordOnWindow);
        connect(this,&OneClickConfigController::updateBMWConfigDataToWindowSignal,realTool,&OneClickConfigWindow::updateBMWConfigDataToWindowSlot);

        connect(this,&OneClickConfigController::testConditionStausSignal,realTool,&OneClickConfigWindow::setEnableOperateSlot);
        connect(this,&OneClickConfigController::showWidgetSignal,realTool,&OneClickConfigWindow::setWidgetShow);

        MesManager *mesInstance = MesManager::get();
        connect(mesInstance,&MesManager::checkV2FinshedSignal,this,&OneClickConfigController::checkV2ResultSlot);
        connect(mesInstance,&MesManager::uploadFinishSignal,this,&OneClickConfigController::uploadResultSlot);

        //施耐德配置
        connect(mesInstance,&MesManager::schneiderYJPZInfoSignal,this,&OneClickConfigController::parseSchneiderYJPZInfo);
        connect(mesInstance,&MesManager::snInfoSignal,this,&OneClickConfigController::parseSchneiderConfigInfo);

        //ssh探测
        if(sshQueryWorker == nullptr)
        {
            sshQueryWorker = new SSHQueryWorker();
            connect(sshQueryWorker,&SSHQueryWorker::sshQueryResultSignal,this,&OneClickConfigController::processSSHQueryResult);
        }

        buildBusiness();
        tool = realTool;
    }

    return tool;
}
void OneClickConfigController::setWindow(QWidget * )
{

}
bool OneClickConfigController::buildISO15118Business()
{
    ISO15118Config * worker = new ISO15118Config();
    TestManager::get()->addTestWorker(worker);

    TestManager::get()->updateCrossMistakeProofing(ISO15118_ENABLE_E,false);
    TestManager::get()->updateSelectWorkerCtx(ISO15118_ENABLE_E);

    return true;
}
bool OneClickConfigController::buildGBConfigureBusiness()
{
    GBConfig * gbWorker = new GBConfig();
    TestManager::get()->addTestWorker(gbWorker);

    TestManager::get()->updateCrossMistakeProofing(GB_ONE_KEY_CONFIGURE_E,false);
    TestManager::get()->updateSelectWorkerCtx(GB_ONE_KEY_CONFIGURE_E);

    connect(gbWorker,&GBConfig::progressSignal,this,&OneClickConfigController::handleWorkerProcesInfoSlot);
    return true;
}
bool OneClickConfigController::buildBWMConfigureBusiness()
{
    BMWConfig * bmwConfig = new BMWConfig();
    TestManager::get()->addTestWorker(bmwConfig);

    TestManager::get()->updateCrossMistakeProofing(BMW_CONFIG_E,false);
    TestManager::get()->updateSelectWorkerCtx(BMW_CONFIG_E);

    LinkManager * linker = LinkManager::get();
    connect(linker,&LinkManager::ethernetLinkedSignal,bmwConfig,&BMWConfig::processEthernetSlot);

    TestManager * testManager = TestManager::get();
    connect(bmwConfig,&BMWConfig::finished, testManager,&TestManager::finishedResult);

    MesManager * mesManager = MesManager::get();
    connect(mesManager, &MesManager::checkV2FinshedSignal,bmwConfig, &BMWConfig::getMACSlot);
    connect(mesManager, &MesManager::macAddrSignal,bmwConfig,&BMWConfig::updateConfigDataSlot);

    connect(bmwConfig,&BMWConfig::updateConfigDataToWindow,this,&OneClickConfigController::updateBMWConfigDataToWindowSlot);
    return true;
}
bool OneClickConfigController::buildVinFastConfigureBusiness()
{
    PileIdConfig * pileIdConfig = new PileIdConfig();
    TestManager::get()->addTestWorker(pileIdConfig);
    TestManager::get()->updateSelectWorkerCtx(VIN_FAST_CONFIGE_E);

    TestManager * testManager = TestManager::get();
    connect(pileIdConfig,&PileIdConfig::tipCtxSignal,testManager,&TestManager::tipCtxSignal);
    return true;
}
bool OneClickConfigController::buildSNDBusiness(ToolFunctionType type)
{
    SchneiderConfig * schneiderConfig = new SchneiderConfig();
    TestManager::get()->addTestWorker(schneiderConfig);
    TestManager::get()->updateCrossMistakeProofing(type,false);
    TestManager::get()->updateSelectWorkerCtx(type);

    TestManager * testManager = TestManager::get();
    connect(schneiderConfig,&SchneiderConfig::tipCtxSignal,testManager,&TestManager::tipCtxSignal);
    connect(schneiderConfig,&SchneiderConfig::closeTipCtxSignal,testManager,&TestManager::closeTipSignal);
    connect(schneiderConfig,&SchneiderConfig::setFinalUplodaCtxSignal,
            testManager,&TestManager::updateMesUploadDataInfoCtxSlot);
    connect(schneiderConfig, &SchneiderConfig::finished, testManager, &TestManager::finishedResult);
    connect(schneiderConfig, &SchneiderConfig::generatePinCodeFinishedSingal,
           this,&OneClickConfigController::generatePinCodeFinishedSingal);
    connect(schneiderConfig, &SchneiderConfig::generateWIFINameFinishedSingal,
            this,&OneClickConfigController::generateWIFINameFinishedSingal);
    connect(schneiderConfig, &SchneiderConfig::generateWIFIPassWordFinishedSingal,
            this,&OneClickConfigController::generateWIFIPassWordFinishedSingal);
    return true;
}
bool OneClickConfigController::buildSNDConfigureBusiness()
{
    buildSNDBusiness(SCHNEIDER_CONFIG_E);
    return true;
}
bool OneClickConfigController::buildSNDFactoryConfigureBusiness()
{
    buildSNDBusiness(SCHNEIDER_FANCTORY_CONFIG_E);
    return true;
}
bool OneClickConfigController::buildINTLConfigBusiness()
{
    INTLDCConfig * internalDCConfig = new INTLDCConfig();
    TestManager::get()->addTestWorker(internalDCConfig);

    internalDCConfig->setTestTaskIndex(0);
    TestManager::get()->updateCrossMistakeProofing(INTERNAL_DC_CONFIGE_E,false);
    TestManager::get()->updateSelectWorkerCtx(INTERNAL_DC_CONFIGE_E);
    DeviceContext::get()->setSSHLoginPassword("!&n#gVjFAQG!Vztwv9x3C2w&A*bJLkxH");

    internalDCConfig->setMaintaincePattern(APPConfig::get()->isDependMes());
    connect(TestManager::get(),&TestManager::mesDependSignal,internalDCConfig,&INTLDCConfig::setMaintaincePattern);

    connect(internalDCConfig, &INTLDCConfig::humanProcessTipSignal, this, &OneClickConfigController::processHumanOperateSlot);
    connect(this, &OneClickConfigController::humanOperateResultSignal,internalDCConfig, &INTLDCConfig::receivHumanOperateResultSlot);

    businnessHandler = new INTLDCConfigHandler(this);
    businnessHandler->setBusinessWorker(internalDCConfig);
    setTestConditionStatus(false);
    connect(businnessHandler,&INTLDCConfigHandler::runEnvCtxStatusSignal,this,&OneClickConfigController::processTestRunEnvCtx);
    connect(businnessHandler,&INTLDCConfigHandler::widgetShowControlSignal,this,&OneClickConfigController::handleWidgetShowSlot);
    InterfaceData::get()->initEvdPrefixMap();

    int enable;
    QString path;
    QString other;
    SQLiteManager::get()->select(DC_CONFIG_FILEPATH_CONTROL,enable,path,other);
    InterfaceData::get()->setCustomFile(path);

    QThread * workerThread = new QThread();
    internalDCConfig->moveToThread(workerThread);
    workerThread->start();
    return true;
}

bool OneClickConfigController::buildBarcodeConfigBusiness()
{
    TestToolBarcodeConfig * testToolBarcodeConfig = new TestToolBarcodeConfig();
    TestManager::get()->setDeviceLinkConditionStatus(false);
    connect(testToolBarcodeConfig,&TestToolBarcodeConfig::updateTestStatusSignal,TestManager::get(),&TestManager::updateTestStatusSignal);
    TestManager::get()->addTestWorker(testToolBarcodeConfig);

    TestManager::get()->updateCrossMistakeProofing(TEST_TOOL_BARCODE_CONFIG_E,false);
    TestManager::get()->updateSelectWorkerCtx(TEST_TOOL_BARCODE_CONFIG_E);
    return true;
}

bool OneClickConfigController::buildVinFastDcConfigBussiness()
{
    AuthorityKeyConfig * internalDCConfig = new AuthorityKeyConfig();
    TestManager::get()->addTestWorker(internalDCConfig);

    TestManager::get()->updateCrossMistakeProofing(VIN_FAST_DC_CONFIG_E,false);
    TestManager::get()->updateSelectWorkerCtx(VIN_FAST_DC_CONFIG_E);

    QThread * workerThread = new QThread();
    internalDCConfig->moveToThread(workerThread);
    workerThread->start();
    return true;
}

bool OneClickConfigController::buildBusiness()
{
    ToolFunctionType funType = ToolKitsManager::get()->getCurrentToolFunction();
    auto iter = businessList.find(funType);
    if(iter != businessList.end())
    {
        if(iter.value().buildBusiness)
        {
            (this->*iter.value().buildBusiness)();
            return true;
        }
        else
        {
            qDebug()<< "not register process handle,check it";
        }
    }
    return false;
}
void OneClickConfigController::displayControlHandle()
{
    configType = 0;
    int displayPage = 0;
    ToolFunctionType funType = ToolKitsManager::get()->getCurrentToolFunction();
    if(funType == SCHNEIDER_CONFIG_E)
    {
        configType = 1;
        cfgData.schneiderConfigType = configType;
        displayPage = 1;
    }
    else if(funType == SCHNEIDER_FANCTORY_CONFIG_E)
    {
        configType = 2;
        cfgData.schneiderConfigType = configType;
        displayPage = 1;
    }
    else if(funType == GB_ONE_KEY_CONFIGURE_E)
    {
        configType = 3;
        displayPage = 1;
    }
    else if(funType == BMW_CONFIG_E)
    {
        configType = 4;
        displayPage = 2;
    }
    else if(funType == ISO15118_ENABLE_E)
    {
        displayPage = 3;
    }
    else if(funType == INTERNAL_DC_CONFIGE_E)
    {
        displayPage = 4;
    }
    else if(funType == TEST_TOOL_BARCODE_CONFIG_E)
    {
        displayPage = 5;
    }
    else if(funType == VIN_FAST_DC_CONFIG_E)
    {
        displayPage = 6;
    }

    emit changConfigType(configType,displayPage);
    return;
}
void OneClickConfigController::startConfigSlot(QString & data)
{
//    if(isRunConfig)
//    {
//        emit tipWindowSignal("正在配置，请配置完成后再点击！", MODAL, OK, TIPS);
//        return;
//    }

//    bool mesLogSta = MesManager::get()->isMesLogined();

//    if(!mesLogSta)
//    {
//        emit tipWindowSignal(tr("请先登录MES！"), MODAL, OK, TIPS);
//        return;
//    }
//    if(!snCheckResult)
//    {
//        emit tipWindowSignal("SN码错误！", MODAL, OK, TIPS);
//        return;
//    }


    ToolFunctionType funType = ToolKitsManager::get()->getCurrentToolFunction();
    qDebug() << "bind func" << data << funType;
    auto iter = businessList.find(funType);
    if(iter != businessList.end())
    {
        if(iter.value().startPreProcess)
        {
            bool ret = (this->*iter.value().startPreProcess)(data);
            if(ret)
            {
                QString toolName("oneClickConfigTool");
                emit startConfigSignal(toolName);
            }
        }
        else
        {
            qDebug()<< "not register process handle,check it";
        }
    }

//    isRunConfig = true;
}
bool OneClickConfigController::startCommonPreProcess(const QString &data)
{
    Q_UNUSED(data);
    return true;
}
bool OneClickConfigController::startGBConfigPreProcess(const QString &data)
{
    Q_UNUSED(data);
    return checkSSHStatus();
}
bool OneClickConfigController::startBWBConfigPreProcess(const QString &data)
{
    Q_UNUSED(data);
    if(!LinkManager::get()->getEthernetLinkedStatus())
    {
        emit tipWindowSignal("请先连接以太网！", MODAL, OK, TIPS);
        return false;
    }
    if(cfgData.snConfig.isEmpty() || cfgData.evseIdConfig.isEmpty() || cfgData.macConfig.isEmpty())
    {
        emit tipWindowSignal("配置获取信息失败，请正确输入SN号获取信息！", MODAL, OK, TIPS);
        return false;
    }
    return true;
}
bool OneClickConfigController::starSNDConfigPreProcess(const QString &data)
{
    Q_UNUSED(data);
    if(cfgData.customerSN.isEmpty())
    {
        emit tipWindowSignal("SN号为空，请重新扫描SN号", MODAL, OK, TIPS);
        return false;
    }

    if(cfgData.customerCpid.isEmpty())
    {
        emit tipWindowSignal("桩号为空，请重新扫描桩号", MODAL, OK, TIPS);
        return false;
    }

    QString sn;
    sn = DeviceContext::get()->getSN();
    cfgData.customerSN = sn;

    DeviceMesWorker mesClient;
    mesClient.getSNInfoSlot();

    InterfaceData::get()->setData(cfgData);

    return false;
}
bool OneClickConfigController::startSNDFactoryConfigPreProcess(const QString &data)
{
    QSharedPointer<SNRules> snRules;
    QStringList out;
    QString reason;
    snRules.reset(new SNDSNRules());
    bool ret = snRules->parse(data,out,reason);
    if(ret == false)
    {
        emit tipWindowSignal(reason, MODAL, OK, TIPS);
        return false;
    }
    cfgData.customerCR = out[0];
    cfgData.customerSN = out[1];
    cfgData.customerUuid = out[2];
    cfgData.customerCpid = out[3];
    DeviceContext::get()->setSN(out[1]);
    //需要现在TestManager里设置好sn号
    TestManager::get()->setSnCode(out[1]);
    DeviceMesWorker mesClient;
    mesClient.getSNInfoSlot();

    InterfaceData::get()->setData(cfgData);

    return false;
}
bool OneClickConfigController::startVinfastConfigPreProcess(const QString &data)
{
    QRegularExpression regExp("^[0-9]+$");
    if(!regExp.match(data).hasMatch() || data.length() < 3)
    {
        emit tipWindowSignal("输入的桩ID不符合规定！", MODAL, OK, WARNING);
        return false;
    }

    OneClickCfgData cfgData;
    int index = data.length() - 2;
    cfgData.pileId = data.mid(0, index);

    //提示当前桩ID
    QString tip = QString("请确定配置桩ID为：%1").arg(cfgData.pileId);
    bool ret = tipWindowSignal(tip,MODAL,OKCANCEL,TIPS);
    if(!ret)
    {
        return false;
    }
    qDebug() << "pile ID : " << cfgData.pileId;

    return checkSSHStatus();
}
bool OneClickConfigController::startInternalDCPreProcess(const QString & data)
{
    QString reason;
    if(businnessHandler->handlePreStepInfo(data,reason) != 0)
    {
        emit tipWindowSignal(reason, MODAL, OK, TIPS);
        return false;
    }
    return true;
}

bool OneClickConfigController::startBarcodeConfigPreProcess(const QString &data)
{
    Q_UNUSED(data);
    return true;
}

bool OneClickConfigController::startVinfastDCConfigPreProcess(const QString &data)
{
    Q_UNUSED(data);
    bool mesLogSta = MesManager::get()->isMesLogined();

    if(!sshLinkStatus)
    {
        emit tipWindowSignal(tr("请先连接SSH！"), MODAL, OK, TIPS);
        return false;
    }
    if(!mesLogSta)
    {
        emit tipWindowSignal(tr("请先登录MES！"), MODAL, OK, TIPS);
        return false;
    }
    if(APPConfig::get()->isDependMes())
    {
        if(!snCheckResult)
        {
            emit tipWindowSignal("SN码错误！", MODAL, OK, TIPS);
            return false;
        }
    }

    return true;
}

void OneClickConfigController::hanldeMaterialCodeSlot(const QString & code)
{
    if(businnessHandler)
    {
        businnessHandler->handleMaterialCodeInputSlot(code);
        qDebug()<< "update ssh link";
    }
}

void OneClickConfigController::updateMaterialNumberSlot(QString & number)
{
    DeviceContext::get()->setMaterialCode(number);
}
void OneClickConfigController::updateSSHLinkResult(bool status, QString ip, int port)
{
    sshLinkStatus = status;
    if(status)
    {

    }
    else
    {
        isRunConfig = false;
    }
    if(businnessHandler)
    {
        QTimer::singleShot(1000,[=]()
        {
            businnessHandler->handleSSHLinkResultSlot(status,ip,port);
        });
        qDebug()<< "update ssh link";
    }
}

void OneClickConfigController::configResultSlot(bool result)
{
    isRunConfig = false;
    emit configFinishedSignal(result);
    if(result)
    {

    }
    else
    {

    }
    if(businnessHandler)
    {
        businnessHandler->handleTestResult(result);
    }

}

void OneClickConfigController::checkV2ResultSlot(bool sta, const QString &ctx)
{
    if(isRunConfig)
    {
        return;
    }
    QString str = ctx;
    snCheckResult = sta;
    if(snCheckResult)
    {
        ToolFunctionType funType = ToolKitsManager::get()->getCurrentToolFunction();
        if(funType == TEST_TOOL_BARCODE_CONFIG_E)
        {
            QString data;
            startConfigSlot(data);
        }
    }
}

void OneClickConfigController::uploadResultSlot(bool sta, const QString &ctx)
{
    //test 正式记得修改
    isFinished = true;
    QString str = ctx;
    if(sta)
    {
        successNumber++;
        emit finishCfgNumberSignal(successNumber);
        snCheckResult = false;
        emit tipWindowSignal("上传成功", MODELESS, OK, TIPS);
        QTimer::singleShot(3000,[=]()
        {
            emit closeTipWindowSignal();
        });
    }
    else
    {
        if(str.isEmpty())
        {
            str = QString::fromUtf8("mes通信错误,请排查问题!");
        }
        str = "上传MES失败！原因："+str;
        emit tipWindowSignal(str, MODAL, OK, TIPS);
    }

    emit mesUploadResult(sta);
}

void OneClickConfigController::uploadMesSlot()
{
    TestManager::get()->uploadToMesSlot();
}

void OneClickConfigController::parseSchneiderYJPZInfo(bool ret, const QString & ctx, const QJsonObject & data)
{
    qDebug() << "schneiderYJPZ: " << ret << ctx << data;
    QString tipCtx = ctx;
    if(ret)
    {
        cfgData.passWord = data.value("PASSWORD").toString();
        cfgData.wifiPassWord = data.value("WIFIPWD").toString();
        cfgData.customerCpid = data.value("CHIPID").toString();
        cfgData.pinCode = data.value("RESET_PIN_CODE").toString();
        InterfaceData::get()->setData(cfgData);
    }
    else
    {

//        QString tip = QString("获取数据失败，原因：%1").arg(ctx);
//        emit tipWindowSignal(tip, MODAL, OK, TIPS);
	}

    if(checkSSHStatus())
    {
        QString toolName("oneClickConfigTool");
        emit startConfigSignal(toolName);
    }

}

void OneClickConfigController::parseSchneiderConfigInfo(bool ret, const QString & ctx, const QJsonObject & data)
{
    qDebug() << "schneider pile id from mes is: " << ret << ctx << data;
    QString tipCtx = ctx;
    if(ret)
    {
        cfgData.iniFilename = data.value("PROGRAM_NAME").toString();
        cfgData.pileId = data.value("ITEM_SN").toString();
        InterfaceData::get()->setData(cfgData);

        if(configType == 1)
        {
            if(checkSSHStatus())
            {
                QString toolName("oneClickConfigTool");
                emit startConfigSignal(toolName);
            }
        }
        else if (configType == 2)
        {
            DeviceMesWorker mesClient;
            mesClient.getSchneiderYJPZInfoSlot();
        }
    }
    else
    {
        QString tip = QString("获取数据失败，原因：%1").arg(ctx);
        emit tipWindowSignal(tip, MODAL, OK, TIPS);
    }
}

void OneClickConfigController::parseCheckSN(bool ret, const QString & ctx, const QJsonObject & data)
{
    if(ret & (configType == 2))
    {
        DeviceMesWorker mesClient;
        mesClient.getSchneiderYJPZInfoSlot();
    }
    else
    {
        emit tipWindowSignal(ctx, MODAL, OK, TIPS);
    }
}

void OneClickConfigController::setConfigType(int type)
{
    configType = type;

    cfgData.schneiderConfigType = configType;

    emit changConfigType(configType);
    qDebug() << "changConfigType: " <<configType;
}

void OneClickConfigController::setSNCode(QString &str)
{
    cfgData.customerSN = str;
}

void OneClickConfigController::setPileCode(QString &str)
{
    cfgData.customerCpid = str.mid(0, 8);
}

void OneClickConfigController::processSSHQueryResult(bool result, const QString &msg)
{
    if(result)
    {
        //连接上，开始测试
        QString toolName("oneClickConfigTool");
        emit startConfigSignal(toolName);
    }
    else
    {
        emit tipWindowSignal(msg, MODELESS, NO, TIPS);
    }
}

bool OneClickConfigController::checkSSHStatus()
{
    sshLinkStatus = LinkManager::get()->getSSHLinkedStatus("**************", "root");
//    qDebug() <<"sshLinkStatus: " <<sshLinkStatus;
    if(!sshLinkStatus)
    {
        //启动探测SSH
        sshQueryWorker->start();
    }
    else
    {
        QString toolName("oneClickConfigTool");
        emit startConfigSignal(toolName);
    }
    return true;
}
void OneClickConfigController::handleWorkerProcesInfoSlot(int progress,const QString & ctx,int displaycontrol,bool isNormal)
{
    //TEST use
    if(progress==100)
    {
        int tipAux = (MODELESS|OK|TIPS);
        MSG_MODE msgMode =(MSG_MODE) (tipAux &(int) MODELESS);
        MSG_TYPE msgType =(MSG_TYPE) (tipAux &(int) OK);
        MSG_TIP_TYPE msgTipType=(MSG_TIP_TYPE)(tipAux &(int) TIPS);
        emit tipWindowSignal(ctx,msgMode, msgType, msgTipType);

        QTimer::singleShot(1000,[=]()
        {
            emit closeTipWindowSignal();
        });
    }
    else
    {
        emit tipWindowSignal(ctx,(MSG_MODE)(displaycontrol&0x300), (MSG_TYPE)(displaycontrol&0x1), (MSG_TIP_TYPE)(displaycontrol&0xE8));
    }
}

void OneClickConfigController::updateBMWConfigDataToWindowSlot(OneClickCfgData &data)
{
    if(data.macConfig.isEmpty())
    {
        emit tipWindowSignal("获取MAC地址失败，请检查SN号的有效性", MODAL, OK, TIPS);
        return;
    }
    cfgData.snConfig = data.snConfig;
    cfgData.evseIdConfig = data.evseIdConfig;
    cfgData.macConfig = data.macConfig;
    emit updateBMWConfigDataToWindowSignal(data);
}
bool OneClickConfigController::processHumanOperateSlot(const QString & tips,int diplayControl,int timeout)
{
    if((diplayControl & 0x300) == 0x100)
    {
        QSharedPointer<TipController> tip = QSharedPointer<TipController>(new TipController(this->tool->parentWidget()));
        bool ret =  tip->showTipWindowWithTimerSlot(tips,timeout,MODAL,OK,TIPS);
        //不可以直接用ret返回给对应信号。在多线程下是无效的。需要通过信号传递
        emit humanOperateResultSignal(ret);
    }
    return false;
}
