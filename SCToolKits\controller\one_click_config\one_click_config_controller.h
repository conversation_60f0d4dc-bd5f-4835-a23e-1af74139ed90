#ifndef ONECLICKCONFIGCONTROLLER_H
#define ONECLICKCONFIGCONTROLLER_H

#include <QObject>
#include "interface_controller.h"
#include "data/interface_data/interface_data.h"
#include "gui/tip_window.h"
#include "business/workers/ssh_query_worker.h"
#include "business/one_click_config/bmw_config/bmw_config.h"
#include "business/one_click_config/test_tool_barcode/test_tool_barcode_config.h"
#include "intl_dc_config_handler.h"
class OneClickConfigController : public IController
{
    Q_OBJECT

public:
    void showWindow(QWidget * parent);
    void setWindow(QWidget * );
    QWidget * buildWindow(QWidget * parent=nullptr);
    bool buildBusiness();
public slots:
    void startConfigSlot(QString &);
    void configResultSlot(bool result);
    void updateSSHLinkResult(bool, QString, int);
    void checkV2ResultSlot(bool sta, const QString &ctx);
    void uploadResultSlot(bool sta, const QString &ctx);
    void uploadMesSlot();

    void parseSchneiderYJPZInfo(bool ret, const QString & ctx, const QJsonObject & data);
    void parseSchneiderConfigInfo(bool ret, const QString & ctx, const QJsonObject & data);
    void parseCheckSN(bool ret, const QString & ctx, const QJsonObject & data);

    void setConfigType(int );
    void setSNCode(QString &str);
    void setPileCode(QString &str);

    void processSSHQueryResult(bool, const QString &);

    bool checkSSHStatus();
    void handleWorkerProcesInfoSlot(int,const QString & ctx,int displaycontrol=0x200|0x8|0x1,bool isNormal=true);
    void updateBMWConfigDataToWindowSlot(OneClickCfgData &);

    bool processHumanOperateSlot(const QString & tip,int diplayControl=0x100|0x8|0x2,int timeout=0);

    void setTestConditionStatus(bool);
    void processTestRunEnvCtx(bool ,const QString & aux="");
    void handleWidgetShowSlot(int widgetId,bool st,const QString & aux="");

    void hanldeMaterialCodeSlot(const QString & code);
    void updateMaterialNumberSlot(QString &);
signals:
    void startConfigSignal(QString &);
    bool tipWindowSignal(QString tips, MSG_MODE mode, MSG_TYPE msgType, MSG_TIP_TYPE tipType);
    void closeTipWindowSignal();
    void configFinishedSignal(bool);
    void mesUploadResult(bool);
    void finishCfgNumberSignal(int);
    void setConfigInfo(QString &, QString &);
    void setYJPZConfigInfo(QString &, QString &, QString &);

    void configTypeSignal(int );
    void changConfigType(int ,int pageIndex=0,const QString &aux="");

    void setSnCodeOnWindow(QString &);
    void clearWindowSiganl();
    void generatePinCodeFinishedSingal(const QString &);
    void generateWIFINameFinishedSingal(const QString &);
    void generateWIFIPassWordFinishedSingal(const QString &);

    void humanOperateResultSignal(bool);
    void testConditionStausSignal(bool);

    void showWidgetSignal(int,bool,const QString & aux="");
    void configFileNameSingal(const QString &);
    void ethernetInfoSignal(const QString &,int port);

    void updateBMWConfigDataToWindowSignal(OneClickCfgData &);
public:
    OneClickConfigController();
    ~OneClickConfigController();
private:
    bool buildISO15118Business();
    bool buildGBConfigureBusiness();
    bool buildBWMConfigureBusiness();
    bool buildVinFastConfigureBusiness();

    bool buildSNDConfigureBusiness();
    bool buildSNDFactoryConfigureBusiness();
    bool buildSNDBusiness(ToolFunctionType type);

    bool buildINTLConfigBusiness();
    bool buildBarcodeConfigBusiness();
    bool buildVinFastDcConfigBussiness();
private:
    bool startCommonPreProcess(const QString &data="");
    bool startGBConfigPreProcess(const QString &data="");
    bool startBWBConfigPreProcess(const QString &data="");
    bool startVinfastConfigPreProcess(const QString &data="");
    bool starSNDConfigPreProcess(const QString &data="");
    bool startSNDFactoryConfigPreProcess(const QString &data="");
    bool startInternalDCPreProcess(const QString &data="");
    bool startBarcodeConfigPreProcess(const QString &data="");
    bool startVinfastDCConfigPreProcess(const QString &data="");


    void displayControlHandle();
private:
    QWidget * tool;
    IOneClickConfigHandler * businnessHandler {nullptr};

    bool sshLinkStatus;
    bool isRunConfig;
    bool snCheckResult;
    bool uploadMesResult;
    bool isFinished;
    int successNumber;
    bool testConditionStauts;

    int configType;

    OneClickCfgData cfgData;

    SSHQueryWorker *sshQueryWorker {nullptr};
    BMWConfig * bmwConfig {nullptr};

    typedef bool (OneClickConfigController::*handle)();
    typedef bool (OneClickConfigController::*startPrehandle)(const QString & data);
    class configBusinessHandle
    {
    public:
        handle buildBusiness;
        startPrehandle startPreProcess;
    };
    void setHandles(ToolFunctionType,handle build,startPrehandle preProcess);
    QMap<ToolFunctionType,configBusinessHandle>businessList;
};

#endif // ONECLICKCONFIGCONTROLLER_H
