#include "single_board_check_controller.h"
#include "toolkits_manager.h"

SingleBoardCheckController::SingleBoardCheckController():
    tool(nullptr), choiceWindow(nullptr), serialPortPtr(nullptr),testWorker(nullptr),serialLink<PERSON>ta(false),tipController(nullptr)
{
    TestManager * testManager = TestManager::get();
    connect(this,&SingleBoardCheckController::startTest,testManager, &TestManager::startTestSlot);
    connect(this,&SingleBoardCheckController::retestSignal,testManager, &TestManager::retestSignal);

    setHandles(AC_METER_CTRL_BOARD_E,&SingleBoardCheckController::buildAcMeterBoardBusiness);
    setHandles(DC_CONTROL_BOTTOM_BOARD_E,&SingleBoardCheckController::buildDcMainBoardBusiness);
    setHandles(DC_TOP_MAIN_BOARD_E,&SingleBoardCheckController::buildDcMainBoardBusiness);
    setHandles(DPAU_CONTROL_BOARD_E,&SingleBoardCheckController::buildDpauControlBoardBusiness);
    setHandles(LIQUID_COOL_BOARD_E,&SingleBoardCheckController::buildLiquidCoolerBusiness);
    setHandles(IONCHI_CCU_BOARD_E,&SingleBoardCheckController::buildDcMainBoardBusiness,&SingleBoardCheckController::startCcuPreProcess);
    setHandles(XGB_CCU_BOARD_E,&SingleBoardCheckController::buildDcMainBoardBusiness,&SingleBoardCheckController::startCcuPreProcess);
    setHandles(US_WAN_YUE_CORE_BOARD_E,&SingleBoardCheckController::buildAcMeterBoardBusiness);
    setHandles(SCHNEIDER_NEW_RESI_CTRL_BOARD_E,&SingleBoardCheckController::buildAcMeterBoardBusiness);
    setHandles(DC_PRE_CHARGE_BOARD_E,&SingleBoardCheckController::buildAcMeterBoardBusiness);
    setHandles(MCC_BOARD_CHECK_E, &SingleBoardCheckController::buildDcMainBoardBusiness);
}
void SingleBoardCheckController::setHandles(ToolFunctionType type,buildBusinesshandle build,startPrehandle preProcess)
{
    BoardCheckBusinessHandle handles;
    handles.buildBusiness = build;
    handles.startPreProcess = preProcess;
    businessList[type] = handles;
}

SingleBoardCheckController::~SingleBoardCheckController()
{

}

void SingleBoardCheckController::getControlWindowInfo(bool &setMax, bool &hideMes, bool &hideCom, bool &hideSn, bool &hideCan)
{

    if(ToolKitsManager::get()->getCurrentToolFunction() == LIQUID_COOL_BOARD_E)
    {
        setMax=false;
        hideMes=false;
        hideCom = false;
        hideSn=false;
        hideCan=false;
    }
    else if(ToolKitsManager::get()->getCurrentToolFunction() == DPAU_CONTROL_BOARD_E)
    {
        setMax=false;
        hideMes=false;
        hideCom = true;
        hideSn=false;
        hideCan=false;
    }
    else if(ToolKitsManager::get()->getCurrentToolFunction() == DC_PRE_CHARGE_BOARD_E)
    {
        setMax=false;
        hideMes=false;
        hideCom = false;
        hideSn=false;
        hideCan=true;
    }
    else if(ToolKitsManager::get()->getCurrentToolFunction() == MCC_BOARD_CHECK_E)
    {
        setMax=false;
        hideMes=false;
        hideCom = false;
        hideSn=false;
        hideCan=true;
    }
    else
    {
        setMax=false;
        hideMes=false;
        hideCom = true;
        hideSn=false;
        hideCan=true;
    }
}
void SingleBoardCheckController::showWindow(QWidget * parent)
{
    // TODO parent 变更的情况
    if(tool == nullptr)
    {
        tool = new SingleBoardCheckWindow(parent);
    }
    if(choiceWindow == nullptr)
    {
        choiceWindow = new TestCaseChoiceWindow(tool);
    }

    //根据不同的测试板更改对应界面
    funType = ToolKitsManager::get()->getCurrentToolFunction();
    BoardType boardType = AM62;
    if(funType == GD32_AC_DELAY_BOARD_E)
    {
        boardType = AC_RELAY_BOARD;
    }

    emit testBoardTypeSignal(boardType);

    tool->show();
}

bool SingleBoardCheckController::buildAcMeterBoardBusiness()
{
    //绑定通信的业务函数
    LinkManager * linkManager = LinkManager::get();
    connect(linkManager, &LinkManager::serialPortLinkResult, this, &SingleBoardCheckController::parseSerialLinkSta);

    bindWorkerFuns(ToolKitsManager::get()->getCurrentToolFunction(), false);

    return true;
}

bool SingleBoardCheckController::buildDcMainBoardBusiness()
{
    //绑定通信的业务函数
    LinkManager * linkManager = LinkManager::get();

    // 获取当前工具功能类型
    ToolFunctionType currentFunType = ToolKitsManager::get()->getCurrentToolFunction();

    if(currentFunType == MCC_BOARD_CHECK_E)
    {
        DeviceContext::get()->setDeviceLinkIP("**************");
        DeviceContext::get()->setDeviceLinkPort(12233);
    }

    connect(linkManager, &LinkManager::ethernetLinkedSignal, this, &SingleBoardCheckController::parseEthernetLinkSta);
    bindWorkerFuns(ToolKitsManager::get()->getCurrentToolFunction(), false);

    return true;
}

bool SingleBoardCheckController::buildLiquidCoolerBusiness()
{
    //1366冷源使用以太网,1489冷源使用串口
    QString materialCode;
    ToolKitsManager::get()->getEquipment(materialCode);

    //绑定通信的业务函数
    LinkManager * linkManager = LinkManager::get();
    if(materialCode.contains("1366"))
    {
        connect(linkManager, &LinkManager::ethernetLinkedSignal, this, &SingleBoardCheckController::parseEthernetLinkSta);
        connect(linkManager, &LinkManager::updateCanLinkedStatus, this, &SingleBoardCheckController::parseCanLinkSta);
    }
    else if (materialCode.contains("1489"))
    {
        connect(linkManager, &LinkManager::serialPortLinkResult, this, &SingleBoardCheckController::parseSerialLinkSta);
    }

    bindWorkerFuns(LIQUID_COOL_BOARD_E, false);

    return true;
}

bool SingleBoardCheckController::buildDpauControlBoardBusiness()
{
    bindWorkerFuns(DPAU_CONTROL_BOARD_E, false);
    LinkManager * linkManager = LinkManager::get();
    connect(linkManager, &LinkManager::updateCanLinkedStatus, this, &SingleBoardCheckController::parseCanLinkSta);
    return true;
}

bool SingleBoardCheckController::startCcuPreProcess(const QString &data)
{
    bool ethernetLinkStatus = LinkManager::get()->getEthernetLinkedStatus();
    if(!ethernetLinkStatus)
    {
        QTimer::singleShot(5000, [=]()
        {
            LinkManager::get()->connectHost(DeviceContext::get()->getDeviceLinkIP(),
                                            DeviceContext::get()->getDeviceLinkPort());
        });

        return false;
    }
    return true;
}

bool SingleBoardCheckController::buildBusiness()
{
    ToolFunctionType funType = ToolKitsManager::get()->getCurrentToolFunction();
    auto iter = businessList.find(funType);
    if(iter != businessList.end())
    {
        if(iter.value().buildBusiness)
        {
            (this->*iter.value().buildBusiness)();
            return true;
        }
        else
        {
            qDebug()<< "not register process handle,check it";
        }
    }
    return false;
}

QWidget * SingleBoardCheckController::buildWindow(QWidget * parent)
{
    if(tool == nullptr)
    {
        tool = new SingleBoardCheckWindow(parent);

        qRegisterMetaType<QByteArray>("QByteArray&");
        SingleBoardCheckWindow *realWind = dynamic_cast<SingleBoardCheckWindow*>(tool);
        connect(realWind, &SingleBoardCheckWindow::startTestSignal, this, &SingleBoardCheckController::startTestSlot);
        connect(realWind, &SingleBoardCheckWindow::retestSignal, this, &SingleBoardCheckController::retestSignal);
        connect(realWind, &SingleBoardCheckWindow::showChoiceWindowSignal, this, &SingleBoardCheckController::showTestCaseChoiceWindow);

        TestManager * testManager = TestManager::get();
        qRegisterMetaType<TipContext>("TipContext&");
        connect(testManager, &TestManager::sendTestObjectContext, realWind,&SingleBoardCheckWindow::updateDisplaySlot);
        connect(realWind, &SingleBoardCheckWindow::uploadMESByHumanSignal, testManager,&TestManager::uploadToMesSlot);

        MesManager *mesInstance = MesManager::get();
        connect(mesInstance, SIGNAL(checkV2FinshedSignal(bool,QString)),this, SLOT(checkV2ResultSlot(bool,QString)));
        connect(mesInstance, SIGNAL(uploadFinishSignal(bool,QString)),this, SLOT(uploadResultSlot(bool,QString)));

        connect(this, SIGNAL(snCodePraseReasult(bool)), tool, SLOT(updateSnCodeResultSlot(bool)));
        connect(this, SIGNAL(mesUploadResult(bool,QString)), tool, SLOT(updateMesUploadResultSlot(bool,QString)));
        connect(this, &SingleBoardCheckController::testBoardTypeSignal, realWind, &SingleBoardCheckWindow::updateTestWindowDisplay);

        connect(this, &SingleBoardCheckController::updateTestResultSignal, realWind, &SingleBoardCheckWindow::updateTestResultSlot);
    }
    if(choiceWindow == nullptr)
    {
        choiceWindow = new TestCaseChoiceWindow(tool);
        connect(this, &SingleBoardCheckController::testBoardTypeSignal, choiceWindow,&TestCaseChoiceWindow::updateChoiceType);
        connect(choiceWindow,&TestCaseChoiceWindow::choicePENCheckSiganl,this,&SingleBoardCheckController::choicePENCheckSlot);
    }
    if(tipController == nullptr)
    {
        tipController = new TipController(parent);
        connect(this,&SingleBoardCheckController::tipContexSignal, tipController, &TipController::showTipWindowSlot);
        connect(this,&SingleBoardCheckController::closeTipWindow, tipController, &TipController::closeTipWinowSlot);
    }

    buildBusiness();

    return tool;
}

void SingleBoardCheckController::startTestSlot()
{
    ToolFunctionType funType = ToolKitsManager::get()->getCurrentToolFunction();
    QString data;
    auto iter = businessList.find(funType);
    if(iter != businessList.end())
    {
        if(iter.value().startPreProcess)
        {
            bool ret = (this->*iter.value().startPreProcess)(data);
            if(ret)
            {
                QString toolName("singleBoardCheckTool");
                emit startTest(toolName);
            }
        }
        else if(iter.value().startPreProcess == nullptr)
        {
            QString toolName("singleBoardCheckTool");
            emit startTest(toolName);
        }
        else
        {
            qDebug()<< "not register process handle,check it";
        }
    }
}

void SingleBoardCheckController::parseSerialLinkSta(QString port, bool sta)
{
    qDebug() << "link serial port :" << port << sta;
    serialLinkSta = sta;
    if(sta)
    {
        LinkManager * linkManager = LinkManager::get();
        serialPortPtr = linkManager->getSerail(port);

        if(funType == AC_METER_CTRL_BOARD_E)
        {
            serialPortPtr->setSerialMsgType(MODBUS_RTU_MSG);
            qRegisterMetaType<QByteArray>("QByteArray&");
            disconnect(testWorker, &FlowTestWorker::sendDataSignal, serialPortPtr, &SerialWorker::sendDataSlot);
            disconnect(serialPortPtr, &SerialWorker::transformNetMsg, testWorker, &FlowTestWorker::processModbusMsg);
            connect(testWorker, &FlowTestWorker::sendDataSignal, serialPortPtr, &SerialWorker::sendDataSlot);
            connect(serialPortPtr, &SerialWorker::transformNetMsg, testWorker, &FlowTestWorker::processModbusMsg);
        }
        else
        {
            serialPortPtr->setSerialMsgType(Standard_Common_MSG);
            qRegisterMetaType<QByteArray>("QByteArray&");
            disconnect(testWorker, &FlowTestWorker::sendDataSignal, serialPortPtr, &SerialWorker::sendDataSlot);
            disconnect(serialPortPtr, &SerialWorker::transformNetMsg, testWorker, &TestWorker::recvDataSlot);

            connect(testWorker, &FlowTestWorker::sendDataSignal, serialPortPtr, &SerialWorker::sendDataSlot);
            connect(serialPortPtr, &SerialWorker::transformNetMsg, testWorker, &TestWorker::recvDataSlot);
        }
    }
}

void SingleBoardCheckController::parseEthernetLinkSta(bool sta, const QString &ip, int port)
{
    qDebug() << "link Ethernet " << "ip= " << ip << "port= " << port << sta;
    if(sta)
    {
        LinkManager * linkManager = LinkManager::get();

        // 确保获取当前IP的客户端而不是默认的
        EthernetClient * ethernetClient = linkManager->getEthernetClient(DeviceContext::get()->getDeviceLinkIP());
        // EthernetClient * ethernetClient = linkManager->getEthernetClient();

        if(funType == DC_CONTROL_BOTTOM_BOARD_E ||
           funType == DC_TOP_MAIN_BOARD_E ||
           funType == LIQUID_COOL_BOARD_E ||
           funType == IONCHI_CCU_BOARD_E ||
           funType == XGB_CCU_BOARD_E ||
           funType == MCC_BOARD_CHECK_E)
        {
            if(ethernetClient != nullptr)
            {
                disconnect(testWorker, &FlowTestWorker::sendDataSignal, ethernetClient,&EthernetClient::sendBytesSlot);
                disconnect(ethernetClient,&EthernetClient::dataComInSigal,testWorker,&TestWorker::recvDataSlot);

                connect(testWorker,&FlowTestWorker::sendDataSignal,ethernetClient,&EthernetClient::sendBytesSlot);
                connect(ethernetClient,&EthernetClient::dataComInSigal,testWorker,&TestWorker::recvDataSlot);
                startTestSlot();
            }
        }
    }
}

void SingleBoardCheckController::parseCanLinkSta(bool sta)
{
    if(sta)
    {
        LinkManager * linkManager = LinkManager::get();
        CANWorker * canWorker = linkManager->getCan();

        if(canWorker != nullptr)
        {
            disconnect(testWorker,&FlowTestWorker::sendCanMsgSignal, canWorker, &CANWorker::sendCanMsgSlot);
            disconnect(canWorker, &CANWorker::recvReplySignal, testWorker,&FlowTestWorker::processCANMsg);

            connect(testWorker,&FlowTestWorker::sendCanMsgSignal, canWorker, &CANWorker::sendCanMsgSlot);
            connect(canWorker, &CANWorker::recvReplySignal, testWorker,&FlowTestWorker::processCANMsg);
        }
    }
}

void SingleBoardCheckController::checkV2ResultSlot(bool sta, const QString &ctx)
{
    QString str = ctx;
    emit snCodePraseReasult(sta);
}

void SingleBoardCheckController::uploadResultSlot(bool sta, const QString &ctx)
{
    QString str = ctx;
    if(sta)
    {
        emit tipContexSignal("测试成功，上传MES完成！", MODAL, OK, TIPS);
        emit mesUploadResult(true, "上传成功");
    }
    else
    {
        if(str.isEmpty())
        {
            str = QString::fromUtf8("mes通信错误,请排查问题!");
        }
        emit mesUploadResult(false, str);
    }
}

void SingleBoardCheckController::showTestCaseChoiceWindow()
{
    choiceWindow->exec();
}

void SingleBoardCheckController::choicePENCheckSlot(bool checked)
{
    CircuitBoardData data;
    data.PENRelayCheck = checked;
    InterfaceData::get()->setData(data);
}

void SingleBoardCheckController::recvAdditionalInfoSlot(QMap<int, QString> additionalTestInfo)
{
    QString testResult;

    // 遍历映射表，将字符串换行显示
    QMapIterator<int, QString> iterator(additionalTestInfo);
    while (iterator.hasNext())
    {
        iterator.next();
        testResult += QString("任务%1: %2, 失败\n").arg(iterator.key() + 1).arg(iterator.value());
    }

    emit tipContexSignal(testResult, MODAL, OK, TIPS);
    emit updateTestResultSignal(additionalTestInfo);
}

void SingleBoardCheckController::bindWorkerFuns(ToolFunctionType type, bool sta)
{
    if(testWorker == nullptr)
    {
        TestManager* testManager = TestManager::get();
        testWorker = new FlowTestWorker("singleBoardCheckTool");
        testManager->addTestWorker(testWorker);

        //test 开始与完成。
        connect(this, &SingleBoardCheckController::retestSignal, testWorker, &FlowTestWorker::resetTestObject);
        connect(testWorker,&FlowTestWorker::finishedTestSiganl, testManager, &TestManager::recvToolTestResultSlot);
        connect(testWorker,&FlowTestWorker::updateTestStatusSignal, testManager, &TestManager::updateTestStatusSignal);

        //显示控制：
        connect(testWorker,&FlowTestWorker::sendTipContext, testManager, &TestManager::sendTestObjectContext);

        // 附加信息显示
        connect(testWorker,&FlowTestWorker::sendAdditionalInfo, this, &SingleBoardCheckController::recvAdditionalInfoSlot);

        //testflowerwork的提示关联
        connect(testWorker, &FlowTestWorker::finishedTestSiganl, testManager,&TestManager::closeTipSignal);
        connect(testWorker, &FlowTestWorker::emitTipCtxSignal, testManager, &TestManager::tipCtxSignal);

        testManager->updateCrossMistakeProofing(type,sta);
        testManager->updateSelectWorkerCtx(type);
        testManager->updateSelectSerialPortCtx(type);
    }
}
