#ifndef SINGLEBOARDCHECKCONTROLLER_H
#define SINGLEBOARDCHECKCONTROLLER_H

#include <QObject>
#include "interface_controller.h"
#include "single_board_check/single_board_check_window.h"
#include "single_board_check/test_case_choice_window.h"
#include "communication/link_manager.h"
#include "communication/serial/serial_worker.h"
#include "common_share/test_manager.h"
#include "gui/tip_window.h"
#include <QThread>

class SingleBoardCheckController : public IController
{
    Q_OBJECT
public:
    SingleBoardCheckController();
    ~SingleBoardCheckController();

public:
    void showWindow(QWidget * parent)override;
    QWidget * buildWindow(QWidget * parent=nullptr)override;
    bool buildBusiness()override;
    virtual void getControlWindowInfo(bool &setMax, bool &hideMes, bool &hideCom, bool &hideSn, bool &hideCan) override;

private:
    void bindWorkerFuns(ToolFunctionType type, bool sta);

    bool buildAcMeterBoardBusiness();
    bool buildDcMainBoardBusiness();
    bool buildLiquidCoolerBusiness();
    bool buildDpauControlBoardBusiness();
private:
    bool startCcuPreProcess(const QString &data="");
public slots:
    void startTestSlot();

    void checkV2ResultSlot(bool sta, const QString &ctx);
    void uploadResultSlot(bool sta, const QString &ctx);
    void showTestCaseChoiceWindow();

    void choicePENCheckSlot(bool);
    void parseSerialLinkSta(QString port, bool sta);
    void parseEthernetLinkSta(bool sta, const QString &ip, int port);
    void parseCanLinkSta(bool sta);

    void recvAdditionalInfoSlot(QMap<int, QString> additionalTestInfo);       // 接收flow传输的测试结果并进行处理
signals:
    void startTest(QString &);
    void sendTipWindowCtxSignal(QString tips, MSG_MODE mode, MSG_TYPE ntype, MSG_TIP_TYPE ntiptype);
    void snCodePraseReasult(bool);
    void mesUploadResult(bool,QString);
    void retestSignal();
    void testBoardTypeSignal(BoardType type);
    bool tipContexSignal(QString tips, MSG_MODE mode, MSG_TYPE msgType, MSG_TIP_TYPE tipType);
    void closeTipWindow();
    void updateTestResultSignal(QMap<int, QString>& additionalTestInfo);     // 界面更新具体测试结果
private:
    QWidget * tool;
    TestCaseChoiceWindow * choiceWindow;

    QThread workThreadPtr;
    SerialWorker * serialPortPtr;
    FlowTestWorker *testWorker;
    ToolFunctionType funType;
    bool serialLinkSta;
    TipController * tipController;

    typedef bool (SingleBoardCheckController::*buildBusinesshandle)();
    typedef bool (SingleBoardCheckController::*startPrehandle)(const QString & data);
    class BoardCheckBusinessHandle
    {
    public:
        buildBusinesshandle buildBusiness;
        startPrehandle startPreProcess;
    };
    void setHandles(ToolFunctionType,buildBusinesshandle build,startPrehandle preProcess=nullptr);
    QMap<ToolFunctionType,BoardCheckBusinessHandle>businessList;
};

#endif // SINGLEBOARDCHECKCONTROLLER_H
