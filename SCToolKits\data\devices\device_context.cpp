#include<QString>
#include<QStringList>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include "device_context.h"
class SPecialDeviceInfo
{
  public:
    QString SN;
    QString CPID;
    QString UUID;
    QString materialCode;
};
class PrintDeviceInfo
{
public:
    QStringList info;
};
class DeviceNetworkLinkInfo
{
public:
    DeviceNetworkLinkInfo():IP("**************"),user("root"),sshPassword("d1H3.s2C4"),sshLoginModel(0),sshPort(22),customPort(12233)
    {

    }
    QString IP;
    QString user;
    QString sshPassword;
    int sshLoginModel;
    int sshPort;
    int customPort;
    QString sshPrivateKey;
};
class DeviceSerialPortLinkInfo
{
public:
    DeviceSerialPortLinkInfo(int defaultBaud=9600,int defaultstopBit=8,int defaultDatabit=1,int defaultParity = 0,int defaultFlowctrl=0):
        baud(defaultBaud),
        stopBit(defaultstopBit),
        dataBit(defaultstopBit),
        parity(defaultParity),
        flowctrl(defaultFlowctrl)
    {

    }
public:
    QString serialPort;
    int baud;
    int stopBit;
    int dataBit;
    int parity;
    int flowctrl;

};

class DeviceWebInfo
{
public:
    QString account="Admin";
    QString password="1234";
    int webType=2;//v1=1,v2=2;
    QString defaultAccount="Admin";
    QString defaultPassword="1234";
};
//TODO
class FirmwareInfo
{
public:
    QString mcuFirmware;//
    QString coreBoardFirmware;//
};
//TODO:研发部门信息research and development department :RD
class RDDepartmentInfo
{
public:
    QString department;

};

class FourthGInfo
{
public:
    QString imei;
    QString macId;
    QString rsn;
};

class DeviceContextImpl
{
public:
    DeviceContextImpl():specialDeviceInfo(new SPecialDeviceInfo()){};
    void createSpecialDeviceInfo() {specialDeviceInfo = new SPecialDeviceInfo();}
    QString sn;
    QString materiaCode;
    QString mesLogicDeviceId;
    QString mesLoginAccount;
    SPecialDeviceInfo * specialDeviceInfo;
    PrintDeviceInfo  *printInfo;

    QString passWord;
    QString wifiPassWord;

    QString CPID;

    DeviceNetworkLinkInfo networkLinkInfo;
    DeviceSerialPortLinkInfo serialPortLinkInfo;
    DeviceWebInfo webInfo;

    QString pinCode;

    FirmwareInfo firmwareInfo;

    FourthGInfo fourthGInfo;

    //标品和定制的信息
    bool isBaseProductType = true;
    RDDepartmentInfo departmentInfo;
};

DeviceContext * DeviceContext::devices = nullptr;
DeviceContext::DeviceContext():impl(new DeviceContextImpl())
{

}

void DeviceContext::setSN(const QString & sn)
{
    impl->sn = sn;
}

QString DeviceContext::getSN()
{
    return impl->sn;
}
void DeviceContext::setMaterialCode(const QString & materiaCode)
{
    impl->materiaCode = materiaCode;
}
QString DeviceContext::getMaterialCode() const
{
    return impl->materiaCode;
}

void DeviceContext::setMesLogicDeviceId(const QString &id)
{
    impl->mesLogicDeviceId = id;
}

QString DeviceContext::getMesLogicDeviceId()
{
    return impl->mesLogicDeviceId;
}

void DeviceContext::setMesLoginAccount(const QString &account)
{
    impl->mesLoginAccount = account;
}

QString DeviceContext::getMesLoginAccount()
{
    return impl->mesLoginAccount;
}

bool DeviceContext::hasSpecialDeviceInfo()
{
    return impl->specialDeviceInfo == nullptr ? false:true;
}

void DeviceContext::setPrintStr(const QString & info)
{
    if(impl->printInfo==nullptr)
    {
        impl->printInfo = new PrintDeviceInfo();
    }

    impl->printInfo->info<< info;
    return;
}
QString DeviceContext::getDeviceLinkIP()const
{
    return impl->networkLinkInfo.IP;
}
void DeviceContext::setDeviceLinkIP(const QString &ip)
{
    impl->networkLinkInfo.IP = ip;
}
int DeviceContext::getDeviceLinkPort()const
{
    return impl->networkLinkInfo.customPort;
}
void DeviceContext::setDeviceLinkPort(int port)
{
    impl->networkLinkInfo.customPort = port;
}
QString DeviceContext::getSSHLoginPassword()const
{
    return impl->networkLinkInfo.sshPassword;
}

void DeviceContext::setSSHLoginPassword(const QString &pwd)
{
    impl->networkLinkInfo.sshPassword = pwd;
}
QString DeviceContext::getSSHLoginUser()const
{
    return impl->networkLinkInfo.user;
}
void DeviceContext::setSSHLoginUser(const QString & user)
{
     impl->networkLinkInfo.user = user;
}

QString DeviceContext::getSSHPrivateKey() const
{
    return impl->networkLinkInfo.sshPrivateKey;
}

void DeviceContext::setSSHPrivateKey(const QString &keyFilePath)
{
    impl->networkLinkInfo.sshPrivateKey = keyFilePath;
}
int DeviceContext::getSSHLoginModel()const
{
    return impl->networkLinkInfo.sshLoginModel;
}
void DeviceContext::setSSHLoginModel(int model)
{
    impl->networkLinkInfo.sshLoginModel = model;
}

void DeviceContext::setPinCode(const QString & code)
{
    impl->pinCode = code;
}

QString DeviceContext::getPinCode()
{
    return impl->pinCode;
}
void DeviceContext::setCPID(const QString & cPId)
{
    impl->CPID =cPId;
    return;
}
QString DeviceContext::getCPID()const
{
    return impl->CPID;
}

QString DeviceContext::getMCUFirmware(const QString & mcu)
{
    return impl->firmwareInfo.mcuFirmware;
}
void DeviceContext::setMCUFirmware(const QString & name ,const QString & mcu)
{
     impl->firmwareInfo.mcuFirmware = name;
}
void DeviceContext::setCoreBoardFirmware(const QString & f)
{
    impl->firmwareInfo.coreBoardFirmware = f;
    return;
}
QString DeviceContext::getCoreBoardFirmware()
{
    return impl->firmwareInfo.coreBoardFirmware;
}
void DeviceContext::setBaseProductType(bool isBase)
{
    impl->isBaseProductType = isBase;
}
bool DeviceContext::isBaseProductType()const
{
    return impl->isBaseProductType;
}
void DeviceContext::setCustomSN(const QString & sn)
{
    impl->specialDeviceInfo->SN = sn;
}
QString DeviceContext::getCustomSN()const
{
    return impl->specialDeviceInfo->SN;
}
void DeviceContext::setCustomMaterialCode(const QString & code)
{
    impl->specialDeviceInfo->materialCode = code;
}
QString DeviceContext::getCustomMaterialCode()const
{
    return impl->specialDeviceInfo->materialCode;
}

void DeviceContext::setWebLoginInfo(const QString & accout,const QString & pwd,int webType)
{
    impl->webInfo.account = accout;
    impl->webInfo.password = pwd;
    impl->webInfo.webType =webType;
}
void DeviceContext::getWebLoginInf(QString & accout,QString & pwd)const
{
    accout = impl->webInfo.account;
    pwd = impl->webInfo.password;
}
void DeviceContext::setWebType(int type)
{
    impl->webInfo.webType = type;
}
int DeviceContext::getWebyType()const
{
    return impl->webInfo.webType;
}
void DeviceContext::setDepartment(const QString & department)
{
    impl->departmentInfo.department = department;
}
QString DeviceContext::getDepartment()const
{
    return impl->departmentInfo.department;
}

void DeviceContext::setIMEIInfo(const QString &imei)
{
    impl->fourthGInfo.imei = imei;
}

QString DeviceContext::getIMEIInfo()
{
    return impl->fourthGInfo.imei;
}

void DeviceContext::setMACIdInfo(const QString &macId)
{
    impl->fourthGInfo.macId = macId;
}

QString DeviceContext::getMACIdInfo()
{
    return impl->fourthGInfo.macId;
}

void DeviceContext::setRSNInfo(const QString &rsn)
{
    impl->fourthGInfo.rsn = rsn;
}

QString DeviceContext::getRSNInfo()
{
    return impl->fourthGInfo.rsn;
}

void DeviceContext::setSerialPortInfo(const QString & port,int baud,int dataBit,int stopBit,int parity,int flowctr)
{
    impl->serialPortLinkInfo.serialPort = port;
    impl->serialPortLinkInfo.baud = baud;
    impl->serialPortLinkInfo.dataBit = dataBit;
    impl->serialPortLinkInfo.stopBit = stopBit;
    impl->serialPortLinkInfo.parity = parity;
    impl->serialPortLinkInfo.flowctrl = flowctr;

}
void  DeviceContext::getSerialPortInfo(QString & port,int & baud,int & dataBit,int & stopBit,int & parity,int &flowctrl)
{
    port = impl->serialPortLinkInfo.serialPort;
    baud = impl->serialPortLinkInfo.baud ;
    dataBit = impl->serialPortLinkInfo.dataBit;
    stopBit = impl->serialPortLinkInfo.stopBit;
    parity = impl->serialPortLinkInfo.parity;
    flowctrl = impl->serialPortLinkInfo.flowctrl;

}
QString DeviceContext::getSerialPortName()const
{
    return impl->serialPortLinkInfo.serialPort;
}
