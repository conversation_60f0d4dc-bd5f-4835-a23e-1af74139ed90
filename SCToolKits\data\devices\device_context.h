#ifndef DEVICECONTEXT_H
#define DEVICECONTEXT_H
#include<QString>


class DeviceContextImpl;

class DeviceContext
{
public:
    static DeviceContext *get()
    {
        //mutex?no ?
        if(devices == nullptr)
        {
            devices = new DeviceContext();
        }
        return devices;
    }
public:
    void setSN(const QString & sn);
    QString getSN();
    void setMaterialCode(const QString & materiaCode);
    QString getMaterialCode() const;

    void setMesLogicDeviceId(const QString &id);
    QString getMesLogicDeviceId();
    void setMesLoginAccount(const QString &account);
    QString getMesLoginAccount();
    bool hasSpecialDeviceInfo();
    void setPrintStr(const QString & info);
    QString getDeviceLinkIP()const;
    void setDeviceLinkIP(const QString &ip);
    int getDeviceLinkPort()const;
    void setDeviceLinkPort(int port);
    QString getSSHLoginPassword()const;
    void setSSHLoginPassword(const QString &pwd);
    QString getSSHLoginUser()const;
    void setSSHLoginUser(const QString & user);
    QString getSSHPrivateKey()const;
    void setSSHPrivateKey(const QString & keyFilePath);
    int getSSHLoginModel()const;
    void setSSHLoginModel(int model);

    void setSerialPortInfo(const QString & port,int baud=9600,int dataBit=8,int stopBit=1,int parity=0,int flowctr=0);
    void getSerialPortInfo(QString & port,int & baud,int & dataBit,int & stopBit,int & parity,int &flowctrl);
    QString getSerialPortName()const;

    void setPinCode(const QString &);
    QString getPinCode();
    QString getCPID()const;
    void setCPID(const QString &);

    QString getMCUFirmware(const QString & mcu="");
    void setMCUFirmware(const QString & name ,const QString & mcu="");

    QString getCoreBoardFirmware();
    void setCoreBoardFirmware(const QString & f);

    bool isBaseProductType()const;
    void setBaseProductType(bool );

    void setCustomSN(const QString & sn);
    QString getCustomSN()const;
    void setCustomMaterialCode(const QString & code);
    QString getCustomMaterialCode()const;

    void setWebLoginInfo(const QString & accout,const QString & pwd,int webType=1);
    void getWebLoginInf(QString & accout,QString & pwd)const;
    void setWebType(int type);
    int getWebyType()const;

    void setDepartment(const QString &);
    QString getDepartment()const;

    void setIMEIInfo(const QString &);
    QString getIMEIInfo();
    void setMACIdInfo(const QString &);
    QString getMACIdInfo();
    void setRSNInfo(const QString &);
    QString getRSNInfo();
private:
    DeviceContext();
private:
    DeviceContextImpl * impl;
private:
    static DeviceContext * devices;
};

#endif // DEVICECONTEXT_H
