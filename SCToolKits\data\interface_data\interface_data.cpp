﻿#include "interface_data.h"

InterfaceData * InterfaceData::instance=nullptr;
InterfaceData::InterfaceData()
{
    circuitBoardData.PENRelayCheck = false;
}

InterfaceData::~InterfaceData()
{

}

bool InterfaceData::setData(NetworkCfgData & data)
{
    networkCfgData = data;
    return true;
}

bool InterfaceData::setData(OneClickCfgData & data)
{
    oneClickCfgData = data;
    return true;
}

bool InterfaceData::setData(WriteFirmwareData & data)
{
    writeFirmwareData = data;
    return true;
}

bool InterfaceData::setData(CircuitBoardData &data)
{
    circuitBoardData = data;
    return true;
}

bool InterfaceData::setData(WifiConfData &data)
{
    wifiConfData = data;
    return true;
}

bool InterfaceData::setData(WebConfigData &data)
{
    webConfigData = data;
    return true;
}

bool InterfaceData::setData(GuiAgeingConfigData &data)
{
    if(ageingData == nullptr)
    {
        ageingData = new GuiAgeingConfigData;
    }
    *ageingData = data;
    return true;
}
void InterfaceData::getData(WriteFirmwareData & data)
{
    data = writeFirmwareData;
}

void InterfaceData::getData(NetworkCfgData & data)
{
    data = networkCfgData;
}

void InterfaceData::getData(OneClickCfgData & data)
{
    data = oneClickCfgData;
}

void InterfaceData::getData(CircuitBoardData &data)
{
    data = circuitBoardData;
}

void InterfaceData::getData(WifiConfData &data)
{
    data = wifiConfData;
}

void InterfaceData::getData(WebConfigData &data)
{
    data = webConfigData;
}
bool InterfaceData::getData(GuiAgeingConfigData &data)
{
    if(ageingData)
    {
        data = *ageingData;
        return true;
    }
    return false;
}
QString InterfaceData::getCustomFile()
{
    return customFile;
}

void InterfaceData::initEvdPrefixMap()
{
    evdPrefixMap["EVD1S60"] = "6750";
    evdPrefixMap["EVD1S120"] = "4709";
    evdPrefixMap["EVD1S150"] = "4709";
    evdPrefixMap["EVD1S180"] = "4709";
    evdPrefixMap["EVD2S120"] = "7587";
    evdPrefixMap["EVD2S150"] = "7587";
    evdPrefixMap["EVD2S180"] = "7587";
    evdPrefixMap["EVD2S240"] = "7587";
    evdPrefixMap["EVD2S320"] = "7587";
    evdPrefixMap["EVD2S60"] = "7587";
    evdPrefixMap["EVD1S720"] = "8190";
    evdPrefixMap["EVD1S480"] = "8190";
    evdPrefixMap["EVD1D720"] = "8190";
}

QMap<QString, QString> InterfaceData::getEvdPrefixMap()
{
    return evdPrefixMap;
}
void InterfaceData::setCustomFile(const QString & file)
{
    customFile = file;
}
