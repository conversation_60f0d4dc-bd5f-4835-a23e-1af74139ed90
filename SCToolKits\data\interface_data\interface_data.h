﻿#ifndef INTERFACEDATA_H
#define INTERFACEDATA_H

#include <QObject>
#include "gui_data.h"
#include <QMap>
#define LP_ADDRESS_LOWER_LIMIT 0
#define LP_ADDRESS_UPPER_LIMIT 0xF


typedef enum
{
    A7 = 0,
    Router
}ConfigTarget;

typedef struct
{
    ConfigTarget configTarget;

    // 4G
    QString apn;
    QString username;
    QString passwd;
    QString pin;

    // DNS
    QString dns1;
    QString dns2;

    // OCPP
    QString cpid;
    QString url;
    QString path;
    QString port;
    QString ssl_on;
    QString authorrization_key;

    //language
    QString language;
    QString modelCode;

    //web
    QString newUser;
    QString pwd;
}NetworkCfgData;

//wifi测试数据
typedef struct
{
    QString ssid;
    QString model;
    QString pwd;
}WifiConfData;

typedef struct
{
    int schneiderConfigType;
    //施耐德生产配置
    QString customerUuid;

    //施耐德出厂配置
    QString customerCpid;
    QString customerSN;
    QString customerCR;

    QString passWord;
    QString wifiPassWord;
    QString iniFilename;
    QString pileId;
    QString pinCode;
    QString materialCode;

    //BMW配置
    QString snConfig;
    QString evseIdConfig;
    QString macConfig;
}OneClickCfgData;

typedef struct
{
    // OTA
    QString path;
    int address;

    // JLink
    QString exePath;
    QString scriptPath;
    QString device;
    QString addrStr;
    QString firmwareName;
    QString firmwareSrcPath;
    QString firmwareDstPath;
    QString firewareType;

    // new
    QString projectFileName;
    QString projectFilePath;
    QString dataFileSAddr;
}WriteFirmwareData;

typedef struct
{
    //测试功能 Arc继电器板
    bool PENRelayCheck;
}CircuitBoardData;

//web config
typedef enum
{
    UNKNOW = 0,
    CHANGE_INIT_PWD,   ///<第一次登录修改密码
    CHAGNE_PWD,        ///<修改密码
    RESET_INIT_PWD,    ///<重置密码
    ADD_NEW_USER,      ///<新增用户
}WebConfigType;

typedef struct
{
    WebConfigType type;

    //账号密码修改
    QString oldAccount;
    QString oldPwd;
    QString newAccount;
    QString newPwd;
    //web
    QString newUser;
    QString pwd;
}WebConfigData;

class InterfaceData
{
public:
    static InterfaceData *get()
    {
        if(instance==nullptr)
        {
            instance = new InterfaceData;
        }
        return instance;
    }

public:
    bool setData(NetworkCfgData &);
    bool setData(OneClickCfgData &);
    bool setData(WriteFirmwareData &);
    bool setData(CircuitBoardData &);
    bool setData(WifiConfData &);
    bool setData(WebConfigData &);
    bool setData(GuiAgeingConfigData &);

    void getData(NetworkCfgData &);
    void getData(OneClickCfgData &);
    void getData(WriteFirmwareData &);
    void getData(CircuitBoardData &);
    void getData(WifiConfData &);
    void getData(WebConfigData &);
    bool getData(GuiAgeingConfigData &);

    void setCustomFile(const QString & file);
    QString getCustomFile();

    void initEvdPrefixMap();
    QMap<QString,QString> getEvdPrefixMap();

private:
    NetworkCfgData networkCfgData;
    OneClickCfgData oneClickCfgData;
    WriteFirmwareData writeFirmwareData;
    CircuitBoardData circuitBoardData;
    WifiConfData wifiConfData;
    WebConfigData webConfigData;

    GuiAgeingConfigData * ageingData = nullptr;
    QString customFile;//自定义文件的全路径。
    QMap<QString,QString> evdPrefixMap;

private:
    InterfaceData();
    ~InterfaceData();
private:
    static InterfaceData * instance;
};

#endif // INTERFACEDATA_H
