﻿#include "protocol_maintainer.h"
#include "crc.h"
ProtocolMaintainer * ProtocolMaintainer::instance=nullptr;
ProtocolMaintainer::ProtocolMaintainer():msgId(0)
{
    cmd[PIN_GPIO_SET_DIRECTON] = GPIO_DIRECTION_CMD;
    cmd[PIN_GPIP_SET_EL] = GPIO_SET_EL_CMD;
    cmd[ETHER_PING] = ETHER_PING_CMD;
    cmd[HUMAN_OPERATE_TASK] = HUMAN_REPONSE_CMD;
    cmd[PIN_PWM_SET_DUTY_CYCLE] = PWM_SET_CYCLE_CMD;
    cmd[PIN_485_COMMUNICATION] = CHECK_485_COMMUNICATION;
    cmd[PIN_232_COMMUNICATION] = CHECK_232_COMMUNICATION;
    cmd[PIN_CAN_COMMUNICATION] = CHECK_CAN_COMMUNICATION;
    cmd[TEMPERATURE_SENSOR_STATUS] = TEMPERATURE_SENSOR_CMD;
    cmd[AUDIO_SENSOR_STATUS] = CHECK_AUDIO_CMD;
    cmd[PIN_VOLTAGE_VALUE] = CHECK_PIN_VOLTAGE_CMD;
    cmd[PIN_PWM_ECAP] = CHECK_PIN_PWM_ECAP_CMD;
    cmd[ARC_LIGHT_BOARD_EFFECT_CODE] = ARC_LIGHT_BOARD_EFFECT;
    cmd[ARC_LIGHT_BOARD_STATUS_CODE] = CHECK_PIN_VOLTAGE_CMD;
    cmd[CHECK_PIN_STATUS_TASK] = CHECK_GPIO_EL_CMD;
    cmd[CHECK_FLASH_TASK] = CHECK_FLASH_CMD;
    cmd[ERASE_FLASH_TASK] = ERASE_FLASH_CMD;
    cmd[CHECK_S1_SWITCH_TASK] = CHECK_SW1_CMD;
    cmd[CHECK_RELAY_TASK] = CHECK_RELAY_CMD;
    cmd[METER_CHIP_CHECK_TASK] = RELAY_BOARD_METER_CHIP_CHECK;

    cmd[MassEneryStore_WRITE_TASK] = MASS_ENERGY_STORE_WRITE_CMD;
    cmd[MassEneryStore_READ_TASK] = MASS_ENERGY_STORE_READ_CMD;//大储只用这两个命令，具体的信息放在body中
    cmd[HUMI_SENSOR_TASK] = HUMI_SENSOR_CMD;
    cmd[PRESS_SENSOR_TASK] = PRESS_SENSOR_CMD;
    cmd[ACCELE_SENSOR_TASK] = ACCELE_SENSOR_CMD;
    cmd[PT1000_TEMP_TASK] = PT1000_TEMP_CMD;
    cmd[PRESS_SENSOR_TASK] = PRESS_SENSOR_CMD;
    cmd[LIQUID_LEVEL_TASK] = LIQUID_LEVEL_CMD;
    cmd[BMW_CONFIG_TASK] = BMW_CONFIG_CMD;
    cmd[INSULATION_CHECK_TASK] = INSULATION_CHECK_CMD;

    cmd[MCC_BOARD_CHECK_TASK] = MCC_BOARD_CHECK_CMD;
}
ProtocolMaintainer::~ProtocolMaintainer()
{

}

int ProtocolMaintainer::getCmd(TestOjectOperateCode  operateCode)
{
    auto iter = cmd.find(operateCode);
    if(iter != cmd.end())
    {
        return iter.value();
    }
    return -1;
}

void ProtocolMaintainer::addHeader(QByteArray & data)
{
    data.append(0xFF);
    data.append(0x6A);
    data.append(0xA6);
}

void ProtocolMaintainer::addFixedPackageInfo(QByteArray & data)
{
    //版本和保留字节
    data.append(0x10);
}
void ProtocolMaintainer::addByte(int data,QByteArray & outData)
{
    outData.append(data & 0xff);
    return;
}
void ProtocolMaintainer::add2Bytes(int data,QByteArray & outData)
{
    //小端模式
    outData.append(data & 0xff);
    outData.append((data >> 8) & 0xff);
    return;
}
void ProtocolMaintainer::add4Bytes(int data,QByteArray & outData)
{
    //小端模式
    outData.append(data & 0xff);
    outData.append((data >> 8) & 0xff);
    outData.append((data >> 16) & 0xff);
    outData.append((data >> 24) & 0xff);
    return;
}
void ProtocolMaintainer::addArray(QByteArray & src,QByteArray & outData)
{
    //小端模式
    outData.append(src);
    return;
}
void ProtocolMaintainer::addCrc(QByteArray & outData)
{
    int checkHiLo = CRC16(reinterpret_cast<uint8*>(outData.data() + 3),outData.size()-3, Hi_Lo);

    outData.append(checkHiLo & 0xff);
    outData.append((checkHiLo >> 8) & 0xff);
}

void ProtocolMaintainer::updateMsgId(QByteArray & data)
{
    //TODO atomic
    data.append(msgId & 0xff);
    data.append((msgId >> 8) & 0xff);
    msgId++;
    return;
}

void ProtocolMaintainer::add2BytesBigEndian(int data,QByteArray & outData)
{
    //大端模式
    outData.append((data >> 8) & 0xff);
    outData.append(data & 0xff);
    return;
}

void ProtocolMaintainer::add4BytesBigEndian(int data,QByteArray & outData)
{
    //大端模式
    outData.append((data >> 24) & 0xff);
    outData.append((data >> 16) & 0xff);
    outData.append((data >> 8) & 0xff);
    outData.append(data & 0xff);
    return;
}

void ProtocolMaintainer::addCrcBigEndian(QByteArray & outData)
{
    //大端模式
    int checkHiLo = CRC16(reinterpret_cast<uint8*>(outData.data() + 3),outData.size()-3, Hi_Lo);

    outData.append((checkHiLo >> 8) & 0xff);
    outData.append(checkHiLo & 0xff);
    return;
}
