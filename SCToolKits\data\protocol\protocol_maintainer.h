﻿#ifndef PROTOCOLMAINTAINER_H
#define PROTOCOLMAINTAINER_H

#include <QString>
#include <QMap>
#include "data/test_objects/support_test_define.h"
#include "data/light_color_check/light_color_check_data.h"
typedef enum
{
    HAND_SHAK_CMD = 0x0,

    HUMAN_REPONSE_CMD = 0x10,
    //每一个隔一个
    GPIO_DIRECTION_CMD = 0x69, //gpio操作请求
    GPIO_SET_EL_CMD = 0x71, //gpio操作请求
    ETHER_PING_CMD = 0x73, //ether ping
    PWM_SET_CYCLE_CMD = 0x75, //PWM duty cycle
    CHECK_485_COMMUNICATION = 0x77, //验证485通信可靠性
    CHECK_232_COMMUNICATION = 0x79, //验证232通信可靠性.当前232都是复用了CHECK_485_COMMUNICATION
    CHECK_CAN_COMMUNICATION = 0x81, //验证232通信可靠性.当前232都是复用了CHECK_485_COMMUNICATION
    TEMPERATURE_SENSOR_CMD = 0x83, //温度检测
    CHECK_AUDIO_CMD = 0x85, //音频检测
    CHECK_PIN_VOLTAGE_CMD = 0x87, //引脚电压检测
    CHECK_PIN_PWM_ECAP_CMD = 0x89,   // pwm捕获检测
    CHECK_GPIO_EL_CMD = 0x93,			// GPIO电平检测
    CHECK_FLASH_CMD = 0x95,             // flash检测
    ERASE_FLASH_CMD = 0x97,				// flash擦除
    RELAY_BOARD_METER_CHIP_CHECK = 0x9b, //arc继电器板电表检测
    ARC_LIGHT_BOARD_EFFECT = 0x9D, //ARC灯板灯带灯效控制命令

    MASS_ENERGY_STORE_WRITE_CMD = 0x9F,//专用于大储的写。
    MASS_ENERGY_STORE_READ_CMD = 0xA1,//专用于大储的读。

    CHECK_SW1_CMD = 0xA3,               // S1开关检测
    CHECK_RELAY_CMD = 0XA5,             // 继电器检测
    HUMI_SENSOR_CMD = 0xA7,             /*湿度检测*/
    PRESS_SENSOR_CMD = 0xA9,            /*压力检测*/
    ACCELE_SENSOR_CMD = 0xAB,           /*加速度检测*/
    PT1000_TEMP_CMD = 0xAD,             /*PT1000检测*/

    LIQUID_LEVEL_CMD = 0xAF,            //液位检测
    BMW_CONFIG_CMD = 0xB3,              //BMW配置
    UPGRADE_EVCC_PLC_CMD = 0xB5,        //更新EVCC固件
    INSULATION_CHECK_CMD = 0xB7,        //专用于绝缘检测的信令

    MCC_BOARD_CHECK_CMD = 0xB9,         // MCC测试的命令
}RequestCmd;

class ProtocolMaintainer
{
public:
    static ProtocolMaintainer *get()
    {
        if(instance==nullptr)
        {
            instance = new ProtocolMaintainer;
        }
        return instance;
    }
    static void addHeader(QByteArray & data);
    static void addFixedPackageInfo(QByteArray & data);
    static void addByte(int data,QByteArray & outData);
    static void add2Bytes(int data,QByteArray & outData);
    static void add4Bytes(int data,QByteArray & outData);
    static void addArray(QByteArray & src,QByteArray & outData);
    static void addCrc(QByteArray & outData);

    //大端序
    static void add2BytesBigEndian(int data,QByteArray & outData);
    static void add4BytesBigEndian(int data,QByteArray & outData);
    static void addCrcBigEndian(QByteArray & outData);

public:
    int getCmd(TestOjectOperateCode  operateCode);
    void updateMsgId(QByteArray & data);

private:
    ProtocolMaintainer();
    ~ProtocolMaintainer();
private:
    static ProtocolMaintainer *instance;
private:
    QMap<TestOjectOperateCode,RequestCmd>cmd;
    short int msgId;
};

#endif // PROTOCOLMAINTAINER_H
