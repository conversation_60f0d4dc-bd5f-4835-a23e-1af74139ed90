﻿#ifndef SUPPORT_TEST_DEFINE_H
#define SUPPORT_TEST_DEFINE_H
#define JACK_1_PORT "jack1Port"
#define JACK_2_PORT "jack2Port"
#define JACK_3_PORT "jack3Port"
#define JACK_4_PORT "jack4Port"
#define JACK_5_PORT "jack5Port"
#define JACK_6_PORT "jack6Port"
#define JACK_7_PORT "jack7Port"
#define JACK_8_PORT "jack8Port"
#define JACK_9_PORT "jack9Port"
#define JACK_10_PORT "jack10Port"
#define JACK_11_PORT "jack11Port"
#define JACK_12_PORT "jack12Port"
#define JACK_13_PORT "jack13Port"
#define JACK_21_PORT "jack21Port"
#define JACK_22_PORT "jack22Port"
#define JACK_23_PORT "jack23Port"
#define JACK_24_PORT "jack24Port"
#define JACK_25_PORT "jack25Port"

#define BOARD_TEMPERATURE "boradTemperature"
#define POWER_SUPPLY_CASE "powerSupply"
#define JACK_PIN_GPIO_TYPE "gpio"
#define JACK_ETHERNET "ethernet"
#define BOARD_VOLTAGE "BoardVoltage"

#define CONFIG_4G "4gConfig"
#define CONFIG_DNS "dnsConfig"
#define CONFIG_OCPP "ocppConfig"
#define CONFIG_PILEID "pileIdConfig"
#define CONFIG_LANGUAGE "languageConfig"
#define CONFIG_OCPP_ATLANTE "ocppConfigAtlante"

#define SCHNEIDER_CPID_CONFIG "schneiderCpidConfig"
#define SCHNEIDER_FACTORY_CONFIG "schneiderFactoryConfig"

#define CHECK_4G "4gCheck"

#define BEEP_CONTROL "beepControl"
#define LED_CONTROL "ledControl"
#define RFID_READ "rfidRead"
#define RFID_WRITE "rfidWrite"
#define BMW_RFID_WRITE "BMWRfidWrite"

#define EMERGRNCY_STOP "emergencyStop"
#define LEAKAGE_PROTECTION "leakageProtection"
#define CHECK_SWITCH "checkSwitch"
#define CTRL_RELAY "ctrlRelay"
#define PE_DETECTION "peDetection"
#define CHECK_PIN_VOLTAGE "check12vVoltage"
#define CHECK_US_PIN_VOLTAGE "checkUS12vVoltage"
#define CHECK_CP_CIRCUIT "checkCpCircuit"
#define CHECK_ELECTRONIC_LOCK "checkElectronicLock"
#define CHECK_DRY_NODE "checkDryNode"
#define CHECK_S2Switch "checkS2Switch"
#define CHECK_FLASH "checkFlash"

#define CHECK_S1Switch "checkS1Switch"
#define CHECK_METER "checkMeter"
#define CHECK_METER_COMMUNICATION "checkMeterCommunication"
#define CHECK_SCREEN_COMMUNICATION "checkScreenCommunication"
#define CHECK_RFID_COMMUNICATION "checkRfidCommunication"

#define CHECK_MAIN_RELAY "checkMainRelay"
#define CHECK_PEN_RELAY "checkPENRelay"
#define CHECK_ONEPHASE_METER_CHIP "checkOnePhaseMeterChip"
#define CHECK_THREEPHASE_METER_CHIP "checkThreePhaseMeterChip"
#define CHECK_LEAKAGE_FAULT "checkLeakageFault"
#define CHECK_ADAPTER_BOARD_PEN_RELAY "checkAdapterBoardPenRelay"

#define CHECK_STEPPING_MOTOR "checkSteppingMotor"
#define CHECK_HUMI_SENSOR "checkHumiSensor"
#define CHECK_PRESS_SENSOR "checkPressSensor"
#define CHECK_LIQUID_COOL "checkLiquidCool"
#define CHECK_AC_METER "checkACMeter"
#define CHECK_DPAU "checkDpau"
#define CHECK_CCU "checkCCU"
#define CHECK_FAULT "checkFault"
#define CHECK_LEVEL_FEEDBACK "checkLevelFeedback"
#define CHECK_COMMUNICATION "checkCommunication"
//防呆方式
#define CPID_MODE "CPIDMode"

#define GB_CONFIG_FUN "GBConfigFun"
#define BMW_CONFIG "BMWConfig"
#define CHECK_CANCOM_COMMUNICATION "checkCanCommunication"
#define CHECK_VCC_VOLTAGE "checkVccVoltage"
#define CHECK_5V_VOLTAGE "check5vVoltage"
#define CHECK_N_12V_VOLTAGE "checkN12vVoltage"
#define CHECK_CP_VOLTAGE "checkCPVoltage"
#define CHECK_3V3_VOLTAGE "check3v3Voltage"
#define CHECK_GPVDDC_VOLTAGE "checkGpvddcVoltage"
#define CHECK_AVDDXAL_VOLTAGE "checkAvddxalVoltage"
#define CHECK_AVDD_VOLTAGE "checkAvddVoltage"
#define CHECK_VDDP_VOLTAGE "checkVddpVoltage"
#define CHECK_PFBOU1_VOLTAGE "checkPfbou1Voltage"
#define CHECK_PD_RESISTANCE "checkPDResistance"

#define ADHESION_CHECK "adhesionCheck"
#define CHECK_WIFI "checkWifi"
#define CHECK_UNCAP "checkUncap"
#define CHECK_TIC "checkTic"
#define CHECK_CC_VOLTAGE "checkCcVoltage"
#define CHECK_NETWORK "checkNetwork"

#define CHECK_DSO_BOARD_GPIO "checkDsoBoardGpio"
#define CHECK_DSO_BOARD_UART "checkDsoBoardUart"
#define CHECK_SWITCH_BOARD_GPIO "checkSwitchBoardUart"
#define CHECK_SWITCH_BOARD_PWM "checkSwitchBoardPwm"

#define CHECK_MCC_BOARD "checkMCCBoard"

typedef enum
{
    //GPIO的设置
    PIN_CIRCUIT_OPEN = 0,
    PIN_GPIO_EXPORT = 1,
    PIN_GPIO_SET_DIRECTON ,//IN and OUT
    PIN_GPIP_SET_EL,
    PIN_GPIO_UNEXPORT,
    PIN_VOLTAGE_VALUE,
    //PWM设置
    PIN_PWM_SET_DUTY_CYCLE,
    PIN_PWM_ECAP,

    //SERIAL
    PIN_485_COMMUNICATION,
    PIN_232_COMMUNICATION,
    PIN_CAN_COMMUNICATION,
    //网络
    ETHER_PING,

    TEMPERATURE_SENSOR_STATUS,

    AUDIO_SENSOR_STATUS,
    //尚量板卡
    SHANG_LIANG_VOLTAGE_RANG,
    SHANG_LIANG_VOLTAGE_STATUS,
    //
    LIGHT_COLOR_STATUS,
    //
    VOLTAGE_STATUS,
    DIPSWITCH_STATUS,
    //
    SSH_TASK,
    CURL_TASK,
    //
    TRIGGER_TASK,
    //
    FTP_TASK,
    FTP_DOWNLOAD_TASK,
    //
    JLINK_CMD_TASK,

    //写卡
    YI_HUO_YAN_RFID_TYPE,
    YI_HUO_YAN_RFID_STATUS,
    //提示相关
    HUMAN_OPERATE_TASK,

    //ARC灯板
    ARC_LIGHT_BOARD_EFFECT_CODE,
    ARC_LIGHT_BOARD_STATUS_CODE,

    //ARCh主板
    CHECK_PIN_STATUS_TASK,
    CHECK_FLASH_TASK,
    ERASE_FLASH_TASK,

    //美标弯月主板
    CHECK_S1_SWITCH_TASK,
    CHECK_RELAY_TASK,
    CHECK_METER_TASK,

    //继电器板
    METER_CHIP_CHECK_TASK,

    //大储
    MassEneryStore_WRITE_TASK,
    MassEneryStore_READ_TASK,

    //施耐德生产配置
    SCHNEIDER_PRODUCTION_CONFIG_TASK,
    //出厂配置
    SCHNEIDER_FACTORY_CONFIG_TASK,
    SCHNEIDER_CHECK_CONFIG_TASK,
    SCHNEIDER_CHECK_CERTIFICATE_TASK,
    SCHNEIDER_CHECK_SECURE_TASK,
    GET_SE05X_CHIP_UID,
    GET_RTP_SERVER_PID,
    KILL_RTP_SERVER_PID,
    RUN_RTP_SERVER,
    WAITE_SE05X_CHIP,

    //MES
    MES_REQUEST_TASK,
    //
    COPY_FILE_TASK,

    //环境检测板
    HUMI_SENSOR_TASK,
    PRESS_SENSOR_TASK,
    ACCELE_SENSOR_TASK,
    PT1000_TEMP_TASK,

    LIQUID_LEVEL_TASK,

    CREATE_FILE_TASK,
    BMW_CONFIG_TASK,
    MODBUS_TEST_TASK,
    INSULATION_CHECK_TASK,

    //
    TEST_TYPE_TASK,
    EMERGENCY_STOP_TASK,
    PD_RESISTANCE_TASK,

    MCC_BOARD_CHECK_TASK,

	UNKOWN_OPERATE_CODE
}TestOjectOperateCode;


#endif // SUPPORT_TEST_DEFINE_H
