#include "bess_cabinet_window.h"
#include "ui_bess_cabinet_window.h"
#include <QDateTime>
#include <QRegularExpression>
#include <QStyle>

BESSCabinetWindow::BESSCabinetWindow(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::BESSCabinetWindow)
{
    ui->setupUi(this);

    // 初始化按钮样式
    initButtonStyle();

}

BESSCabinetWindow::~BESSCabinetWindow()
{
    delete ui;
}


void BESSCabinetWindow::on_pushButton_Disconnect_clicked()
{
    emit closeSSHRequest();
}

void BESSCabinetWindow::updateSSHStatus(bool status)
{
    if(status)
    {
        ui->pushButton_Disconnect->setProperty("status", "connected");
    }
    else
    {
        ui->pushButton_Disconnect->setProperty("status", "closed");
    }

    // 刷新样式
    ui->pushButton_Disconnect->style()->unpolish(ui->pushButton_Disconnect);
    ui->pushButton_Disconnect->style()->polish(ui->pushButton_Disconnect);
}

void BESSCabinetWindow::initButtonStyle()
{
    QString buttonStyle = R"(
          /* 基础样式 */
          QPushButton#pushButton_Disconnect
          {
              font-family: "Microsoft YaHei";
              font-size: 48px;
              font-weight: 600;
              padding: 12px 24px;
              min-width: 320px;
              border-radius: 6px;
          }

          /* 断开状态：灰色 */
          QPushButton#pushButton_Disconnect[status="disconnected"]
          {
              background-color: rgb(248, 249, 250);
              color: rgb(33, 37, 41);
              border: 2px solid rgb(206, 212, 218);
          }

          QPushButton#pushButton_Disconnect[status="disconnected"]:hover
          {
              background-color: rgb(233, 236, 239);
              border: 2px solid rgb(173, 181, 189);
          }

          QPushButton#pushButton_Disconnect[status="disconnected"]:pressed
          {
              background-color: rgb(222, 226, 230);
              border: 2px solid rgb(134, 142, 150);
          }

          /* 连接状态：绿色 */
          QPushButton#pushButton_Disconnect[status="connected"]
          {
              background-color: rgb(85, 255, 127);
              color: rgb(25, 25, 25);
              border: 2px solid rgb(76, 175, 80);
          }

          QPushButton#pushButton_Disconnect[status="connected"]:hover
          {
              background-color: rgb(95, 255, 137);
              border: 2px solid rgb(56, 142, 60);
          }

          QPushButton#pushButton_Disconnect[status="connected"]:pressed
          {
              background-color: rgb(75, 235, 117);
              border: 2px solid rgb(46, 125, 50);
          }

          /* 关闭状态：红色 */
          QPushButton#pushButton_Disconnect[status="closed"]
          {
              background-color: rgb(255, 100, 100);
              color: rgb(25, 25, 25);
              border: 2px solid rgb(244, 67, 54);
          }

          QPushButton#pushButton_Disconnect[status="closed"]:hover
          {
              background-color: rgb(255, 110, 110);
              border: 2px solid rgb(198, 40, 40);
          }

          QPushButton#pushButton_Disconnect[status="closed"]:pressed
          {
              background-color: rgb(245, 90, 90);
              border: 2px solid rgb(183, 28, 28);
          })";

        ui->pushButton_Disconnect->setStyleSheet(buttonStyle);

        ui->pushButton_Disconnect->setProperty("status", "disconnected");

        // 刷新样式
        ui->pushButton_Disconnect->style()->unpolish(ui->pushButton_Disconnect);
        ui->pushButton_Disconnect->style()->polish(ui->pushButton_Disconnect);

}
