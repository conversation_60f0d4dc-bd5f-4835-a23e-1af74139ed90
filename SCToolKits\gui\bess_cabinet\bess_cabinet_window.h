#ifndef BESS_CABINET_WINDOW_H
#define BESS_CABINET_WINDOW_H

#include <QMainWindow>

namespace Ui {
class BESSCabinetWindow;
}

class BESSCabinetWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit BESSCabinetWindow(QWidget *parent = nullptr);
    ~BESSCabinetWindow();
    void initButtonStyle();

signals:
    void closeSSHRequest();

private slots:
    void on_pushButton_Disconnect_clicked();

public slots:
    void updateSSHStatus(bool status);

private:
    Ui::BESSCabinetWindow *ui;
};

#endif // BESS_CABINET_WINDOW_H
