<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>BESSCabinetWindow</class>
 <widget class="QMainWindow" name="BESSCabinetWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1054</width>
    <height>550</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <property name="sizePolicy">
    <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <widget class="QWidget" name="layoutWidget">
    <property name="geometry">
     <rect>
      <x>21</x>
      <y>131</y>
      <width>1001</width>
      <height>249</height>
     </rect>
    </property>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_4">
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_3">
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="logoLabel">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="maximumSize">
            <size>
             <width>161</width>
             <height>151</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="pixmap">
            <pixmap resource="../../src.qrc">:/img/power_upgrade/StarCharge_logo.png</pixmap>
           </property>
           <property name="scaledContents">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_2">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="horizontalSpacer_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Maximum</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>41</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Maximum</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>349</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_Disconnect">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="minimumSize">
          <size>
           <width>372</width>
           <height>150</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QPushButton#pushButton_Disconnect
{
    background-color: rgb(248, 249, 250);    /* 非常浅的灰色背景 */
    color: rgb(33, 37, 41);                  /* 深灰色文字 */
    font-family: &quot;Microsoft YaHei&quot;;
    font-size: 48px;
    font-weight: 600;                        /* 稍微减轻字重 */
    padding: 12px 24px;
    min-width: 320px;
    
    border: 2px solid rgb(206, 212, 218);    /* 浅灰色边框 */
    border-radius: 6px;
    
    /* 简洁阴影 */
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}

QPushButton#pushButton_Disconnect:hover
{
    background-color: rgb(233, 236, 239);    /* 悬停时稍深 */
    border: 2px solid rgb(173, 181, 189);
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);              /* 轻微上浮效果 */
}

QPushButton#pushButton_Disconnect:pressed
{
    background-color: rgb(222, 226, 230);
    border: 2px solid rgb(134, 142, 150);
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.1);
    transform: translateY(0px);
}</string>
         </property>
         <property name="text">
          <string>加密并关闭</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_4">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1054</width>
     <height>23</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources>
  <include location="../../src.qrc"/>
 </resources>
 <connections/>
</ui>
