#include "serial_port_dialog.h"
#include "ui_serial_port_dialog.h"
#include "ip_context.h"
#include <QDebug>
SerialPortDialog::SerialPortDialog(QWidget *parent) :
    QDialog(parent),
    linkType(SERIAL_PORT),
    linkTypeIndex(0),
    ui(new Ui::SerialPortDialog)
{
    ui->setupUi(this);

    ui->comboBox_other->hide();
    ui->label_11->hide();

    chargStationIPLineEdit = new QIPLineEdit(this);
    chargStationIPLineEdit->setStyleSheet("background: #F4F5FD;border-radius: 2px;font-size:18px");
    chargStationIPLineEdit->setMinimumSize(QSize(200,40));
    chargStationIPLineEdit->setMaximumSize(QSize(414,40));
    ui->horizontalLayout_17->addWidget(chargStationIPLineEdit);

    updateSerialInfo();
    connect(&timer, &QTimer::timeout, this, &SerialPortDialog::updateSerialInfo);
    timer.start(1000);
}

SerialPortDialog::~SerialPortDialog()
{
    delete ui;
}

void SerialPortDialog::on_btn_ok_clicked()
{
    int index =ui->comboBox_7->currentIndex();
    linkType = (LinkType)index;
    linkTypeIndex = index;
    emit changeLinkTypeSignal(index);

    updateConfigData(); 

    hide();
    return;
}

void SerialPortDialog::updateConfigData()
{
    if(linkType == SERIAL_PORT)
    {
        emit serialPortInfoSignal(ui->comboBox->currentText(),
                                  ui->comboBox_2->currentText().toInt(),
                                  ui->comboBox_4->currentText().toInt(),
                                  ui->comboBox_5->currentText().toInt(),
                                  ui->comboBox_3->currentText().toInt(),
                                  ui->comboBox_6->currentText().toInt());
    }
    else if(linkType == SSH)
    {
        QStringList auxInfo;
        QString auxName = ui->comboBox_other->currentText();
        if(auxName == QString::fromUtf8("直流登录") ||
           auxName == QString::fromUtf8("DC Login"))
        {
            auxInfo<<ui->lineEdit_webIp->text();
            auxInfo<<ui->lineEdit_webPwd->text();
        }

        emit sshInfoSignal(ui->lineEdit_ip->text(),ui->lineEdit_user->text(),ui->lineEdit_pwd->text(),ui->comboBox_model->currentIndex(),auxInfo);
    }
    else if (linkType == ETHERNET_LINK_E)
    {
        emit ethernetInfoSignal(chargStationIPLineEdit->text(),ui->lineEdit->text().toInt());
    }
}

void SerialPortDialog::updateSerialInfo()
{
    //updateConfigData();
    QList<QSerialPortInfo> list = QSerialPortInfo::availablePorts();
    int index = 0;
    if(list.size() == 0)
    {
        ui->comboBox->clear();
        return;
    }
    foreach(QSerialPortInfo one, list)
    {
        if(one.portName() == ui->comboBox->itemText(index++))
        {
            continue;
        }
        else
        {
            ui->comboBox->clear();
            foreach(QSerialPortInfo one, list)
            {
                ui->comboBox->addItem(one.portName());
            }
            return;
        }
    }
}
void SerialPortDialog::setLinkType(LinkType type)
{
    if((type < MAX_LINK_TYPE))
    {
        linkTypeIndex = (int)type;
        ui->comboBox_7->setCurrentIndex(linkTypeIndex);
        ui->stackedWidget->setCurrentIndex(linkTypeIndex);
        linkType = type;
    }
}

void SerialPortDialog::setSSHModel(int  model)
{
    if(model == 0)
    {
        ui->comboBox_model->setCurrentIndex(0);

    }
    else
    {
        ui->comboBox_model->setCurrentIndex(1);
    }
}

void SerialPortDialog::setSSHCertLoginUser(int user)
{
    //1 是交流
    if(user == 1)
    {
        ui->comboBox_other->setCurrentIndex(0);
    }
    else
    {
         ui->comboBox_other->setCurrentIndex(1);
    }
}
void SerialPortDialog::setBaud(int baud)
{
    ui->comboBox_2->setCurrentText(QString("%1").arg(baud));
}

void SerialPortDialog::on_comboBox_other_currentTextChanged(const QString &arg1)
{
    if(arg1 == QString::fromUtf8("交流登录") ||
       arg1 == QString::fromUtf8("AC Login"))
    {
        ui->stackedWidget_2->setCurrentIndex(2);
    }
    else if(arg1 == QString::fromUtf8("直流登录") ||
            arg1 == QString::fromUtf8("DC Login"))
    {
        ui->stackedWidget_2->setCurrentIndex(1);
    }
    else
    {
       qDebug() << " ";
    }
}

void SerialPortDialog::on_comboBox_model_currentTextChanged(const QString &arg1)
{
    if(arg1 == QString::fromUtf8("账密模式") ||
       arg1 == QString::fromUtf8("Account Password Mode"))
    {
        ui->stackedWidget_2->setCurrentIndex(0);
        ui->comboBox_other->hide();
        ui->label_11->hide();
    }
    else if(arg1 == QString::fromUtf8("密钥模式") ||
            arg1 == QString::fromUtf8("Key Mode"))
    {
        ui->stackedWidget_2->setCurrentIndex(2);
        ui->comboBox_other->show();
        ui->label_11->show();
    }
    else
    {
        qDebug() << " ";
    }
}



void SerialPortDialog::on_comboBox_7_currentIndexChanged(int index)
{
    ui->stackedWidget->setCurrentIndex(index);
}

void SerialPortDialog::on_pushButton_released()
{
    emit openCanDevcieSignal(ui->comboBox_8->currentText(),ui->spinBox->value());
}
