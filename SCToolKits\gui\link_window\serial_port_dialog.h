#ifndef SERIAL_PORT_DIALOG_H
#define SERIAL_PORT_DIALOG_H

#include <QDialog>
#include <QSerialPortInfo>
#include <QTimer>
#include "ip_context.h"
#include "devices/device_context.h"

typedef enum
{
    SERIAL_PORT = 0,
    SSH,
    ETHERNET_LINK_E,
    MAX_LINK_TYPE
}LinkType;

namespace Ui {
class SerialPortDialog;
}

class SerialPortDialog : public QDialog
{
    Q_OBJECT

public:
    explicit SerialPortDialog(QWidget *parent = nullptr);
    ~SerialPortDialog();
signals:
    void serialPortInfoSignal(const QString &,int baud,int databit, int stopbit, int parity,int flowctrl);
    void changeLinkTypeSignal(int);
    void ethernetInfoSignal(const QString &,int);
    void sshInfoSignal(const QString & ip,const QString & usr,const QString & psw,int sshModel,const QStringList &auxInfo);
    //for can
    void openCanDevcieSignal(const QString & type,int deviceIndex);
public slots:
    void setLinkType(LinkType type);
    void setSSHCertLoginUser(int user);
    void setSSHModel(int  modle);
    void setBaud(int  baud);
private slots:
    void on_btn_ok_clicked();
    void on_comboBox_model_currentTextChanged(const QString &arg1);
    void on_comboBox_other_currentTextChanged(const QString &arg1);
    void on_comboBox_7_currentIndexChanged(int index);

    void on_pushButton_released();

public slots:
    void updateSerialInfo();
    void updateConfigData();
public:
    LinkType linkType;
    int linkTypeIndex;//0 串口，1,SSH,2：以太网，3：多通信组合：比如SSH和以太网，
private:
    Ui::SerialPortDialog *ui;
    QTimer timer;
    QIPLineEdit *chargStationIPLineEdit;
};

#endif // SERIAL_PORT_DIALOG_H
