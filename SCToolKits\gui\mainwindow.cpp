﻿#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QLabel>
#include <QDebug>
#include "gui/can_config/can_config_window.h"
#include "communication/link_manager.h"

MainWindow::MainWindow(QWidget *parent, bool toolSelect)
    : QMainWindow(parent),
      ui(new Ui::MainWindow),curToolWindow(nullptr),commonConfigWindow(nullptr),mesLoginWindow(nullptr),
      authorityWindow(nullptr),
      testObjectGenerateWindow(nullptr)
{
    ui->setupUi(this);
    if(toolSelect == false)
    {
        ui->comboBox->hide();
    }

    ui->lab_materialNumber->setVisible(false);
    ui->lineEdit_materialNumber->setVisible(false);
    on_btn_toolTestResult_clicked();
    ui->lineEdit_SN->setAttribute(Qt::WA_InputMethodEnabled, false);
}

MainWindow::~MainWindow()
{
    if(mesLoginWindow)
    {
        delete mesLoginWindow;
    }
    delete ui;
}
void MainWindow::setWindowTitleSlot(const QString & info)
{
    setWindowTitle(info);
}
QWidget * MainWindow::getToolsWindow()
{
    //return ui->toolsWidget;
    return nullptr;
}
void MainWindow::updateToolSelectEnableSlot(bool enable)
{
    if(enable)
    {
        ui->comboBox->show();
    }
    else
    {
        ui->comboBox->hide();
    }
}
void MainWindow::setToolsList(const QStringList & ToolsList)
{
    ui->comboBox->clear();

    foreach( auto tool,ToolsList)
    {
        ui->comboBox->addItem(tool);
    }
}

void MainWindow::addTool(QWidget *tool)
{
    if(curToolWindow)
    {
        curToolWindow->close();
    }
    ui->verticalLayout->addWidget(tool);

    curToolWindow = tool;
}
bool MainWindow::event(QEvent *event)
{
    if(event->type() == QEvent::LanguageChange)
    {
        QString tool = ui->comboBox->currentText();
        ui->retranslateUi(this);

        ui->label->setText(tr(tool.toStdString().c_str()));
    }
    return QMainWindow::event(event);
}
void MainWindow::on_comboBox_currentTextChanged(const QString &tool)
{
    emit updateToolSignal(tool);
}

void MainWindow::setToolName(const QString & tools)
{
    ui->comboBox->setCurrentText(tools);
}

void MainWindow::sendChangeToolSignalSlot(const QString &toolname)
{
    emit updateToolSignal(toolname);
}

void MainWindow::showMainWinMaxSlot(bool UIFlag)
{
    if(UIFlag == true)
    {
        this->showMaximized();
    }
    else
    {
        this->showNormal();
    }
}

void MainWindow::hideCanConfigSlot(bool hideFlag)
{
    if(hideFlag == true)
    {
        ui->btn_canConfig->hide();
        ui->lab_canConnectStatus->hide();
        ui->btn_canConnect->hide();
    }
    else
    {
        ui->btn_canConfig->show();
        ui->lab_canConnectStatus->show();
        ui->btn_canConnect->show();
    }
}

void MainWindow::hideComConfigSlot(bool hideFlag)
{
    if(hideFlag == true)
    {
        ui->btn_comConfig->hide();
        ui->lab_comSta->hide();
        ui->btn_comConnect->hide();
        ui->label_2->hide();
        ui->label_3->hide();
        ui->lab_SSHSta->hide();
        ui->lab_comSta->hide();
        ui->label_4->hide();
        ui->lab_eth_link->hide();
    }
    else
    {
        ui->btn_comConfig->show();
        ui->lab_comSta->show();
        ui->btn_comConnect->show();
        ui->label_2->show();
        ui->label_3->show();
        ui->lab_SSHSta->show();
        ui->lab_comSta->show();
        ui->label_4->show();
        ui->lab_eth_link->show();
    }
}

void MainWindow::hideMesConfigSlot(bool hideFlag)
{
    if(hideFlag == true)
    {
        ui->btn_mesConfig->hide();
        ui->lab_mesLoginSta->hide();
    }
    else
    {
        ui->btn_mesConfig->show();
        ui->lab_mesLoginSta->show();
    }
}

void MainWindow::hideSNInfoSlot(bool hideFlag)
{
    if(hideFlag == true)
    {
        ui->lab_SN->hide();
        ui->lineEdit_SN->hide();
        //ui->lab_materialNumber->hide();
        //ui->lineEdit_materialNumber->hide();
    }
    else
    {
        ui->lab_SN->show();
        ui->lineEdit_SN->show();
        //ui->lab_materialNumber->show();
        //ui->lineEdit_materialNumber->show();
    }
}

void MainWindow::on_btn_comConfig_clicked()
{
    emit showCommWindowSignal();
}

void MainWindow::on_btn_comConnect_clicked()
{
    if(ui->btn_comConnect->text() == QString::fromUtf8("连接") ||
       ui->btn_comConnect->text() == QString::fromUtf8("Connect"))
    {
        emit commConnectSignal();
    }
    else
    {
        emit commDisconnectSignal();
    }
}

void MainWindow::updateSSHLinkResultSlot(bool sta, QString ip, int port)
{
    if(sta)
    {
        ui->lab_SSHSta->setStyleSheet("border-radius:5px;background:green");
        ui->btn_comConnect->setText(tr("断开"));
    }
    else
    {
        ui->lab_SSHSta->setStyleSheet("border-radius:5px;background:rgb(213, 213, 213);");
        ui->btn_comConnect->setText(tr("连接"));
    }
}

void MainWindow::updateSerialLinkResultSlot(bool result)
{
    if(result)
    {
        ui->lab_comSta->setStyleSheet("border-radius:5px;background:green");
        ui->btn_comConnect->setText(tr("断开"));
    }
    else
    {
        ui->lab_comSta->setStyleSheet("border-radius:5px;background:rgb(213, 213, 213);");
        ui->btn_comConnect->setText(tr("连接"));
    }
}

void MainWindow::updateEthernetLinkResultSlot(bool sta, const QString & ip, int port)
{
    if(sta)
    {
        ui->lab_eth_link->setStyleSheet("border-radius:5px;background:green");
        ui->btn_comConnect->setText(tr("断开"));
    }
    else
    {
        ui->lab_eth_link->setStyleSheet("border-radius:5px;background:rgb(213, 213, 213);");
        ui->btn_comConnect->setText(tr("连接"));
    }
}
void MainWindow::on_btn_mesConfig_clicked()
{
    emit openMesLoginWindowSigal();
}

void MainWindow::updateMesLoginResultSlot(bool sta, QString ctx)
{
    if(sta)
    {
        ui->lab_mesLoginSta->setStyleSheet("border-radius:5px;background:green");
        ui->btn_mesConfig->setStyleSheet("#btn_mesConfig{"
                                         "background:#92DCDD;"
                                         "border-radius:5px;}"
                                         "#btn_mesConfig:hover{"
                                         "border:1px solid rgb(112, 145, 255);}");
        ui->lineEdit_SN->setPlaceholderText("请输入SN码！");
        ui->lineEdit_materialNumber->setPlaceholderText("请输入SN码！");
    }
    else
    {
        ui->btn_mesConfig->setStyleSheet("#btn_mesConfig{"
                                         "background:rgb(255, 249, 215);"
                                         "border-radius:5px;}"
                                         "#btn_mesConfig:hover{"
                                         "border:1px solid rgb(112, 145, 255);}");
        ui->lab_mesLoginSta->setStyleSheet("border-radius:5px;background:rgb(213, 213, 213);");
    }
}

void MainWindow::on_btn_toolTestResult_clicked()
{
    if(ui->scrollArea->isHidden())
    {
        ui->scrollArea->show();
        ui->btn_toolTestResult->setIcon(QIcon(":/img/single_board_check/down.png"));
    }
    else
    {
        ui->scrollArea->hide();
        ui->btn_toolTestResult->setIcon(QIcon(":/img/single_board_check/up.png"));
    }
}

void MainWindow::on_actionright_triggered(bool checked)
{
    emit configActionTriggeredSignal();
}

void MainWindow::on_actiondebug_triggered(bool checked)
{
    qDebug()<<"opendebug";
    emit openDebugSignal();
}

void MainWindow::on_actioncaseTemplate_triggered(bool checked)
{
    //TODO:功能临时关闭。
#if 0
    if(testObjectGenerateWindow == nullptr)
    {
        testObjectGenerateWindow =  new TestObjectGenerateWindow();
    }
    testObjectGenerateWindow->show();
    qDebug()<< "click";
#endif
}

void MainWindow::on_btn_canConfig_clicked()
{
//    CanConfigWindow *canConfig = new CanConfigWindow(this);
//    canConfig->show();
    emit showCanCfgWinSignal();
}

void MainWindow::on_btn_canConnect_clicked()
{
    if(ui->btn_canConnect->text() == QString::fromUtf8("连接"))
    {
        emit connectCanSignal();
    }
    else
    {
        emit disConnectCanSignal();
    }
}

void MainWindow::updateCanConnectStatusSlot(bool status)
{
    if(status)
    {
        ui->lab_canConnectStatus->setStyleSheet("border-radius:13px;background:green");
        ui->btn_canConnect->setText(tr("断开"));
        ui->btn_canConfig->setEnabled(false);
    }
    else
    {
        ui->lab_canConnectStatus->setStyleSheet("border-radius:13px;background:red");
        ui->btn_canConnect->setText(tr("连接"));
        ui->btn_canConfig->setEnabled(true);
    }
}

void MainWindow::on_lineEdit_SN_returnPressed()
{
    emit editSnFinishedSignal(ui->lineEdit_SN->text());
    ui->lineEdit_SN->clearFocus();
}

void MainWindow::updateLinkBtnTextSLot(QString text)
{
    ui->btn_comConnect->setText(tr(text.toStdString().c_str()));
}


void MainWindow::on_equipmentAdd_triggered()
{
    emit equipmentAddSignal();
}

void MainWindow::on_equipmentSearch_triggered()
{
    emit equipmentSearchSignal();
}

void MainWindow::clearSnCodeSlot()
{
    ui->lineEdit_SN->clear();
    ui->lineEdit_SN->setFocus();
}

void MainWindow::updateSNCodeSlot(const QString & snCode)
{
    ui->lineEdit_SN->setText(snCode);
}

void MainWindow::on_actionappInfo_triggered()
{
    emit openAppInfoSignal();
}

void MainWindow::on_actioninstructionsforuse_triggered()
{
    emit openTutorialMnanualSignal();
}

void MainWindow::on_comboBox_currentIndexChanged(const QString &arg1)
{
    // 适配中英切换，取消UI里面配置的槽函数
    ui->label->setText(tr(arg1.toStdString().c_str()));
}

void MainWindow::on_action_triggered()
{
    emit openMenuConfigSignal();
}

void MainWindow::on_toolSwitch_triggered()
{
    emit openMenuConfigSignal();
}
