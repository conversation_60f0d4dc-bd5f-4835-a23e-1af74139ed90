﻿#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QStringList>
#include "gui/link_window/serial_port_dialog.h"
#include "gui/mes_login/mes_login_window.h"
#include "menus/settings/authority.h"
#include "menus/settings/test_object_generate_window.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr, bool toolSelect = true);
    ~MainWindow();
public:
    QWidget * getToolsWindow();
    void addTool(QWidget *tool);
    void setToolsList(const QStringList & list);
    void setToolName(const QString & tools);
protected:
    bool event(QEvent *event)override;
public slots:
    void setWindowTitleSlot(const QString & info);
    void sendChangeToolSignalSlot(const QString &toolname);
    void showMainWinMaxSlot(bool);
    void hideCanConfigSlot(bool);
    void hideComConfigSlot(bool);
    void hideMesConfigSlot(bool);
    void hideSNInfoSlot(bool);

    void updateSerialLinkResultSlot(bool);
    void updateSSHLinkResultSlot(bool, QString, int);
    void updateEthernetLinkResultSlot(bool sta, const QString & ip, int port);
    void updateMesLoginResultSlot(bool, QString);

    void updateCanConnectStatusSlot(bool);
    void updateLinkBtnTextSLot(QString);

    void clearSnCodeSlot();
    void updateSNCodeSlot(const QString &);
    void updateToolSelectEnableSlot(bool);
private slots:
    void on_comboBox_currentTextChanged(const QString &arg1);

    void on_btn_comConfig_clicked();
    void on_btn_comConnect_clicked();
    void on_btn_mesConfig_clicked();

    void on_btn_toolTestResult_clicked();

private slots:
    void on_actionright_triggered(bool checked);
    void on_actiondebug_triggered(bool checked);
    void on_actioncaseTemplate_triggered(bool checked);
    void on_btn_canConfig_clicked();
    void on_btn_canConnect_clicked();

    void on_lineEdit_SN_returnPressed();

    void on_equipmentAdd_triggered();

    void on_equipmentSearch_triggered();

    void on_actionappInfo_triggered();

    void on_actioninstructionsforuse_triggered();

    void on_comboBox_currentIndexChanged(const QString &arg1);

    void on_action_triggered();

    void on_toolSwitch_triggered();


signals:
    void updateToolSignal(const QString &);
    void showCommWindowSignal();
    void showCanCfgWinSignal();
    void commConnectSignal();
    void commDisconnectSignal();
    void connectCanSignal();
    void disConnectCanSignal();

    //menu 信号
    void configActionTriggeredSignal();
    void openMenuConfigSignal();
    void openDebugSignal();
    //menu for equipments
    void equipmentSearchSignal();
    void equipmentAddSignal();

    //MES
    void openMesLoginWindowSigal();
    //SN号
    void editSnFinishedSignal(const QString & sn);

    //help
    void openAppInfoSignal();
    void openTutorialMnanualSignal();
private:
    Ui::MainWindow *ui;

    QWidget * curToolWindow;
    SerialPortDialog * commonConfigWindow;
    MesLoginWindow * mesLoginWindow;
private:
    Authority * authorityWindow;
    TestObjectGenerateWindow * testObjectGenerateWindow;
};
#endif // MAINWINDOW_H
