#include "authority.h"
#include "ui_authority.h"
#include<QIntValidator>
Authority::Authority(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::Authority)
{
    ui->setupUi(this);
    QIntValidator *validator = new QIntValidator(3, 100, ui->lineEdit); // 设置范围为 3 到 100
    ui->lineEdit->setValidator(validator);
    updateVisible(false);

    ui->lineEdit_accunt->setAttribute(Qt::WA_InputMethodEnabled, false);
    ui->lineEdit_password->setAttribute(Qt::WA_InputMethodEnabled, false);
}

Authority::~Authority()
{
    delete ui;
}
bool Authority::event(QEvent *event)
{
    if(event->type() == QEvent::LanguageChange)
    {
        ui->retranslateUi(this);
    }
    return QMainWindow::event(event);
}

void Authority::updateVisible(bool visible,int type)
{
    if(visible == false)
    {
        ui->radioButton->hide();
        ui->radioButton_2->hide();
        ui->groupBox->hide();
        ui->radioButton_3->hide();
        ui->lineEdit->hide();
        ui->pushButton_3->hide();
        ui->groupBox_2->hide();
        ui->groupBox_3->hide();
        ui->groupBox_4->hide();
        return;
    }
    else
    {
        //type == 0;管理员用户
        //type == 1;超级用户
        if(type == 0)
        {
            ui->base_authority_widget->show();
            ui->root_authority_wideget->show();
        }
        else if (type == 1)
        {
            ui->base_authority_widget->show();
            ui->root_authority_wideget->hide();
        }
        if(visible)
        {
            ui->radioButton->show();
            ui->groupBox->show();
            ui->radioButton_2->show();
            ui->radioButton_3->show();
            ui->lineEdit->show();
            ui->pushButton_3->show();
            ui->groupBox_2->show();
            ui->groupBox_3->show();
    //        ui->label->show();
            ui->groupBox_4->show();
        }
    }


}
void Authority::closeEvent(QCloseEvent *event)
{
    updateVisible(false);
}
void Authority::on_buttonBox_accepted()
{
    QString accunt = ui->lineEdit_accunt->text();
    QString password = ui->lineEdit_password->text();
    loginConfigSignal(accunt, password);
}
void Authority::loginResultSlot(bool ret,int type)
{
    if(ret)
    {
        updateVisible(ret,type);
    }
    else
    {
        //弹框处理
    }
}
void Authority::updateLogOutToFileSlot(bool ret)
{
    ui->radioButton->setChecked(ret);
}
void Authority::on_radioButton_toggled(bool checked)
{
    emit enableLogOutFileSignal(checked);
}

void Authority::on_radioButton_2_toggled(bool checked)
{
    emit enableRootModelSignal(checked);
}

void Authority::on_radioButton_3_toggled(bool checked)
{
    emit enableToolSwitchSignal(checked);
}

void Authority::on_pushButton_3_pressed()
{
    int reconTime = ui->lineEdit->text().toInt();
    emit sshReconnectTimeSignal(reconTime);
}

void Authority::on_radioButton_4_clicked(bool checked)
{
    if(checked)
    {
        emit guiDisplayLanguageSignal(0);
    }
}

void Authority::on_radioButton_5_clicked(bool checked)
{
    if(checked)
    {
        emit guiDisplayLanguageSignal(1);
    }
}

void Authority::on_radioButton_7_clicked(bool checked)
{
    if(checked)
    {
        emit dependMesSignal(checked,false);
    }

}

void Authority::on_radioButton_8_clicked(bool checked)
{
    if(checked)
    {
        emit dependMesSignal(!checked,false);
    }
}

void Authority::on_lineEdit_password_returnPressed()
{
    on_buttonBox_accepted();
}

void Authority::on_pushButton_clicked()
{
    QString filePath = QFileDialog::getOpenFileName(this,"选择文件",QDir::currentPath(),
                                                    "所有文件 (*.*)"  //文件过滤器
                                                    );

        if (!filePath.isEmpty())
        {
            QString fileName = QFileInfo(filePath).fileName();

            ui->lineEdit_2->setText(fileName);

            // 完整路径
            // lineEdit->setText(filePath);
        }
}

void Authority::on_lineEdit_2_returnPressed()
{
    emit setSshPrivateKeyFileSignal(ui->lineEdit_2->text());
}

void Authority::on_radioButton_9_clicked(bool checked)
{
    emit enableMesDeviceIDModifySingal(checked);
}
