#ifndef AUTHORITY_H
#define AUTHORITY_H

#include <QMainWindow>
#include <QFileDialog>
namespace Ui {
class Authority;
}

class Authority : public QMainWindow
{
    Q_OBJECT

public:
    explicit Authority(QWidget *parent = nullptr);
    ~Authority();
private:
    void closeEvent(QCloseEvent *event);
    void updateVisible(bool visible,int type=-1);
protected:
    bool event(QEvent *event);
public slots:
    void loginResultSlot(bool ret,int type);
    void updateLogOutToFileSlot(bool ret);
signals:
    void loginConfigSignal(const QString&, const QString&);
    void enableLogOutFileSignal(bool enable);
    void enableRootModelSignal(bool enable);
    void enableToolSwitchSignal(bool enable);
    void sshReconnectTimeSignal(int );
    void guiDisplayLanguageSignal(int);
    void dependMesSignal(bool,bool);
    void setSshPrivateKeyFileSignal(const QString &);
    void enableMesDeviceIDModifySingal(bool);
private slots:
    void on_buttonBox_accepted();


    void on_radioButton_toggled(bool checked);

    void on_radioButton_2_toggled(bool checked);

    void on_radioButton_3_toggled(bool checked);

    void on_pushButton_3_pressed();

    void on_radioButton_4_clicked(bool checked);

    void on_radioButton_5_clicked(bool checked);

    void on_radioButton_7_clicked(bool checked);

    void on_radioButton_8_clicked(bool checked);

    void on_lineEdit_password_returnPressed();

    void on_pushButton_clicked();

    void on_lineEdit_2_returnPressed();

    void on_radioButton_9_clicked(bool checked);


private:
    Ui::Authority *ui;
};

#endif // AUTHORITY_H
