<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Authority</class>
 <widget class="QMainWindow" name="Authority">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1241</width>
    <height>523</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>权限管理</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QDialogButtonBox" name="buttonBox">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>130</y>
      <width>321</width>
      <height>51</height>
     </rect>
    </property>
    <property name="minimumSize">
     <size>
      <width>0</width>
      <height>35</height>
     </size>
    </property>
    <property name="font">
     <font>
      <family>等线</family>
      <pointsize>16</pointsize>
     </font>
    </property>
    <property name="styleSheet">
     <string notr="true">QPushButton{
	background-color: rgb(129, 211, 255);
	border-radius:5px;
	width:80;
	height:35px;
	font-size:20px;
}
QPushButton:hover{
	border:1px solid rgb(193, 232, 255);
}</string>
    </property>
    <property name="standardButtons">
     <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
    </property>
   </widget>
   <widget class="QTextBrowser" name="textBrowser">
    <property name="geometry">
     <rect>
      <x>20</x>
      <y>210</y>
      <width>321</width>
      <height>261</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>12</pointsize>
     </font>
    </property>
    <property name="html">
     <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'SimSun'; font-size:12pt; font-weight:400; font-style:normal;&quot;&gt;
&lt;p style=&quot; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;span style=&quot; font-size:14pt;&quot;&gt;    备注，后期根据不同的账号，可以对应不同的工具设置显示和设置&lt;br /&gt;    Note that different tools can be set up for display and configuration based on different accounts in the future&lt;/span&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
    </property>
   </widget>
   <widget class="QLineEdit" name="lineEdit_password">
    <property name="geometry">
     <rect>
      <x>110</x>
      <y>76</y>
      <width>221</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>等线 Light</family>
      <pointsize>12</pointsize>
     </font>
    </property>
    <property name="echoMode">
     <enum>QLineEdit::Normal</enum>
    </property>
   </widget>
   <widget class="QLineEdit" name="lineEdit_accunt">
    <property name="geometry">
     <rect>
      <x>110</x>
      <y>36</y>
      <width>221</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>隶书</family>
      <pointsize>12</pointsize>
     </font>
    </property>
   </widget>
   <widget class="QLabel" name="label_2">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>40</y>
      <width>91</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>等线</family>
      <pointsize>14</pointsize>
     </font>
    </property>
    <property name="text">
     <string>账号：</string>
    </property>
   </widget>
   <widget class="QLabel" name="label_3">
    <property name="geometry">
     <rect>
      <x>10</x>
      <y>76</y>
      <width>91</width>
      <height>31</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <family>等线</family>
      <pointsize>14</pointsize>
     </font>
    </property>
    <property name="text">
     <string>密码：</string>
    </property>
   </widget>
   <widget class="Line" name="line">
    <property name="geometry">
     <rect>
      <x>350</x>
      <y>0</y>
      <width>20</width>
      <height>441</height>
     </rect>
    </property>
    <property name="orientation">
     <enum>Qt::Vertical</enum>
    </property>
   </widget>
   <widget class="QStackedWidget" name="base_authority_widget">
    <property name="geometry">
     <rect>
      <x>370</x>
      <y>10</y>
      <width>841</width>
      <height>191</height>
     </rect>
    </property>
    <widget class="QWidget" name="page">
     <widget class="QRadioButton" name="radioButton">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>50</y>
        <width>191</width>
        <height>31</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>等线</family>
        <pointsize>14</pointsize>
       </font>
      </property>
      <property name="text">
       <string>输入日志到文件</string>
      </property>
      <property name="autoExclusive">
       <bool>false</bool>
      </property>
     </widget>
     <widget class="QGroupBox" name="groupBox">
      <property name="geometry">
       <rect>
        <x>320</x>
        <y>20</y>
        <width>401</width>
        <height>111</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>等线</family>
        <pointsize>14</pointsize>
       </font>
      </property>
      <property name="tabletTracking">
       <bool>false</bool>
      </property>
      <property name="title">
       <string>显示语言设置</string>
      </property>
      <property name="checkable">
       <bool>false</bool>
      </property>
      <property name="checked">
       <bool>false</bool>
      </property>
      <widget class="QWidget" name="layoutWidget">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>50</y>
         <width>341</width>
         <height>32</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QRadioButton" name="radioButton_4">
          <property name="font">
           <font>
            <family>等线</family>
            <pointsize>15</pointsize>
           </font>
          </property>
          <property name="text">
           <string>中文</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="radioButton_5">
          <property name="font">
           <font>
            <family>等线</family>
            <pointsize>15</pointsize>
           </font>
          </property>
          <property name="text">
           <string>英文</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </widget>
    <widget class="QWidget" name="page_2"/>
   </widget>
   <widget class="QStackedWidget" name="root_authority_wideget">
    <property name="geometry">
     <rect>
      <x>370</x>
      <y>210</y>
      <width>881</width>
      <height>291</height>
     </rect>
    </property>
    <property name="currentIndex">
     <number>1</number>
    </property>
    <widget class="QWidget" name="page_3"/>
    <widget class="QWidget" name="page_4">
     <widget class="QGroupBox" name="groupBox_2">
      <property name="geometry">
       <rect>
        <x>430</x>
        <y>150</y>
        <width>401</width>
        <height>121</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>等线</family>
        <pointsize>14</pointsize>
       </font>
      </property>
      <property name="tabletTracking">
       <bool>false</bool>
      </property>
      <property name="title">
       <string>SSH重连时间(秒)</string>
      </property>
      <property name="checkable">
       <bool>false</bool>
      </property>
      <property name="checked">
       <bool>false</bool>
      </property>
      <widget class="QWidget" name="horizontalLayoutWidget">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>49</y>
         <width>341</width>
         <height>61</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <widget class="QLineEdit" name="lineEdit">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>等线</family>
            <pointsize>13</pointsize>
           </font>
          </property>
          <property name="placeholderText">
           <string>输入10到100</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_3">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>40</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>100</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>等线</family>
            <pointsize>-1</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
	background-color: rgb(129, 211, 255);
	border-radius:5px;
	width:80;
	height:35px;
	font-size:20px;
}
QPushButton:hover{
	border:1px solid rgb(193, 232, 255);
}
</string>
          </property>
          <property name="text">
           <string>设置</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QGroupBox" name="groupBox_4">
      <property name="geometry">
       <rect>
        <x>430</x>
        <y>30</y>
        <width>401</width>
        <height>111</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>等线</family>
        <pointsize>14</pointsize>
       </font>
      </property>
      <property name="tabletTracking">
       <bool>false</bool>
      </property>
      <property name="title">
       <string>SSH连接密钥文件</string>
      </property>
      <property name="checkable">
       <bool>false</bool>
      </property>
      <property name="checked">
       <bool>false</bool>
      </property>
      <widget class="QWidget" name="layoutWidget_3">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>50</y>
         <width>341</width>
         <height>52</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <widget class="QLineEdit" name="lineEdit_2">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>16</pointsize>
           </font>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton">
          <property name="minimumSize">
           <size>
            <width>100</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>等线</family>
            <pointsize>-1</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton{
	background-color: rgb(129, 211, 255);
	border-radius:5px;
	width:80;
	height:35px;
	font-size:20px;
}
QPushButton:hover{
	border:1px solid rgb(193, 232, 255);
}</string>
          </property>
          <property name="text">
           <string>选择文件</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QGroupBox" name="groupBox_3">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>150</y>
        <width>401</width>
        <height>111</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>等线</family>
        <pointsize>14</pointsize>
       </font>
      </property>
      <property name="tabletTracking">
       <bool>false</bool>
      </property>
      <property name="title">
       <string>MES依赖选择(非单次)</string>
      </property>
      <property name="checkable">
       <bool>false</bool>
      </property>
      <property name="checked">
       <bool>false</bool>
      </property>
      <widget class="QWidget" name="layoutWidget_2">
       <property name="geometry">
        <rect>
         <x>30</x>
         <y>50</y>
         <width>341</width>
         <height>32</height>
        </rect>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <item>
         <widget class="QRadioButton" name="radioButton_7">
          <property name="font">
           <font>
            <family>等线</family>
            <pointsize>15</pointsize>
           </font>
          </property>
          <property name="text">
           <string>依赖MES</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QRadioButton" name="radioButton_8">
          <property name="font">
           <font>
            <family>等线</family>
            <pointsize>15</pointsize>
           </font>
          </property>
          <property name="text">
           <string>不依赖MES</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>50</y>
        <width>361</width>
        <height>30</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_5">
       <item>
        <widget class="QRadioButton" name="radioButton_2">
         <property name="font">
          <font>
           <family>等线</family>
           <pointsize>14</pointsize>
          </font>
         </property>
         <property name="text">
          <string>管理员模式</string>
         </property>
         <property name="autoExclusive">
          <bool>false</bool>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QRadioButton" name="radioButton_3">
         <property name="font">
          <font>
           <family>等线</family>
           <pointsize>14</pointsize>
          </font>
         </property>
         <property name="text">
          <string>工具切换</string>
         </property>
         <property name="autoExclusive">
          <bool>false</bool>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QRadioButton" name="radioButton_9">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>100</y>
        <width>221</width>
        <height>28</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>等线</family>
        <pointsize>14</pointsize>
       </font>
      </property>
      <property name="text">
       <string>使能MESID可修改</string>
      </property>
      <property name="autoExclusive">
       <bool>false</bool>
      </property>
     </widget>
    </widget>
   </widget>
  </widget>
 </widget>
 <tabstops>
  <tabstop>lineEdit_accunt</tabstop>
  <tabstop>lineEdit_password</tabstop>
  <tabstop>lineEdit</tabstop>
  <tabstop>pushButton_3</tabstop>
  <tabstop>radioButton_4</tabstop>
  <tabstop>radioButton_5</tabstop>
  <tabstop>radioButton</tabstop>
  <tabstop>radioButton_2</tabstop>
  <tabstop>radioButton_3</tabstop>
  <tabstop>textBrowser</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
