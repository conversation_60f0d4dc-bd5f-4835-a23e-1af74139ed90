#include "config_window.h"
#include "ui_config_window.h"
#include <QDebug>
ConfigWindow::ConfigWindow(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::ConfigWindow)
{
    ui->setupUi(this);
    ui->btn_matrialBind->setVisible(false);
    ui->btn_otherCfg->setVisible(false);
    ui->btn_network_check->setVisible(false);
    ui->table_acMeterMaterial->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
}

ConfigWindow::~ConfigWindow()
{
    delete ui;
}

void ConfigWindow::updateConfigToolGUI(const QString & toolName,int type)
{
    ui->comboBox_2->setCurrentIndex(0);
    ui->comboBox_2->setItemText(0,toolName);
    qDebug()<< ui->comboBox_2->currentText();
    currToolFunType = type;
//    on_btn_matrialBind_clicked();
    switch (currToolFunType)
    {
        case 15:
        case 16:
        case 52:
        case 59:
        {
            updateGUI(2, 0);
        }
        break;
        case 37: //线损补偿工具
        {
            updateGUI(1, 1);
        }
        break;
        case 39: //交流电表校表
        {
            updateGUI(1, 0);
        }
        break;
        case 64://EVCC_CHARGE_FCT_E
        {
            updateGUI(4,0);
        }
        break;
        case 40://PCS_M215_AGEING_E
        {
            updateGUI(5,0);
        }
        case 50://INTERNAL_DC_CONFIGE_E
        {
            updateGUI(6,0);
        }
        break;
        default:
        {
            updateGUI(0, 0);
        }
        break;
    }
}

void ConfigWindow::setMenuShowEnable(bool enable)
{
    ui->btn_otherCfg->setEnabled(enable);
    ui->btn_matrialBind->setEnabled(enable);
}

void ConfigWindow::updateGUI(int mainIndex, int nextIndex)
{
    ui->stackedWidget_main->setCurrentIndex(mainIndex);
    if(mainIndex == 1)  //物料绑定界面
    {
        ui->btn_matrialBind->setEnabled(true);
        ui->stackedWidget_matialCode->setCurrentIndex(nextIndex);
    }
    else if(mainIndex == 2)
    {
        ui->btn_network_check->setEnabled(true);
    }
    else if(mainIndex == 3) //其他配置
    {
        ui->btn_otherCfg->setEnabled(true);
        ui->stackedWidget_other->setCurrentIndex(nextIndex);
    }
}

void ConfigWindow::showAcMeterMaterialDataSlot(QList<QStringList> &list)
{
    if(list.isEmpty())
    {
        return;
    }

    ui->table_acMeterMaterial->clearContents();
    ui->table_acMeterMaterial->setRowCount(0);
    ui->comboBox_acMeterCode->clear();

    //更新物料数据表格
    for(auto iter : list)
    {
        int curRowCnt = ui->table_acMeterMaterial->rowCount();
        ui->table_acMeterMaterial->setRowCount(curRowCnt + 1);
        ui->comboBox_acMeterCode->addItem(iter[0]);

        for(int j = 0; j < iter.count(); j++)
        {
            QTableWidgetItem * item =  new QTableWidgetItem(iter[j]);
            ui->table_acMeterMaterial->setItem(curRowCnt, j, item);
        }
    }
}


void ConfigWindow::on_btn_addData_clicked()
{
    QStringList materialData;
    QString materialCode = ui->lineEdit_acMeterCode->text();
    QString currentRate = ui->lineEdit_addCurrRate->text();
    QString connectMode = QString::number(ui->comboBox_conMode->currentIndex());

    if(materialCode.isEmpty() || currentRate.isEmpty() || connectMode.isEmpty())
    {
        qDebug() << "add data is null";
        return;
    }
    materialData.append(materialCode);
    materialData.append(currentRate);
    materialData.append(connectMode);

    qDebug() << "add data: " << materialData;
    emit addAcMeterMaterialDataSignal(materialData);
}

void ConfigWindow::on_btn_deleteData_clicked()
{
    QString materialCode = ui->comboBox_acMeterCode->currentText();
    if(materialCode.isEmpty())
    {
        qDebug() << "delete data is null";
        return;
    }
    qDebug() << "delete data: " << materialCode;
    emit deleteAcMeterMaterialDataSignal(materialCode);
}

void ConfigWindow::on_btn_ok_clicked()
{
    emit settingNetworkInfoSignal(ui->radioButton_3->isChecked() ? 1 : 0,//设置是否自动获取测试项
                                  ui->radioButton_2->isChecked() ? 2 : 1);//设置脚本逻辑
    ui->label_6->setText("设置成功！");
}

void ConfigWindow::on_pushButton_released()
{
    int num = ui->comboBox->currentText().toInt();
    emit evccfctNumSignal(num);
}

void ConfigWindow::on_pushButton_2_pressed()
{
    int index = ui->comboBox_3->currentIndex();
    emit mesWorkstationSignal(index+1);
}

void ConfigWindow::on_pushButton_3_pressed()
{
    emit softwareVersionSignal(ui->textEdit->toPlainText());
}

void ConfigWindow::on_pushButton_4_released()
{
    int value = ui->comboBox_4->currentText().toInt();
    emit ageingTimeSignal(value);
}

void ConfigWindow::on_pushButton_5_released()
{
    int value = ui->comboBox_5->currentText().toInt();
    emit ageingPowerSignal(value);
}

void ConfigWindow::on_btn_choose_config_clicked()
{
    QString filePath = QFileDialog::getOpenFileName(this,"选择文件",QDir::currentPath(),
                                                    "所有文件 (*.*)"  //文件过滤器
                                                    );
    if (!filePath.isEmpty())
    {
        ui->lineEdit->setText(filePath);
    }
}

void ConfigWindow::on_lineEdit_returnPressed()
{
    emit updateXmlFileSignal(ui->lineEdit->text());
}
