#ifndef CONFIG_WINDOW_H
#define CONFIG_WINDOW_H

#include <QMainWindow>
#include <QTableWidget>
#include <QFileDialog>

namespace Ui {
class ConfigWindow;
}

class ConfigWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit ConfigWindow(QWidget *parent = nullptr);
    ~ConfigWindow();

public slots:
    void updateConfigToolGUI(const QString & toolName,int type);

    // ac meter
    void showAcMeterMaterialDataSlot(QList<QStringList> & list);
signals:
    void addAcMeterMaterialDataSignal(QStringList &);
    void deleteAcMeterMaterialDataSignal(QString &);
    void settingNetworkInfoSignal(int,int);

    void mesWorkstationSignal(int);

    void evccfctNumSignal(int);

    void softwareVersionSignal(const QString &);

    //老化配置：
    void ageingTimeSignal(int);
    void ageingPowerSignal(int);
    void updateXmlFileSignal(const QString &);
private slots:
    void on_btn_addData_clicked();

    void on_btn_deleteData_clicked();

    void on_btn_ok_clicked();

    void on_pushButton_released();

    void on_pushButton_2_pressed();

    void on_pushButton_3_pressed();

    void on_pushButton_4_released();

    void on_pushButton_5_released();

    void on_lineEdit_returnPressed();

    void on_btn_choose_config_clicked();

private:
    void setMenuShowEnable(bool enable);
    void updateGUI(int mainIndex, int nextIndex);

private:
    Ui::ConfigWindow *ui;

    int currToolFunType;
};

#endif // CONFIG_WINDOW_H
