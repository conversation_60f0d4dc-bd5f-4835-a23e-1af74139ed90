<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ConfigWindow</class>
 <widget class="QMainWindow" name="ConfigWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1100</width>
    <height>700</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1100</width>
    <height>700</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>1100</width>
    <height>700</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>工具配置</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QWidget" name="widget" native="true">
    <property name="geometry">
     <rect>
      <x>0</x>
      <y>0</y>
      <width>181</width>
      <height>701</height>
     </rect>
    </property>
    <property name="styleSheet">
     <string notr="true">background-color: rgb(244, 248, 255);</string>
    </property>
    <widget class="QPushButton" name="btn_matrialBind">
     <property name="enabled">
      <bool>false</bool>
     </property>
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>150</y>
       <width>161</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>等线</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">#btn_matrialBind{
	background:rgb(255, 249, 215);
	border-radius:5px;
	border:1px solid black;
}
#btn_matrialBind:hover{
	border:1px solid rgb(112, 145, 255);
}</string>
     </property>
     <property name="text">
      <string>物料绑定</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_otherCfg">
     <property name="enabled">
      <bool>false</bool>
     </property>
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>270</y>
       <width>161</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>等线</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">#btn_otherCfg{
background:rgb(255, 249, 215);
border-radius:5px;
border:1px solid black;
}
#btn_otherCfg:hover{
border:1px solid rgb(112, 145, 255);
}</string>
     </property>
     <property name="text">
      <string>其他配置</string>
     </property>
    </widget>
    <widget class="QPushButton" name="btn_network_check">
     <property name="enabled">
      <bool>false</bool>
     </property>
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>210</y>
       <width>161</width>
       <height>41</height>
      </rect>
     </property>
     <property name="font">
      <font>
       <family>等线</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">#btn_network_check{
	background:rgb(255, 249, 215);
	border-radius:5px;
	border:1px solid black;
}
#btn_network_check:hover{
	border:1px solid rgb(112, 145, 255);
}</string>
     </property>
     <property name="text">
      <string>通讯功能配置</string>
     </property>
    </widget>
    <widget class="QPushButton" name="pushButton_config">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>80</y>
       <width>161</width>
       <height>41</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">#pushButton_config{
background:rgb(255, 249, 215);
border-radius:5px;
border:1px solid black;
}
#pushButton_config:hover{
border:1px solid rgb(112, 145, 255);
}</string>
     </property>
     <property name="text">
      <string>配置</string>
     </property>
    </widget>
    <widget class="QComboBox" name="comboBox_2">
     <property name="geometry">
      <rect>
       <x>10</x>
       <y>20</y>
       <width>161</width>
       <height>41</height>
      </rect>
     </property>
     <property name="styleSheet">
      <string notr="true">background-color: rgb(85, 170, 255);</string>
     </property>
     <property name="editable">
      <bool>false</bool>
     </property>
     <item>
      <property name="text">
       <string/>
      </property>
     </item>
    </widget>
   </widget>
   <widget class="QStackedWidget" name="stackedWidget_main">
    <property name="geometry">
     <rect>
      <x>180</x>
      <y>0</y>
      <width>921</width>
      <height>701</height>
     </rect>
    </property>
    <property name="accessibleName">
     <string/>
    </property>
    <property name="currentIndex">
     <number>6</number>
    </property>
    <widget class="QWidget" name="page_defaut">
     <widget class="QLabel" name="label">
      <property name="geometry">
       <rect>
        <x>250</x>
        <y>260</y>
        <width>381</width>
        <height>131</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>等线</family>
        <pointsize>17</pointsize>
       </font>
      </property>
      <property name="text">
       <string>该工具暂时不支持配置功能！</string>
      </property>
     </widget>
    </widget>
    <widget class="QWidget" name="page_maerial">
     <widget class="QStackedWidget" name="stackedWidget_matialCode">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>20</y>
        <width>891</width>
        <height>651</height>
       </rect>
      </property>
      <property name="currentIndex">
       <number>1</number>
      </property>
      <widget class="QWidget" name="page_acmeter">
       <widget class="QTableWidget" name="table_acMeterMaterial">
        <property name="geometry">
         <rect>
          <x>60</x>
          <y>270</y>
          <width>791</width>
          <height>371</height>
         </rect>
        </property>
        <property name="font">
         <font>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QTableWidget{
 border: 2px solid black;
}</string>
        </property>
        <property name="rowCount">
         <number>0</number>
        </property>
        <property name="columnCount">
         <number>3</number>
        </property>
        <column>
         <property name="text">
          <string>物料号</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>电流变比</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>接线方式</string>
         </property>
        </column>
       </widget>
       <widget class="QPushButton" name="btn_addData">
        <property name="geometry">
         <rect>
          <x>650</x>
          <y>50</y>
          <width>93</width>
          <height>41</height>
         </rect>
        </property>
        <property name="font">
         <font>
          <family>等线</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">#btn_addData{
	background:rgb(240, 255, 247);
	border-radius:5px;
	border:1px solid black;
}
#btn_addData:hover{
	border:1px solid rgb(112, 145, 255);
}</string>
        </property>
        <property name="text">
         <string>增加数据</string>
        </property>
       </widget>
       <widget class="QComboBox" name="comboBox_conMode">
        <property name="geometry">
         <rect>
          <x>420</x>
          <y>90</y>
          <width>151</width>
          <height>41</height>
         </rect>
        </property>
        <property name="font">
         <font>
          <family>等线</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">border:1px solid black</string>
        </property>
        <item>
         <property name="text">
          <string>三相四线</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>三相三线</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>单相二线</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>单相三线</string>
         </property>
        </item>
       </widget>
       <widget class="QLineEdit" name="lineEdit_acMeterCode">
        <property name="geometry">
         <rect>
          <x>190</x>
          <y>40</y>
          <width>381</width>
          <height>41</height>
         </rect>
        </property>
        <property name="font">
         <font>
          <family>等线</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="accessibleName">
         <string/>
        </property>
        <property name="styleSheet">
         <string notr="true">border:1px solid black</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="placeholderText">
         <string>物料号</string>
        </property>
       </widget>
       <widget class="QLineEdit" name="lineEdit_addCurrRate">
        <property name="geometry">
         <rect>
          <x>190</x>
          <y>90</y>
          <width>151</width>
          <height>41</height>
         </rect>
        </property>
        <property name="font">
         <font>
          <family>等线</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="accessibleName">
         <string/>
        </property>
        <property name="styleSheet">
         <string notr="true">border:1px solid black</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="placeholderText">
         <string>电流变比</string>
        </property>
       </widget>
       <widget class="QLabel" name="label_3">
        <property name="geometry">
         <rect>
          <x>60</x>
          <y>50</y>
          <width>121</width>
          <height>31</height>
         </rect>
        </property>
        <property name="font">
         <font>
          <family>等线</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>增加料号数据：</string>
        </property>
       </widget>
       <widget class="QLabel" name="label_2">
        <property name="geometry">
         <rect>
          <x>60</x>
          <y>190</y>
          <width>121</width>
          <height>31</height>
         </rect>
        </property>
        <property name="font">
         <font>
          <family>等线</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>删除料号数据：</string>
        </property>
       </widget>
       <widget class="QPushButton" name="btn_deleteData">
        <property name="geometry">
         <rect>
          <x>650</x>
          <y>180</y>
          <width>93</width>
          <height>41</height>
         </rect>
        </property>
        <property name="font">
         <font>
          <family>等线</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">#btn_deleteData{
	background:rgb(240, 255, 247);
	border-radius:5px;
	border:1px solid black;
}
#btn_deleteData:hover{
	border:1px solid rgb(112, 145, 255);
}</string>
        </property>
        <property name="text">
         <string>删除数据</string>
        </property>
       </widget>
       <widget class="QComboBox" name="comboBox_acMeterCode">
        <property name="geometry">
         <rect>
          <x>190</x>
          <y>180</y>
          <width>381</width>
          <height>41</height>
         </rect>
        </property>
        <property name="font">
         <font>
          <family>等线</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">border:1px solid black;</string>
        </property>
       </widget>
      </widget>
      <widget class="QWidget" name="page_meterloss"/>
     </widget>
    </widget>
    <widget class="QWidget" name="page_network_check">
     <widget class="QWidget" name="horizontalLayoutWidget">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>110</y>
        <width>381</width>
        <height>41</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QLabel" name="label_4">
         <property name="maximumSize">
          <size>
           <width>120</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <family>等线</family>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="text">
          <string>4G检测配置：</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox_2">
         <property name="styleSheet">
          <string notr="true">border:none</string>
         </property>
         <property name="title">
          <string/>
         </property>
         <widget class="QRadioButton" name="radioButton">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>10</y>
            <width>91</width>
            <height>25</height>
           </rect>
          </property>
          <property name="font">
           <font>
            <family>等线</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>基础检测</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QRadioButton" name="radioButton_2">
          <property name="geometry">
           <rect>
            <x>120</x>
            <y>10</y>
            <width>131</width>
            <height>25</height>
           </rect>
          </property>
          <property name="font">
           <font>
            <family>等线</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>验证丢包率</string>
          </property>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QPushButton" name="btn_ok">
      <property name="geometry">
       <rect>
        <x>350</x>
        <y>450</y>
        <width>151</width>
        <height>41</height>
       </rect>
      </property>
      <property name="font">
       <font>
        <family>等线</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="styleSheet">
       <string notr="true">#btn_ok{
	background:rgb(240, 255, 247);
	border-radius:5px;
	border:1px solid black;
}
#btn_ok:hover{
	border:1px solid rgb(112, 145, 255);
}</string>
      </property>
      <property name="text">
       <string>确认</string>
      </property>
     </widget>
     <widget class="QWidget" name="horizontalLayoutWidget_2">
      <property name="geometry">
       <rect>
        <x>20</x>
        <y>50</y>
        <width>381</width>
        <height>41</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <widget class="QLabel" name="label_5">
         <property name="maximumSize">
          <size>
           <width>120</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="font">
          <font>
           <family>等线</family>
           <pointsize>12</pointsize>
          </font>
         </property>
         <property name="text">
          <string>自动获取测试项：</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QGroupBox" name="groupBox">
         <property name="styleSheet">
          <string notr="true">border:none</string>
         </property>
         <property name="title">
          <string/>
         </property>
         <widget class="QRadioButton" name="radioButton_3">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>10</y>
            <width>54</width>
            <height>25</height>
           </rect>
          </property>
          <property name="font">
           <font>
            <family>等线</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>是</string>
          </property>
          <property name="checked">
           <bool>true</bool>
          </property>
         </widget>
         <widget class="QRadioButton" name="radioButton_4">
          <property name="geometry">
           <rect>
            <x>120</x>
            <y>10</y>
            <width>61</width>
            <height>25</height>
           </rect>
          </property>
          <property name="font">
           <font>
            <family>等线</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>否</string>
          </property>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QLabel" name="label_6">
      <property name="geometry">
       <rect>
        <x>300</x>
        <y>400</y>
        <width>251</width>
        <height>31</height>
       </rect>
      </property>
      <property name="layoutDirection">
       <enum>Qt::LeftToRight</enum>
      </property>
      <property name="styleSheet">
       <string notr="true">color: #00FFFF;
background: transparent;
font: bold 25px 'Microsoft YaHei';
padding: 4px;</string>
      </property>
      <property name="text">
       <string/>
      </property>
      <property name="alignment">
       <set>Qt::AlignCenter</set>
      </property>
     </widget>
    </widget>
    <widget class="QWidget" name="page_other">
     <widget class="QStackedWidget" name="stackedWidget_other">
      <property name="geometry">
       <rect>
        <x>10</x>
        <y>10</y>
        <width>891</width>
        <height>671</height>
       </rect>
      </property>
      <property name="currentIndex">
       <number>1</number>
      </property>
      <widget class="QWidget" name="page"/>
      <widget class="QWidget" name="page_2"/>
     </widget>
    </widget>
    <widget class="QWidget" name="page_evcc">
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>81</x>
        <y>61</y>
        <width>501</width>
        <height>87</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_5">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_4">
         <item>
          <widget class="QLabel" name="label_8">
           <property name="minimumSize">
            <size>
             <width>90</width>
             <height>40</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">border-color: rgb(0, 170, 255);
background-color: rgb(170, 255, 0);</string>
           </property>
           <property name="text">
            <string>  工作中心</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBox_3">
           <property name="minimumSize">
            <size>
             <width>300</width>
             <height>50</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(170, 255, 255);</string>
           </property>
           <item>
            <property name="text">
             <string>功能测试</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>功能测试2</string>
            </property>
           </item>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <item>
          <spacer name="verticalSpacer_2">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>18</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_2">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>35</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(85, 170, 255);</string>
           </property>
           <property name="text">
            <string>设置</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_3">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>18</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>81</x>
        <y>200</y>
        <width>504</width>
        <height>87</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_6">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <item>
          <widget class="QLabel" name="label_7">
           <property name="minimumSize">
            <size>
             <width>90</width>
             <height>40</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">border-color: rgb(0, 170, 255);
background-color: rgb(170, 255, 0);</string>
           </property>
           <property name="text">
            <string>  EVCC工装数</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBox">
           <property name="minimumSize">
            <size>
             <width>300</width>
             <height>50</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(170, 255, 255);</string>
           </property>
           <item>
            <property name="text">
             <string>5</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>1</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>2</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>3</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>4</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>5</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>6</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>7</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>8</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>9</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>10</string>
            </property>
           </item>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout">
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>18</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>35</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(85, 170, 255);</string>
           </property>
           <property name="text">
            <string>设置</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_4">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>18</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>80</x>
        <y>320</y>
        <width>501</width>
        <height>81</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_8">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_7">
         <item>
          <widget class="QLabel" name="label_9">
           <property name="minimumSize">
            <size>
             <width>90</width>
             <height>40</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">border-color: rgb(0, 170, 255);
background-color: rgb(170, 255, 0);</string>
           </property>
           <property name="text">
            <string>软件版本</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QTextEdit" name="textEdit">
           <property name="minimumSize">
            <size>
             <width>300</width>
             <height>50</height>
            </size>
           </property>
           <property name="accessibleDescription">
            <string/>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(85, 170, 0);
font: 75 16pt &quot;Arial&quot;;</string>
           </property>
           <property name="html">
            <string>&lt;!DOCTYPE HTML PUBLIC &quot;-//W3C//DTD HTML 4.0//EN&quot; &quot;http://www.w3.org/TR/REC-html40/strict.dtd&quot;&gt;
&lt;html&gt;&lt;head&gt;&lt;meta name=&quot;qrichtext&quot; content=&quot;1&quot; /&gt;&lt;style type=&quot;text/css&quot;&gt;
p, li { white-space: pre-wrap; }
&lt;/style&gt;&lt;/head&gt;&lt;body style=&quot; font-family:'Arial'; font-size:16pt; font-weight:72; font-style:normal;&quot;&gt;
&lt;p style=&quot;-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;&quot;&gt;&lt;br /&gt;&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
           </property>
           <property name="placeholderText">
            <string>输入样式:2.6.7</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QVBoxLayout" name="verticalLayout_3">
         <item>
          <spacer name="verticalSpacer_5">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>18</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_3">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>35</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(85, 170, 255);</string>
           </property>
           <property name="text">
            <string>设置</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_6">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>18</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
    <widget class="QWidget" name="page_ageing">
     <widget class="QWidget" name="layoutWidget_2">
      <property name="geometry">
       <rect>
        <x>50</x>
        <y>220</y>
        <width>433</width>
        <height>64</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_11">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_12">
         <item>
          <widget class="QLabel" name="label_12">
           <property name="minimumSize">
            <size>
             <width>120</width>
             <height>60</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">border-color: rgb(0, 170, 255);
background-color: rgb(170, 255, 0);
font: 75 16pt &quot;Arial&quot;;</string>
           </property>
           <property name="text">
            <string>最大功率</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBox_5">
           <property name="minimumSize">
            <size>
             <width>150</width>
             <height>60</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(170, 255, 255);
font: 75 12pt &quot;Arial&quot;;</string>
           </property>
           <item>
            <property name="text">
             <string>80</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>70</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_5">
           <item>
            <spacer name="verticalSpacer_8">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>17</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QLabel" name="label_13">
             <property name="styleSheet">
              <string notr="true">font: 6pt &quot;Arial&quot;;</string>
             </property>
             <property name="text">
              <string>单位(%)</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_5">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(85, 170, 255);</string>
         </property>
         <property name="text">
          <string>设置</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="layoutWidget">
      <property name="geometry">
       <rect>
        <x>50</x>
        <y>110</y>
        <width>433</width>
        <height>64</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_10">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_9">
         <item>
          <widget class="QLabel" name="label_10">
           <property name="minimumSize">
            <size>
             <width>120</width>
             <height>60</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">border-color: rgb(0, 170, 255);
background-color: rgb(170, 255, 0);
font: 75 16pt &quot;Arial&quot;;</string>
           </property>
           <property name="text">
            <string>老化时长</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QComboBox" name="comboBox_4">
           <property name="minimumSize">
            <size>
             <width>150</width>
             <height>60</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">background-color: rgb(170, 255, 255);
font: 75 12pt &quot;Arial&quot;;</string>
           </property>
           <item>
            <property name="text">
             <string>2</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>4</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>6</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>8</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>10</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>12</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>14</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>16</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>24</string>
            </property>
           </item>
           <item>
            <property name="text">
             <string>48</string>
            </property>
           </item>
          </widget>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_4">
           <item>
            <spacer name="verticalSpacer_7">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>17</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QLabel" name="label_11">
             <property name="styleSheet">
              <string notr="true">font: 6pt &quot;Arial&quot;;</string>
             </property>
             <property name="text">
              <string>单位(小时)</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_4">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(85, 170, 255);</string>
         </property>
         <property name="text">
          <string>设置</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
    <widget class="QWidget" name="page_one_click_config">
     <widget class="QStackedWidget" name="stackedWidget">
      <property name="geometry">
       <rect>
        <x>30</x>
        <y>20</y>
        <width>841</width>
        <height>631</height>
       </rect>
      </property>
      <widget class="QWidget" name="page_3">
       <widget class="QWidget" name="horizontalLayoutWidget_3">
        <property name="geometry">
         <rect>
          <x>50</x>
          <y>70</y>
          <width>537</width>
          <height>80</height>
         </rect>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_9">
         <item>
          <widget class="QLabel" name="label_10">
           <property name="text">
            <string>导入配置文件：</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEdit">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>50</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="btn_choose_config">
           <property name="minimumSize">
            <size>
             <width>100</width>
             <height>50</height>
            </size>
           </property>
           <property name="text">
            <string>选择配置文件</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </widget>
      <widget class="QWidget" name="page_4"/>
     </widget>
    </widget>
   </widget>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
