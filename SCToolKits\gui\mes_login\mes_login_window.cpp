#include "mes_login_window.h"
#include "ui_mes_login_window.h"
#include "app_start/app_config.h"
#include "mes/mes_manager.h"
#include "QDebug"

MesLoginWindow::MesLoginWindow(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::MesLoginWindow)
{
    ui->setupUi(this);
    ui->lineEdit_deviceId->setEnabled(false);
    setWindowFlags(Qt::CustomizeWindowHint | Qt::WindowCloseButtonHint);
    setWindowModality(Qt::ApplicationModal);

    ui->pushButton_login->setAutoDefault(false);
    ui->lineEdit_PWD->setAttribute(Qt::WA_InputMethodEnabled, false);
}

MesLoginWindow::~MesLoginWindow()
{
    delete ui;
}

void MesLoginWindow::setMesConfigInfo(QString & deviceId, QString & account,bool enableModifyId)
{
    qDebug() << "设置账号" << deviceId;
    ui->lineEdit_ID->setText(account);
    ui->lineEdit_deviceId->setText(deviceId);
    ui->lineEdit_deviceId->setEnabled(enableModifyId);
}

void MesLoginWindow::setConfigInfo()
{
    ui->lineEdit_ID->setText(APPConfig::get()->getStringValue("MES/account"));
    ui->lineEdit_deviceId->setText(APPConfig::get()->getStringValue("MES/deviceID"));
}

void MesLoginWindow::on_pushButton_login_clicked()
{
    QString deviceId = ui->lineEdit_deviceId->text();
    QString account = ui->lineEdit_ID->text();
    QString pwd = ui->lineEdit_PWD->text();
    if(pwd.isEmpty() || account.isEmpty())
    {
        //TODO 弹框提示
        return;
    }

    emit mesLoginInfoSinal(deviceId,account,pwd);
    this->close();
}

void MesLoginWindow::showMesLoginWindow()
{
    ui->lineEdit_ID->setFocus();
#ifdef QT_NO_DEBUG
    ui->lineEdit_ID->clear();
    ui->lineEdit_PWD->clear();
#endif
    this->show();
}

void MesLoginWindow::on_lineEdit_ID_returnPressed()
{
    ui->lineEdit_PWD->setFocus();
}
