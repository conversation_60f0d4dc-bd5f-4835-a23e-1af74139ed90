#ifndef MES_LOGIN_WINDOW_H
#define MES_LOGIN_WINDOW_H

#include <QDialog>

namespace Ui {
class MesLoginWindow;
}

class MesLoginWindow : public QDialog
{
    Q_OBJECT

public:
    explicit MesLoginWindow(QWidget *parent = nullptr);
    ~MesLoginWindow();

public slots:
    void setMesConfigInfo(QString &, QString &,bool enableModifyId=false);
    void setConfigInfo();
    void showMesLoginWindow();
signals:
    void mesLoginInfoSinal(const QString &,const QString &,const QString&);
private slots:
    void on_pushButton_login_clicked();

    void on_lineEdit_ID_returnPressed();

private:
    Ui::MesLoginWindow *ui;
};

#endif // MES_LOGIN_WINDOW_H
