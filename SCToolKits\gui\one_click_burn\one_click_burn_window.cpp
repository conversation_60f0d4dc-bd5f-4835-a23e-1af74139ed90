﻿#include "one_click_burn_window.h"
#include "ui_one_click_burn_window.h"
#include <QDebug>
#include <QFileDialog>
OneClickBurnWindow::OneClickBurnWindow(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::OneClickBurnWindow)
{
    ui->setupUi(this);
    initWindow();
}

OneClickBurnWindow::~OneClickBurnWindow()
{
    delete ui;
}

bool OneClickBurnWindow::event(QEvent *event)
{
    if(event->type() == QEvent::LanguageChange)
    {
        ui->retranslateUi(this);
    }
    return QMainWindow::event(event);
}

void OneClickBurnWindow::updateSelectFileName(QString & name)
{
    ui->lineEdit->setText(name);

    ui->lineEdit_firmware_path->setText(name);
}

void OneClickBurnWindow::on_btn_select_clicked()
{
    emit selectFileSignal();
}

void OneClickBurnWindow::on_btn_start_clicked()
{
    ui->upgrade_progressBar->setValue(0);
    emit startUpgradeSignal();
}

void OneClickBurnWindow::updateUpgradeProgress(int value)
{
    int index = ui->stackedWidget->currentIndex();
    if(index == 4)
    {
        ui->progressBar_4->setValue(value);
    }
    else
    {
        ui->upgrade_progressBar->setValue(value);
    }
}

void OneClickBurnWindow::on_lampPlateAddress_spinBox_valueChanged(int arg1)
{
    emit updateAddress(arg1);
}

void OneClickBurnWindow::on_btn_startSNDBrun_clicked()
{
    emit startSchneiderBurnSignal();
    ui->btn_startSNDBrun->setEnabled(false);
    ui->lineEdit_pcbQRCode->setEnabled(false);
    ui->lineEdit_stmQRCode->setEnabled(false);
}

void OneClickBurnWindow::initWindow()
{
    ui->tableWidget->setColumnWidth(0,300);
    ui->tableWidget->setColumnWidth(1,1000);
    ui->tableWidget->setColumnWidth(2,200);
    ui->tableWidget->setColumnWidth(3,200);
    ui->tableWidget->horizontalHeader()->setVisible(true);

    ui->label_5->setText("等待烧录");
    ui->lineEdit_8->clear();
    ui->progressBar_2->setValue(0);

    //schneider
    ui->lineEdit_pcbQRCode->clear();
    ui->lineEdit_stmQRCode->clear();
    ui->lineEdit_firmwarePath->clear();
    ui->label_25->setText("等待烧录");
    ui->lineEdit_stmQRCode->setEnabled(false);
    ui->lineEdit_firmwarePath->setEnabled(false);
    ui->btn_search_file_2->setEnabled(false);
    ui->btn_startSNDBrun->setEnabled(false);
    ui->label_uploadMes_2->hide();
    ui->btn_uploadSNDMes->hide();
    ui->lineEdit_pcbQRCode->setEnabled(false);
    ui->btn_uploadSNDMes->hide();
    ui->label_uploadMes_2->hide();

    //通过secc给evcc_plc升级
    ui->textBrowser_log->clear();
    ui->lineEdit_evcc_version->clear();
    ui->lineEdit_secc_version->clear();
    ui->lineEdit_attenuation_value->clear();
    ui->label_27->setText(tr("请点击按钮开始升级"));
}

void OneClickBurnWindow::unifiedBurnWindowInit()
{
    ui->label_5->setText("等待烧录");
    ui->progressBar_2->setValue(0);
}

void OneClickBurnWindow::updateJLinkWriteDataDisplay(QStringList & data)
{
}

void OneClickBurnWindow::updateJLinkWriteResult(bool result)
{
    if(result)
    {
        ui->progressBar_2->setValue(100);
        ui->label_11->setText(ui->lineEdit_8->text() + " 烧录完成");
        ui->label_5->setText("烧录完成");
        ui->label_25->setText("烧录完成");
    }
    else
    {
        ui->label_11->setText(ui->lineEdit_8->text() + " 烧录失败");
        ui->label_5->setText("烧录失败");
        ui->label_25->setText("烧录失败");
        ui->btn_startSNDBrun->setEnabled(true);
        ui->lineEdit_pcbQRCode->setEnabled(true);
    }
}

void OneClickBurnWindow::updateJLinkWriteProgress(const QString & name, int index, int totalNum)
{
    switch (burnType)
    {
        case UNIFIED_WRITE:
        {
            ui->label_5->setText(name);
            if(totalNum > 0)
            {
                ui->progressBar_2->setValue(100*((float)index/(float)totalNum));
            }
        }
        break;
        case SND_WRITE:
        {
            ui->label_25->setText(name);
            if(totalNum > 0)
            {
                ui->progressBar_3->setValue(100*((float)index/(float)totalNum));
            }
        }
        break;
        default:
        break;
    }

}
void OneClickBurnWindow::switchGUIDisplay(FlashWriteType type)
{
    burnType = type;
    switch (type)
    {
        case LIGHT_BOARD:
        {
            ui->stackedWidget->setCurrentIndex(0);
        }
        break;
        case UNIFIED_WRITE:
        {
            ui->stackedWidget->setCurrentIndex(1);
        }
        break;
        case SND_WRITE:
        {
            ui->stackedWidget->setCurrentIndex(2);
        }
        break;
        case UPGRADE_EVCC_PLC:
        {
            ui->stackedWidget->setCurrentIndex(3);
        }
        break;
        case A35_SECUR_NU_WRITER:
        {
            ui->stackedWidget->setCurrentIndex(4);
        }
        break;
        default:
        break;
    }
}

void OneClickBurnWindow::on_lineEdit_pcbQRCode_returnPressed()
{
    QString snCode = ui->lineEdit_pcbQRCode->text();
    emit snCodeInputFinishedSignal(snCode);
}

void OneClickBurnWindow::on_lineEdit_stmQRCode_returnPressed()
{
    QString stmCode = ui->lineEdit_stmQRCode->text();
    emit stmCodeInputFinishedSignal(stmCode);
}

void OneClickBurnWindow::updateMesLogStatus(bool sta, QString)
{
    if(sta)
    {
        ui->lineEdit_pcbQRCode->setEnabled(true);
        ui->lineEdit_pcbQRCode->setPlaceholderText("请扫描PCB二维码");
        ui->lineEdit_pcbQRCode->setFocus();
    }
    else
    {
        ui->lineEdit_pcbQRCode->setEnabled(false);
        ui->lineEdit_pcbQRCode->setPlaceholderText("请先登录MES");
    }
}

void OneClickBurnWindow::getFirmwareResultSlot(bool result, QString & path)
{
    if(result)
    {
        ui->lineEdit_stmQRCode->clear();
        ui->lineEdit_stmQRCode->setEnabled(true);
        ui->lineEdit_stmQRCode->setPlaceholderText("请扫描核心板二维码");
        ui->lineEdit_stmQRCode->setFocus();
        ui->lineEdit_firmwarePath->setText(path);
    }
    else
    {
        ui->lineEdit_stmQRCode->setEnabled(false);
        ui->lineEdit_stmQRCode->setPlaceholderText("请先扫描PCB二维码");
        ui->lineEdit_firmwarePath->setText(path);
    }
}

void OneClickBurnWindow::checkStmQRCodeResult(bool res)
{
    if(res)
    {
        ui->btn_startSNDBrun->setEnabled(true);
    }
    else
    {
        ui->btn_startSNDBrun->setEnabled(false);
    }
}

void OneClickBurnWindow::updateLogSlot(const QString & log)
{
    ui->textBrowser->append(log);
}

void OneClickBurnWindow::uploadMesUploadResult(bool result)
{
    if(result)
    {
        ui->label_11->setText(ui->lineEdit_8->text() + " 录入成功");
        initWindow();
        ui->lineEdit_pcbQRCode->setEnabled(true);
    }
    else
    {
        ui->label_11->setText(ui->lineEdit_8->text() + " 录入失败");
    }
}

void OneClickBurnWindow::on_lineEdit_SAPCode_returnPressed()
{
    QString SAPCode = ui->lineEdit_SAPCode->text();
    emit getProjectIdInfo(SAPCode);
}

void OneClickBurnWindow::on_btn_search_clicked()
{
    on_lineEdit_SAPCode_returnPressed();
}

void OneClickBurnWindow::updateWorkOrderInfoSlot(QString & productMaterial, QString & productName)
{
    ui->lineEdit_productMaterial->setText(productMaterial);
    ui->lineEdit_productName->setText(productName);
}

void OneClickBurnWindow::updateBurnInfoSlot(int index, QStringList & infoList)
{
    // tablewidget
    if(index == 0)
    {
        ui->tableWidget->clearContents();
    }
    ui->tableWidget->setRowCount(index + 1);
    for(int i = 0; i < ui->tableWidget->columnCount(); i++)
    {
        QTableWidgetItem* item = ui->tableWidget->item(index , i);
        QString text = infoList.at(i);
        if (item)
        {
            item->setText(text);
        }
        else
        {
            // 如果项不存在，你可以创建一个新的项
            item = new QTableWidgetItem(text);
            ui->tableWidget->setItem(index, i, item);
        }
    }
    // combobox
    if(index == 0)
    {
        disconnect(ui->comboBox_program,SIGNAL(currentTextChanged(const QString &)),this,SLOT(on_comboBox_program_currentTextChanged(const QString &)));
        ui->comboBox_program->clear();
        connect(ui->comboBox_program,SIGNAL(currentTextChanged(const QString &)),this,SLOT(on_comboBox_program_currentTextChanged(const QString &)));
    }
    ui->comboBox_program->addItem(infoList[0] + " | " + infoList[1]);
}

void OneClickBurnWindow::updateMaterialCodeInfoSlot(int index, QString & materialCode, QString & materialName)
{
    if(index == 0)
    {
        ui->comboBox_3->clear();
    }
    qDebug() << index << materialCode;
    ui->comboBox_3->addItem(materialCode + " | " + materialName);
}

void OneClickBurnWindow::on_lineEdit_8_returnPressed()
{
    QString businessOrderNo = ui->lineEdit_SAPCode->text();
    QString materialCode = ui->lineEdit_productMaterial->text();
    QString itemCode = ui->comboBox_3->currentText().mid(0, 10);
    QString actionType = "2";
    QString sn = ui->lineEdit_8->text();

    QString softwareFile = ui->comboBox_program->currentText();

    QStringList info;
    info << businessOrderNo << materialCode << itemCode << sn
         << actionType << softwareFile.mid(softwareFile.indexOf(" | ")+3);
    emit checkSnSignal(info);
}

void OneClickBurnWindow::on_lineEdit_proSNCode_returnPressed()
{
    QString projectSn = ui->lineEdit_proSNCode->text();
    emit getProjectInfoSiganl(projectSn);
}

void OneClickBurnWindow::on_btn_search_2_clicked()
{
    on_lineEdit_proSNCode_returnPressed();
}

void OneClickBurnWindow::on_comboBox_program_currentTextChanged(const QString &arg1)
{
    QString fileName = arg1.mid(arg1.indexOf(" | ")+3);
    emit selectBurnFileSignal(fileName);
}

void OneClickBurnWindow::updateGuiTableDisplay(QString & programName, int finishedCnt)
{
    for(int i = 0; i < ui->tableWidget->rowCount(); i++)
    {
        QTableWidgetItem* item = ui->tableWidget->item(i, 1);
        if(item->text() == programName)
        {
            item = ui->tableWidget->item(i, 2);
            item->setText(QString::number(finishedCnt));
        }
    }
}

void OneClickBurnWindow::on_pushButton_selct_clicked()
{
    emit selectFileSignal();
}

void OneClickBurnWindow::on_pushButton_secc_version_clicked()
{
    ui->lineEdit_secc_version->clear();
    emit startProcessSignal(8);
}

void OneClickBurnWindow::on_pushButton_evcc_version_clicked()
{
    ui->lineEdit_evcc_version->clear();
    emit startProcessSignal(6);
}

void OneClickBurnWindow::on_pushButton_attenuation_value_clicked()
{
    ui->lineEdit_attenuation_value->clear();
    emit startProcessSignal(9);
}

void OneClickBurnWindow::updateVersion(int type, QString &version)
{
    if(type == 8)
    {
        ui->lineEdit_secc_version->setText(version);
    }
    else
    {
        ui->lineEdit_evcc_version->setText(version);
    }
}

void OneClickBurnWindow::updateAttenuationValue(QString &value)
{
    ui->lineEdit_attenuation_value->setText(value);
}

void OneClickBurnWindow::updateEVCCStatus(bool sta)
{
    if(sta)
    {
        ui->lab_evccSta->setStyleSheet("background:green;border-radius:5px;");
    }
    else
    {
        ui->lab_evccSta->setStyleSheet("background:red;border-radius:5px;");
    }
}

void OneClickBurnWindow::updateSSHStatus(bool sta)
{
    if(sta)
    {
        ui->lab_sshSta->setStyleSheet("background:green;border-radius:5px;");
    }
    else
    {
        ui->lab_sshSta->setStyleSheet("background:red;border-radius:5px;");
    }
}

void OneClickBurnWindow::updateTCPStatus(bool sta)
{
    if(sta)
    {
        ui->lab_tcpSta->setStyleSheet("background:green;border-radius:5px;");
    }
    else
    {
        ui->lab_tcpSta->setStyleSheet("background:red;border-radius:5px;");
    }
}

void OneClickBurnWindow::updateProgressBar(int value)
{
    ui->progressBar->setValue(value);
}

void OneClickBurnWindow::on_pushButton_upgrade_clicked()
{
    ui->label_28->setText(tr("请点击按钮开始升级"));
    ui->textBrowser_log->clear();
    ui->progressBar->setValue(0);

    ui->pushButton_secc_version->setEnabled(false);
    ui->pushButton_evcc_version->setEnabled(false);
    ui->pushButton_attenuation_value->setEnabled(false);

    emit startUpgradeSignal();
}

void OneClickBurnWindow::updateLogMsg(const QString &step, const QString &log)
{
    if(!log.isEmpty())
    {
        ui->textBrowser_log->append(log);
    }

    if(!step.isEmpty())
    {
        ui->label_27->setText(step);
    }
}

void OneClickBurnWindow::upgradeFinished(bool result,const QString &ctx)
{
    int index = ui->stackedWidget->currentIndex();
    if(index == 4)
    {
        ui->pushButton_upgrade_2->setEnabled(true);
        ui->pushButton_upgrade_2->setText(tr("点击升级"));
    }
    else
    {
        restEnv();

        if(result)
        {
            initWindow();
        }
        else
        {
            ui->label_28->setText(ctx);
        }
    }
}

void OneClickBurnWindow::restEnv()
{
    ui->pushButton_secc_version->setEnabled(true);
    ui->pushButton_evcc_version->setEnabled(true);
    ui->pushButton_attenuation_value->setEnabled(true);
}

void OneClickBurnWindow::on_pushButton_selct_2_released()
{
    QString fileName = QFileDialog::getOpenFileName(this, tr("Open File"),
                                                    "/home",
                                                    tr("All Files (*.bin)"));
    if(fileName.isEmpty())
    {
        return;
    }
    ui->lineEdit_firmware_path_2->setText(fileName);
    emit coreBoardFirmwareFileSginal(fileName);
}

void OneClickBurnWindow::processAuthResult(bool ret,int type)
{
    qDebug()<< "ret"<<ret;
    Q_UNUSED(type);
    if(ret)
    {
        ui->pushButton->setText(tr("身份正常"));
        ui->groupBox->setEnabled(true);
    }
    else
    {
       ui->groupBox->setEnabled(false);
       ui->pushButton->setText(tr("先点击身份认证"));
       ui->pushButton->setEnabled(true);
    }
}
void OneClickBurnWindow::on_pushButton_upgrade_2_released()
{
    ui->pushButton_upgrade_2->setEnabled(false);
    ui->pushButton_upgrade_2->setText(tr("升级中"));
    emit startUpgradeSignal();
}

void OneClickBurnWindow::on_pushButton_released()
{
    ui->pushButton->setEnabled(false);
    ui->pushButton->setText(tr("身份认证中"));
    emit identityAuthSingal();
}
void OneClickBurnWindow::displayNUwriteInfo(const QString & info)
{
    ui->label_32->setText(info);
}
