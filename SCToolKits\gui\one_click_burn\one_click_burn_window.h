﻿#ifndef ONE_CLICK_BURN_WINDOW_H
#define ONE_CLICK_BURN_WINDOW_H

#include <QMainWindow>

typedef enum
{
    LIGHT_BOARD = 1,
    UNIFIED_WRITE,
    SND_WRITE,
    UPGRADE_EVCC_PLC,
    A35_SECUR_NU_WRITER,
}FlashWriteType;

namespace Ui {
class OneClickBurnWindow;
}

class OneClickBurnWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit OneClickBurnWindow(QWidget *parent = nullptr);
    ~OneClickBurnWindow();

private:
    void initWindow();
    void unifiedBurnWindowInit();
protected:
    bool event(QEvent *event);
public slots:
    void updateSelectFileName(QString &);
    void updateUpgradeProgress(int);
    void updateJLinkWriteDataDisplay(QStringList & data);
    void updateJLinkWriteResult(bool status);
    void updateJLinkWriteProgress(const QString &, int, int);
    void switchGUIDisplay(FlashWriteType type);
    void updateWorkOrderInfoSlot(QString &, QString &);
    void updateBurnInfoSlot(int, QStringList &);
    void updateMaterialCodeInfoSlot(int, QString &, QString &);
    void updateGuiTableDisplay(QString &, int);

    //schneider
    void getFirmwareResultSlot(bool, QString &);
    void updateMesLogStatus(bool sta, QString);
    void checkStmQRCodeResult(bool res);
    void updateLogSlot(const QString &);
    void uploadMesUploadResult(bool result);

    //通过secc给evcc_plc升级
    void updateVersion(int, QString &);
    void updateAttenuationValue(QString &);
    void updateEVCCStatus(bool );
    void updateSSHStatus(bool );
    void updateTCPStatus(bool );
    void updateProgressBar(int );
    void updateLogMsg(const QString &, const QString &);
    void upgradeFinished(bool result,const QString &ctx);
    void restEnv();

    //
    void processAuthResult(bool ret,int type=0);
    void displayNUwriteInfo(const QString &);
private slots:
    void on_btn_select_clicked();
    void on_btn_start_clicked();
    void on_lampPlateAddress_spinBox_valueChanged(int arg1);
    void on_btn_startSNDBrun_clicked();
    void on_lineEdit_pcbQRCode_returnPressed();
    void on_lineEdit_stmQRCode_returnPressed();
    void on_lineEdit_SAPCode_returnPressed();
    void on_btn_search_clicked();
    void on_lineEdit_8_returnPressed();
    void on_lineEdit_proSNCode_returnPressed();
    void on_btn_search_2_clicked();
    void on_comboBox_program_currentTextChanged(const QString &arg1);
    void on_pushButton_selct_clicked();
    void on_pushButton_secc_version_clicked();
    void on_pushButton_evcc_version_clicked();
    void on_pushButton_attenuation_value_clicked();
    void on_pushButton_upgrade_clicked();

    void on_pushButton_selct_2_released();

    void on_pushButton_upgrade_2_released();

    void on_pushButton_released();

signals:
    void selectFileSignal();
    void startUpgradeSignal();
    void updateAddress(int);
    void startJLinkBurnSignal();
    void startSchneiderBurnSignal();

    void snCodeInputFinishedSignal(QString &);
    void stmCodeInputFinishedSignal(QString &);

    void getProjectIdInfo(QString &);  //sap单号查询工单
    void getProjectInfoSiganl(QString &); //产品sn获取产品信息
    void checkSnSignal(QStringList &);
    void selectBurnFileSignal(QString &);

    void startProcessSignal(int );

    void coreBoardFirmwareFileSginal(const QString & file);
    void identityAuthSingal();
private:
    Ui::OneClickBurnWindow *ui;

    FlashWriteType burnType;
};

#endif // ONE_CLICK_BURN_WINDOW_H
