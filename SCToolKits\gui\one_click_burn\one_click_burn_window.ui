<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OneClickBurnWindow</class>
 <widget class="QMainWindow" name="OneClickBurnWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1335</width>
    <height>888</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_5">
    <property name="leftMargin">
     <number>0</number>
    </property>
    <property name="topMargin">
     <number>0</number>
    </property>
    <property name="rightMargin">
     <number>0</number>
    </property>
    <property name="bottomMargin">
     <number>0</number>
    </property>
    <item row="0" column="0">
     <widget class="QStackedWidget" name="stackedWidget">
      <property name="currentIndex">
       <number>4</number>
      </property>
      <widget class="QWidget" name="page">
       <layout class="QGridLayout" name="gridLayout_4">
        <item row="0" column="0">
         <layout class="QGridLayout" name="gridLayout_3">
          <item row="0" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0,1">
            <item>
             <widget class="QLabel" name="label">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>11</pointsize>
               </font>
              </property>
              <property name="text">
               <string>灯板地址：</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QSpinBox" name="lampPlateAddress_spinBox">
              <property name="minimumSize">
               <size>
                <width>51</width>
                <height>28</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>15</pointsize>
               </font>
              </property>
              <property name="maximum">
               <number>15</number>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item row="1" column="0">
           <layout class="QGridLayout" name="gridLayout_2">
            <item row="0" column="0">
             <widget class="QPushButton" name="btn_select">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>60</height>
               </size>
              </property>
              <property name="text">
               <string>浏览文件</string>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLineEdit" name="lineEdit">
              <property name="minimumSize">
               <size>
                <width>22</width>
                <height>30</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="1" column="0">
             <widget class="QPushButton" name="btn_start">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>60</height>
               </size>
              </property>
              <property name="text">
               <string>开始升级</string>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="QProgressBar" name="upgrade_progressBar">
              <property name="minimumSize">
               <size>
                <width>22</width>
                <height>30</height>
               </size>
              </property>
              <property name="value">
               <number>24</number>
              </property>
              <property name="textVisible">
               <bool>false</bool>
              </property>
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="invertedAppearance">
               <bool>false</bool>
              </property>
              <property name="textDirection">
               <enum>QProgressBar::TopToBottom</enum>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item row="1" column="0">
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="page_2">
       <property name="styleSheet">
        <string notr="true">#page_2{
background-image: url(:/img/one_click_config/background .jpg);
}</string>
       </property>
       <layout class="QGridLayout" name="gridLayout">
        <property name="leftMargin">
         <number>100</number>
        </property>
        <property name="rightMargin">
         <number>100</number>
        </property>
        <item row="1" column="0">
         <widget class="QWidget" name="widget" native="true">
          <property name="minimumSize">
           <size>
            <width>1000</width>
            <height>180</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">#widget{
	background:rgba(108, 224, 255, 80%);
	border:1px solid black;
	border-radius:10px;
}</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_7">
           <item row="1" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_14">
             <property name="leftMargin">
              <number>130</number>
             </property>
             <property name="rightMargin">
              <number>80</number>
             </property>
             <item>
              <widget class="QLabel" name="label_12">
               <property name="maximumSize">
                <size>
                 <width>95</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">background:transparent;</string>
               </property>
               <property name="text">
                <string>烧录阶段：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_5">
               <property name="minimumSize">
                <size>
                 <width>600</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>35</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="0" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_12">
             <item>
              <widget class="QLabel" name="label_14">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>170</horstretch>
                 <verstretch>30</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>170</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>170</width>
                 <height>30</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">background:transparent;</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="pixmap">
                <pixmap resource="../../src.qrc">:/img/one_click_burn/label2.png</pixmap>
               </property>
               <property name="scaledContents">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_10">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item row="2" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_15">
             <property name="leftMargin">
              <number>130</number>
             </property>
             <property name="rightMargin">
              <number>80</number>
             </property>
             <item>
              <widget class="QLabel" name="label_20">
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">background:transparent;</string>
               </property>
               <property name="text">
                <string>烧录进度：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QProgressBar" name="progressBar_2">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">QProgressBar{
	border-radius:15px;
	border:1px solid #E8EDF2;
	background-color: rgb(225, 225, 225);
	border-color: rgb(180, 180, 180);
}
QProgressBar:chunk{
	border-radius:14px;
	background-color:rgb(146, 255, 204);
	color: rgb(255, 255, 0);
}
</string>
               </property>
               <property name="value">
                <number>0</number>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QWidget" name="widget_2" native="true">
          <property name="minimumSize">
           <size>
            <width>1000</width>
            <height>650</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>800</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">#widget_2{
	background:rgba(108, 224, 255, 80%);
	border:1px solid black;
	border-radius:10px;
}</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_6">
           <item row="0" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_8">
             <item>
              <widget class="QLabel" name="label_13">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>170</horstretch>
                 <verstretch>30</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>170</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>170</width>
                 <height>30</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>12</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">background:transparent;</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="pixmap">
                <pixmap resource="../../src.qrc">:/img/one_click_burn/label1.png</pixmap>
               </property>
               <property name="scaledContents">
                <bool>false</bool>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_3">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item row="4" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_18">
             <item>
              <widget class="QLabel" name="label_18">
               <property name="minimumSize">
                <size>
                 <width>130</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>130</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>微软雅黑</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">background:transparent;</string>
               </property>
               <property name="text">
                <string>选择烧录程序：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QComboBox" name="comboBox_program">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>35</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <pointsize>15</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">border: 1px solid black;</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="6" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_9">
             <item>
              <widget class="QLabel" name="label_10">
               <property name="minimumSize">
                <size>
                 <width>130</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>95</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>微软雅黑</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">background:transparent;</string>
               </property>
               <property name="text">
                <string>处理结果：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_11">
               <property name="minimumSize">
                <size>
                 <width>500</width>
                 <height>60</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>60</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">background:green;
color:white;</string>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="2" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_11">
             <property name="spacing">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="label_3">
               <property name="minimumSize">
                <size>
                 <width>112</width>
                 <height>0</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>微软雅黑</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">background:transparent;</string>
               </property>
               <property name="text">
                <string>产品代码：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEdit_productMaterial">
               <property name="minimumSize">
                <size>
                 <width>420</width>
                 <height>35</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">border-radius:4px;
border:1px solid black;</string>
               </property>
               <property name="placeholderText">
                <string>自动加载</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_2">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Fixed</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>100</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="label_4">
               <property name="minimumSize">
                <size>
                 <width>112</width>
                 <height>0</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>微软雅黑</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">background:transparent;</string>
               </property>
               <property name="text">
                <string>产品名称：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEdit_productName">
               <property name="minimumSize">
                <size>
                 <width>420</width>
                 <height>35</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">border-radius:4px;
border:1px solid black;</string>
               </property>
               <property name="placeholderText">
                <string>自动加载</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="3" column="0">
            <widget class="QTableWidget" name="tableWidget">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>170</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>等线</family>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">QTableWidget { 
	border: 1px solid black;
	border-radius:3px;
}
QHeaderView::section{
   border-top:0px solid #E5E5E5;
   border-left:0px solid #E5E5E5;
   border-right:0.5px solid #E5E5E5;
   border-bottom: 0.5px solid #E5E5E5;
   background-color:white;
   padding:3px;
}</string>
             </property>
             <property name="autoScroll">
              <bool>false</bool>
             </property>
             <property name="editTriggers">
              <set>QAbstractItemView::NoEditTriggers</set>
             </property>
             <property name="selectionMode">
              <enum>QAbstractItemView::NoSelection</enum>
             </property>
             <property name="showGrid">
              <bool>true</bool>
             </property>
             <property name="sortingEnabled">
              <bool>false</bool>
             </property>
             <property name="wordWrap">
              <bool>false</bool>
             </property>
             <property name="rowCount">
              <number>1</number>
             </property>
             <property name="columnCount">
              <number>4</number>
             </property>
             <attribute name="horizontalHeaderVisible">
              <bool>false</bool>
             </attribute>
             <attribute name="horizontalHeaderCascadingSectionResizes">
              <bool>false</bool>
             </attribute>
             <attribute name="horizontalHeaderMinimumSectionSize">
              <number>30</number>
             </attribute>
             <attribute name="horizontalHeaderDefaultSectionSize">
              <number>200</number>
             </attribute>
             <attribute name="horizontalHeaderHighlightSections">
              <bool>true</bool>
             </attribute>
             <attribute name="horizontalHeaderShowSortIndicator" stdset="0">
              <bool>false</bool>
             </attribute>
             <attribute name="horizontalHeaderStretchLastSection">
              <bool>true</bool>
             </attribute>
             <attribute name="verticalHeaderVisible">
              <bool>false</bool>
             </attribute>
             <attribute name="verticalHeaderCascadingSectionResizes">
              <bool>false</bool>
             </attribute>
             <attribute name="verticalHeaderDefaultSectionSize">
              <number>36</number>
             </attribute>
             <attribute name="verticalHeaderHighlightSections">
              <bool>true</bool>
             </attribute>
             <row/>
             <column>
              <property name="text">
               <string>程序文件</string>
              </property>
             </column>
             <column>
              <property name="text">
               <string>                                                              程序名称                                                </string>
              </property>
             </column>
             <column>
              <property name="text">
               <string>已烧录数量</string>
              </property>
             </column>
             <column>
              <property name="text">
               <string>工单数</string>
              </property>
             </column>
            </widget>
           </item>
           <item row="5" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_22">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_6">
               <item>
                <widget class="QLabel" name="label_8">
                 <property name="minimumSize">
                  <size>
                   <width>130</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>微软雅黑</family>
                   <pointsize>14</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background:transparent;</string>
                 </property>
                 <property name="text">
                  <string>管控料号：</string>
                 </property>
                </widget>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_3">
                 <property name="spacing">
                  <number>0</number>
                 </property>
                 <item>
                  <widget class="QComboBox" name="comboBox_3">
                   <property name="minimumSize">
                    <size>
                     <width>420</width>
                     <height>35</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <pointsize>15</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">border: 1px solid black;</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="horizontalSpacer_4">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Fixed</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>100</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_7">
               <item>
                <widget class="QLabel" name="label_9">
                 <property name="font">
                  <font>
                   <family>微软雅黑</family>
                   <pointsize>14</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background:transparent;</string>
                 </property>
                 <property name="text">
                  <string>物料SN：</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="lineEdit_8">
                 <property name="minimumSize">
                  <size>
                   <width>420</width>
                   <height>35</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>等线</family>
                   <pointsize>15</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">border-radius:4px;
border:1px solid black;</string>
                 </property>
                 <property name="placeholderText">
                  <string>请输入物料SN</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
           <item row="1" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_5">
             <property name="spacing">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="label_2">
               <property name="font">
                <font>
                 <family>微软雅黑</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">background:transparent;</string>
               </property>
               <property name="text">
                <string>SAP工单号：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEdit_SAPCode">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>400</width>
                 <height>35</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">border:1px solid black;
border-right:none;</string>
               </property>
               <property name="placeholderText">
                <string>请输入SAP工单号</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btn_search">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>20</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">#btn_search{
	border:1px solid black;
	border-left:none;
	background:white;
}
#btn_search:hover{
	border:1px solid rgb(179, 179, 179);
}
</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="icon">
                <iconset resource="../../src.qrc">
                 <normaloff>:/img/one_click_burn/search.png</normaloff>:/img/one_click_burn/search.png</iconset>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_5">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Fixed</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>100</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="label_22">
               <property name="minimumSize">
                <size>
                 <width>112</width>
                 <height>0</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>微软雅黑</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">background:transparent;</string>
               </property>
               <property name="text">
                <string>产品SN号：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lineEdit_proSNCode">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>400</width>
                 <height>35</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">border:1px solid black;
border-right:none;</string>
               </property>
               <property name="placeholderText">
                <string>请输入产品SN（维修区）</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btn_search_2">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>35</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">#btn_search_2{
	border:1px solid black;
	border-left:none;
	background:white;
}
#btn_search_2:hover{
	border:1px solid rgb(179, 179, 179);
}
</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="icon">
                <iconset resource="../../src.qrc">
                 <normaloff>:/img/one_click_burn/search.png</normaloff>:/img/one_click_burn/search.png</iconset>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="page_3">
       <layout class="QGridLayout" name="gridLayout_8">
        <item row="0" column="0">
         <widget class="QWidget" name="widget_4" native="true">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>168</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>200</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">#widget_4{
	background:rgb(240, 249, 255);
}</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_10">
           <item row="5" column="1">
            <layout class="QHBoxLayout" name="horizontalLayout_10">
             <property name="spacing">
              <number>0</number>
             </property>
             <item>
              <widget class="QLineEdit" name="lineEdit_firmwarePath">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>12</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">border:1px solid black;
border-right:none;
background:rgb(255, 255, 255);</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="placeholderText">
                <string>等待获取固件路径</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btn_search_file_2">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>30</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">#btn_search_file_2{
	border:1px solid black;
	border-left:none;
	background:rgb(255, 255, 255);
}
#btn_search_file_2:hover{
	border:1px solid rgb(179, 179, 179);
}
</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="icon">
                <iconset resource="../../src.qrc">
                 <normaloff>:/img/one_click_burn/search.png</normaloff>:/img/one_click_burn/search.png</iconset>
               </property>
               <property name="flat">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_16">
             <property name="font">
              <font>
               <family>等线</family>
               <pointsize>16</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">background:transparent;</string>
             </property>
             <property name="text">
              <string>PCB二维码        ：</string>
             </property>
            </widget>
           </item>
           <item row="5" column="0">
            <widget class="QLabel" name="label_19">
             <property name="font">
              <font>
               <family>等线</family>
               <pointsize>16</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">background:transparent;</string>
             </property>
             <property name="text">
              <string>固件地址          ：</string>
             </property>
            </widget>
           </item>
           <item row="6" column="0">
            <spacer name="verticalSpacer_13">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="2" column="0">
            <spacer name="verticalSpacer_15">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="3" column="0">
            <widget class="QLabel" name="label_17">
             <property name="font">
              <font>
               <family>等线</family>
               <pointsize>16</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">background:transparent;</string>
             </property>
             <property name="text">
              <string>核心板二维码   ：</string>
             </property>
            </widget>
           </item>
           <item row="4" column="0">
            <spacer name="verticalSpacer_14">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="lineEdit_pcbQRCode">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>35</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>等线</family>
               <pointsize>14</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">background:rgb(255, 255, 255);
</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="placeholderText">
              <string>请先登录MES</string>
             </property>
            </widget>
           </item>
           <item row="3" column="1">
            <widget class="QLineEdit" name="lineEdit_stmQRCode">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>35</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>等线</family>
               <pointsize>14</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">background:rgb(255, 255, 255);
</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="placeholderText">
              <string>请先扫描PCB二维码</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <spacer name="verticalSpacer_16">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QWidget" name="widget_5" native="true">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>250</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">
background:rgba(229, 224, 255, 90%);
</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_9">
           <item row="8" column="0">
            <spacer name="verticalSpacer_9">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="3" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_20">
             <property name="leftMargin">
              <number>50</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="label_23">
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">background:transparent;</string>
               </property>
               <property name="text">
                <string>烧录进度：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QProgressBar" name="progressBar_3">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>35</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">QProgressBar{
	border-radius:15px;
	border:1px solid #E8EDF2;
	background-color: rgb(225, 225, 225);
	border-color: rgb(180, 180, 180);
}
QProgressBar:chunk{
	border-radius:14px;
	background-color:rgb(146, 255, 204);
	color: rgb(255, 255, 0);
}
</string>
               </property>
               <property name="value">
                <number>0</number>
               </property>
               <property name="alignment">
                <set>Qt::AlignCenter</set>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="7" column="0">
            <widget class="QTextBrowser" name="textBrowser">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>220</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">background:white;
border:1px solid black;</string>
             </property>
            </widget>
           </item>
           <item row="6" column="0">
            <spacer name="verticalSpacer_10">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="5" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_21">
             <property name="leftMargin">
              <number>50</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="label_24">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>38</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">background:transparent;</string>
               </property>
               <property name="text">
                <string>烧录阶段：</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_25">
               <property name="minimumSize">
                <size>
                 <width>300</width>
                 <height>38</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>16</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color:rgb(26, 137, 255)</string>
               </property>
               <property name="text">
                <string>等待烧录</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_name_3">
               <property name="minimumSize">
                <size>
                 <width>150</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>35</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">background:transparent;</string>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item row="0" column="0">
            <spacer name="verticalSpacer_8">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="4" column="0">
            <spacer name="verticalSpacer_11">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item row="1" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_16">
             <property name="leftMargin">
              <number>50</number>
             </property>
             <item>
              <widget class="QPushButton" name="btn_startSNDBrun">
               <property name="minimumSize">
                <size>
                 <width>170</width>
                 <height>50</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>14</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">#btn_startSNDBrun{
	background:rgb(255, 188, 166);
	border-radius:10px;
}
#btn_startSNDBrun:hover{
	border:1px solid black;
}</string>
               </property>
               <property name="text">
                <string>开始烧录</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_17">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="label_status_2">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>60</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>15</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">background:transparent;</string>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_18">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_17">
               <item>
                <widget class="QLabel" name="label_uploadMes_2">
                 <property name="font">
                  <font>
                   <family>等线</family>
                   <pointsize>14</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background:transparent;</string>
                 </property>
                 <property name="text">
                  <string>MES上传失败，点击手动上传-&gt;&gt;</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QPushButton" name="btn_uploadSNDMes">
                 <property name="minimumSize">
                  <size>
                   <width>170</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>等线</family>
                   <pointsize>14</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#btn_uploadSNDMes{
background:rgb(255, 188, 166);
border-radius:10px;
}
#btn_uploadSNDMes:hover{
border:1px solid black;
}</string>
                 </property>
                 <property name="text">
                  <string>上传MES</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="horizontalSpacer_19">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item row="2" column="0">
            <spacer name="verticalSpacer_12">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="page_4">
       <property name="styleSheet">
        <string notr="true">#page_4{ 
background-image: url(:/img/one_click_config/background .jpg);
}</string>
       </property>
       <layout class="QGridLayout" name="gridLayout_12">
        <item row="0" column="0">
         <widget class="QWidget" name="widget_3" native="true">
          <property name="maximumSize">
           <size>
            <width>1224</width>
            <height>760</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true">#widget_3{
	background:rgba(0, 255, 255, 50%);
	border:1px solid black;
	border-radius:10px;
}</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_11">
           <item row="10" column="0" colspan="2">
            <layout class="QVBoxLayout" name="verticalLayout_5">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_26">
               <item>
                <widget class="QLabel" name="label_6">
                 <property name="minimumSize">
                  <size>
                   <width>170</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>170</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>楷体</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background:transparent;</string>
                 </property>
                 <property name="text">
                  <string>阶段</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_27">
                 <property name="minimumSize">
                  <size>
                   <width>340</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>楷体</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background:transparent;</string>
                 </property>
                 <property name="text">
                  <string>--&gt; 请点击按钮开始升级 &lt;--</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_27">
               <item>
                <widget class="QLabel" name="label_26">
                 <property name="minimumSize">
                  <size>
                   <width>170</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>170</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>楷体</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background:transparent;</string>
                 </property>
                 <property name="text">
                  <string>结果</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_28">
                 <property name="minimumSize">
                  <size>
                   <width>340</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>楷体</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background:transparent;</string>
                 </property>
                 <property name="text">
                  <string>请点击按钮开始升级</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
           <item row="7" column="0" colspan="2">
            <layout class="QVBoxLayout" name="verticalLayout_4">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_2">
               <property name="spacing">
                <number>6</number>
               </property>
               <item>
                <widget class="QPushButton" name="pushButton_selct">
                 <property name="minimumSize">
                  <size>
                   <width>170</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>楷体</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#pushButton_selct{
	background:rgba(255, 193, 148, 200);
	border-radius:10px;
}
#pushButton_selct:hover{
	border:1px solid black;
}</string>
                 </property>
                 <property name="text">
                  <string>选择固件</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="lineEdit_firmware_path">
                 <property name="minimumSize">
                  <size>
                   <width>1024</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>楷体</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">border: 1px solid black;
border-radius:10px;</string>
                 </property>
                 <property name="placeholderText">
                  <string>固件包路径</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_4">
               <item>
                <widget class="QPushButton" name="pushButton_upgrade">
                 <property name="minimumSize">
                  <size>
                   <width>170</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>楷体</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#pushButton_upgrade{
	background:rgba(255, 193, 148, 200);
	border-radius:10px;
}
#pushButton_upgrade:hover{
	border:1px solid black;
}</string>
                 </property>
                 <property name="text">
                  <string>开始升级</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QProgressBar" name="progressBar">
                 <property name="minimumSize">
                  <size>
                   <width>1024</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>楷体</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">QProgressBar{
	border-radius:15px;
	border:1px solid #E8EDF2;
	background-color: rgb(225, 225, 225);
	border-color: rgb(180, 180, 180);
}
QProgressBar:chunk{
	border-radius:14px;
	background-color:rgb(146, 255, 204);
	color: rgb(255, 255, 0);
}
</string>
                 </property>
                 <property name="value">
                  <number>100</number>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                 <property name="format">
                  <string>升级进度 %p%</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
           <item row="8" column="0" colspan="2">
            <widget class="QTextBrowser" name="textBrowser_log">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>200</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>楷体</family>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">border: 1px solid black;
background-color:rgb(213,233,243);
border-radius:10px;</string>
             </property>
            </widget>
           </item>
           <item row="4" column="0" colspan="2">
            <widget class="Line" name="line_5">
             <property name="styleSheet">
              <string notr="true">background:transparent;</string>
             </property>
             <property name="frameShadow">
              <enum>QFrame::Plain</enum>
             </property>
             <property name="lineWidth">
              <number>1</number>
             </property>
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item row="2" column="0" colspan="2">
            <widget class="Line" name="line_2">
             <property name="styleSheet">
              <string notr="true">background:transparent;</string>
             </property>
             <property name="frameShadow">
              <enum>QFrame::Plain</enum>
             </property>
             <property name="lineWidth">
              <number>1</number>
             </property>
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item row="9" column="0">
            <widget class="Line" name="line">
             <property name="styleSheet">
              <string notr="true">background:transparent;</string>
             </property>
             <property name="frameShadow">
              <enum>QFrame::Plain</enum>
             </property>
             <property name="lineWidth">
              <number>1</number>
             </property>
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_25">
             <item>
              <widget class="QLabel" name="label_logo">
               <property name="minimumSize">
                <size>
                 <width>210</width>
                 <height>40</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">image: url(:/img/light_color/starCharge_LOGO.png);
background:transparent;</string>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_8">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item row="3" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_24">
             <item>
              <layout class="QVBoxLayout" name="verticalLayout">
               <item>
                <widget class="QPushButton" name="pushButton_secc_version">
                 <property name="minimumSize">
                  <size>
                   <width>170</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>楷体</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#pushButton_secc_version{
	background:rgba(255, 193, 148, 200);
	border-radius:10px;
}
#pushButton_secc_version:hover{
	border:1px solid black;
}</string>
                 </property>
                 <property name="text">
                  <string>获取SECC版本</string>
                 </property>
                </widget>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_13">
                 <item>
                  <widget class="QLabel" name="label_7">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>楷体</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">background:transparent;</string>
                   </property>
                   <property name="text">
                    <string>SECC版本</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QLineEdit" name="lineEdit_secc_version">
                   <property name="minimumSize">
                    <size>
                     <width>250</width>
                     <height>40</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>楷体</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">border: 1px solid black;
border-radius:10px;</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="horizontalSpacer_6">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_2">
               <item>
                <widget class="QPushButton" name="pushButton_evcc_version">
                 <property name="minimumSize">
                  <size>
                   <width>170</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>楷体</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#pushButton_evcc_version{
	background:rgba(255, 193, 148, 200);
	border-radius:10px;
}
#pushButton_evcc_version:hover{
	border:1px solid black;
}</string>
                 </property>
                 <property name="text">
                  <string>获取EVCC版本</string>
                 </property>
                </widget>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_19">
                 <item>
                  <widget class="QLabel" name="label_15">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>楷体</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">background:transparent;</string>
                   </property>
                   <property name="text">
                    <string>EVCC版本</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QLineEdit" name="lineEdit_evcc_version">
                   <property name="minimumSize">
                    <size>
                     <width>250</width>
                     <height>40</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>楷体</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">border: 1px solid black;
border-radius:10px;</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="horizontalSpacer_7">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_3">
               <item>
                <widget class="QPushButton" name="pushButton_attenuation_value">
                 <property name="minimumSize">
                  <size>
                   <width>170</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>楷体</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">#pushButton_attenuation_value{
	background:rgba(255, 193, 148, 200);
	border-radius:10px;
}
#pushButton_attenuation_value:hover{
	border:1px solid black;
}</string>
                 </property>
                 <property name="text">
                  <string>获取衰减值</string>
                 </property>
                </widget>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_23">
                 <item>
                  <widget class="QLabel" name="label_21">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>楷体</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">background:transparent;</string>
                   </property>
                   <property name="text">
                    <string>衰减值</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QLineEdit" name="lineEdit_attenuation_value">
                   <property name="minimumSize">
                    <size>
                     <width>250</width>
                     <height>40</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>楷体</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">border: 1px solid black;
border-radius:10px;</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </item>
            </layout>
           </item>
           <item row="1" column="0">
            <layout class="QHBoxLayout" name="horizontalLayout_29">
             <item>
              <spacer name="horizontalSpacer_13">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_6">
               <property name="spacing">
                <number>0</number>
               </property>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_28">
                 <item>
                  <widget class="QLabel" name="lab_evccSta">
                   <property name="minimumSize">
                    <size>
                     <width>16</width>
                     <height>16</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>16</width>
                     <height>16</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">border-radius:5px;background:rgb(213, 213, 213);</string>
                   </property>
                   <property name="text">
                    <string/>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="QLabel" name="label_29">
                 <property name="minimumSize">
                  <size>
                   <width>70</width>
                   <height>15</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>70</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>楷体</family>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background:transparent;</string>
                 </property>
                 <property name="text">
                  <string>EVCC状态</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="horizontalSpacer_11">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_7">
               <property name="spacing">
                <number>0</number>
               </property>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_30">
                 <item>
                  <widget class="QLabel" name="lab_tcpSta">
                   <property name="minimumSize">
                    <size>
                     <width>16</width>
                     <height>16</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>16</width>
                     <height>16</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">border-radius:5px;background:rgb(213, 213, 213);</string>
                   </property>
                   <property name="text">
                    <string/>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="QLabel" name="label_30">
                 <property name="minimumSize">
                  <size>
                   <width>70</width>
                   <height>15</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>70</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>楷体</family>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background:transparent;</string>
                 </property>
                 <property name="text">
                  <string>TCP状态</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="horizontalSpacer_12">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <layout class="QVBoxLayout" name="verticalLayout_8">
               <property name="spacing">
                <number>0</number>
               </property>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_31">
                 <item>
                  <widget class="QLabel" name="lab_sshSta">
                   <property name="minimumSize">
                    <size>
                     <width>16</width>
                     <height>16</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>16</width>
                     <height>16</height>
                    </size>
                   </property>
                   <property name="styleSheet">
                    <string notr="true">border-radius:5px;background:rgb(213, 213, 213);</string>
                   </property>
                   <property name="text">
                    <string/>
                   </property>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <widget class="QLabel" name="label_31">
                 <property name="minimumSize">
                  <size>
                   <width>70</width>
                   <height>15</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>70</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>楷体</family>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">background:transparent;</string>
                 </property>
                 <property name="text">
                  <string>SSH状态</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <spacer name="horizontalSpacer_9">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="page_5">
       <widget class="QPushButton" name="pushButton">
        <property name="geometry">
         <rect>
          <x>40</x>
          <y>20</y>
          <width>150</width>
          <height>150</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>150</width>
          <height>150</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton:pressed
{
  background-color: rgb(255, 0, 0);
}

QPushButton{
	background-color: rgb(0, 255, 255);
}

QPushButton:pressed{

	background-color: rgb(255, 0, 0);
}</string>
        </property>
        <property name="text">
         <string>先点击身份认证</string>
        </property>
       </widget>
       <widget class="QLabel" name="label_32">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>310</x>
          <y>30</y>
          <width>611</width>
          <height>150</height>
         </rect>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>150</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Arial</family>
          <pointsize>16</pointsize>
         </font>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
       <widget class="QGroupBox" name="groupBox">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="geometry">
         <rect>
          <x>20</x>
          <y>190</y>
          <width>1271</width>
          <height>261</height>
         </rect>
        </property>
        <property name="title">
         <string>烧录</string>
        </property>
        <property name="checkable">
         <bool>false</bool>
        </property>
        <widget class="QWidget" name="layoutWidget">
         <property name="geometry">
          <rect>
           <x>20</x>
           <y>30</y>
           <width>1223</width>
           <height>170</height>
          </rect>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_9">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_33">
            <item>
             <widget class="QComboBox" name="comboBox">
              <property name="minimumSize">
               <size>
                <width>170</width>
                <height>50</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">background-color: rgb(118, 118, 118);</string>
              </property>
              <item>
               <property name="text">
                <string>256M设备</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>128M设备</string>
               </property>
              </item>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_15">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>1008</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_32">
            <item>
             <widget class="QPushButton" name="pushButton_selct_2">
              <property name="minimumSize">
               <size>
                <width>170</width>
                <height>50</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>楷体</family>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton{
	background-color: rgb(0, 255, 255);
}

QPushButton:pressed{

	background-color: rgb(255, 0, 0);
}</string>
              </property>
              <property name="text">
               <string>选择固件</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_14">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>13</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit_firmware_path_2">
              <property name="minimumSize">
               <size>
                <width>1024</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>楷体</family>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">border: 1px solid black;
border-radius:10px;</string>
              </property>
              <property name="placeholderText">
               <string>固件包路径</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_34">
            <item>
             <widget class="QPushButton" name="pushButton_upgrade_2">
              <property name="minimumSize">
               <size>
                <width>170</width>
                <height>50</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>楷体</family>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton{
	background-color: rgb(0, 255, 255);
}

QPushButton:pressed{

	background-color: rgb(255, 0, 0);
}</string>
              </property>
              <property name="text">
               <string>点击升级</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_16">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>1038</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QProgressBar" name="progressBar_4">
              <property name="minimumSize">
               <size>
                <width>1024</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>楷体</family>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">QProgressBar{
	border-radius:15px;
	border:1px solid #E8EDF2;
	background-color: rgb(225, 225, 225);
	border-color: rgb(180, 180, 180);
}
QProgressBar:chunk{
	border-radius:14px;
	background-color:rgb(146, 255, 204);
	color: rgb(255, 255, 0);
}
</string>
              </property>
              <property name="value">
               <number>0</number>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="format">
               <string>升级进度 %p%</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
       </widget>
       <zorder>groupBox</zorder>
       <zorder>pushButton</zorder>
       <zorder>label_32</zorder>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources>
  <include location="../../src.qrc"/>
 </resources>
 <connections/>
</ui>
