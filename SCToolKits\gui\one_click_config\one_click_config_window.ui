<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OneClickConfigWindow</class>
 <widget class="QMainWindow" name="OneClickConfigWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1214</width>
    <height>818</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <pointsize>13</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-image: url(:/img/one_click_config/background .jpg);
</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout">
    <item row="0" column="0">
     <widget class="QWidget" name="widget" native="true">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>125</height>
       </size>
      </property>
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>125</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">background:rgba(145, 145, 145, 80%);

border-radius: 20px;</string>
      </property>
      <layout class="QGridLayout" name="gridLayout_3">
       <property name="topMargin">
        <number>35</number>
       </property>
       <item row="0" column="1">
        <layout class="QVBoxLayout" name="verticalLayout">
         <item>
          <widget class="QPushButton" name="btn_startCfg">
           <property name="minimumSize">
            <size>
             <width>200</width>
             <height>50</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>200</width>
             <height>16777215</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>等线</family>
             <pointsize>13</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">#btn_startCfg{
	background:rgb(255, 209, 152);
	border-radius:10px;
}
#btn_startCfg:hover{
	border:1px solid rgb(193, 232, 255);
}</string>
           </property>
           <property name="text">
            <string>一键配置</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_4">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item row="0" column="2">
        <spacer name="horizontalSpacer_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="0" column="4">
        <spacer name="horizontalSpacer_12">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="0" column="3">
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <item>
            <widget class="QLabel" name="label_2">
             <property name="minimumSize">
              <size>
               <width>110</width>
               <height>40</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>110</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>微软雅黑</family>
               <pointsize>15</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">color:white;
background:transparent;</string>
             </property>
             <property name="text">
              <string>配置进度</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QProgressBar" name="progressBar">
             <property name="minimumSize">
              <size>
               <width>400</width>
               <height>40</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>500</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="font">
              <font>
               <pointsize>13</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">QProgressBar{
	border-radius:20px;
	border:1px solid #E8EDF2;
	background-color: rgb(225, 225, 225);
	border-color: rgb(180, 180, 180);
}
QProgressBar:chunk{
	border-radius:19px;
	background-color:rgb(146, 255, 204);
	color: rgb(255, 255, 0);
}
</string>
             </property>
             <property name="value">
              <number>0</number>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="verticalSpacer_3">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item row="0" column="0">
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item row="0" column="5">
        <layout class="QVBoxLayout" name="verticalLayout_3">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_6">
           <item>
            <spacer name="horizontalSpacer_2">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QLabel" name="lab_cfgStatus">
             <property name="minimumSize">
              <size>
               <width>165</width>
               <height>0</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>微软雅黑</family>
               <pointsize>15</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">color:white;
background:transparent;</string>
             </property>
             <property name="text">
              <string>等待配置</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_9">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_5">
           <item>
            <spacer name="horizontalSpacer_11">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QLabel" name="lab_finishNum">
             <property name="font">
              <font>
               <family>等线</family>
               <pointsize>11</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">color:white;
background:transparent;</string>
             </property>
             <property name="text">
              <string>累计配置完成数：0</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </item>
       <item row="0" column="6">
        <spacer name="horizontalSpacer_13">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </item>
    <item row="1" column="0">
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <spacer name="horizontalSpacer_7">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QStackedWidget" name="stackedWidget">
        <property name="minimumSize">
         <size>
          <width>1000</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>1000</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="font">
         <font>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="autoFillBackground">
         <bool>false</bool>
        </property>
        <property name="styleSheet">
         <string notr="true">background:rgba(0, 0, 0, 40%);
border-radius: 20px;</string>
        </property>
        <property name="currentIndex">
         <number>4</number>
        </property>
        <widget class="QWidget" name="page">
         <property name="styleSheet">
          <string notr="true">background-color: rgba(0, 0, 0, 50%);
border-radius: 20px;</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_2">
          <property name="topMargin">
           <number>55</number>
          </property>
          <property name="bottomMargin">
           <number>83</number>
          </property>
          <item row="4" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <item>
             <spacer name="horizontalSpacer_10">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QLabel" name="label_8">
              <property name="font">
               <font>
                <family>等线</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">#label_8{
	background:transparent;
	color:white;
}</string>
              </property>
              <property name="text">
               <string>MES上传失败，请点击按钮继续上传&gt;&gt;</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="btn_mesUpload">
              <property name="minimumSize">
               <size>
                <width>150</width>
                <height>40</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>等线</family>
                <pointsize>14</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">#btn_mesUpload{
	color:white;
	background:rgba(129, 152, 162, 50%);
	border:1px solid rgb(0, 0, 0);
	border-radius:10px;
}
#btn_mesUpload:hover{
	border:1px solid rgb(193, 232, 255);
}</string>
              </property>
              <property name="text">
               <string>上传MES</string>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item row="1" column="0">
           <spacer name="verticalSpacer_2">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeType">
             <enum>QSizePolicy::Maximum</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="3" column="0">
           <spacer name="verticalSpacer">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="2" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <item>
             <spacer name="horizontalSpacer_4">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QLabel" name="label">
              <property name="font">
               <font>
                <family>楷体</family>
                <pointsize>20</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color:white;
background:transparent;</string>
              </property>
              <property name="text">
               <string>桩ID</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_5">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QLineEdit" name="lineEdit_winCode">
              <property name="minimumSize">
               <size>
                <width>550</width>
                <height>50</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>700</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>华文中宋</family>
                <pointsize>17</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">border:1px solid rgb(192, 194, 189);
border-radius:15px;
color:white;</string>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="cursorPosition">
               <number>0</number>
              </property>
              <property name="placeholderText">
               <string>请输入桩ID</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_6">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item row="0" column="0">
           <widget class="QGroupBox" name="groupBox">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>200</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>200</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>等线</family>
              <pointsize>13</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color:white;
background:transparent;</string>
            </property>
            <property name="title">
             <string>使用说明：</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_4">
             <property name="leftMargin">
              <number>45</number>
             </property>
             <property name="topMargin">
              <number>25</number>
             </property>
             <item row="0" column="0">
              <widget class="QLabel" name="label_3">
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string>1、连接SSH，通信配置--&gt;设置参数--&gt;连接</string>
               </property>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QLabel" name="label_4">
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string>2、MES登录，MES配置--&gt;配置参数--&gt;登录</string>
               </property>
              </widget>
             </item>
             <item row="2" column="0">
              <widget class="QLabel" name="label_5">
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string>3、输入SN码，扫码枪扫入，或手动输入</string>
               </property>
              </widget>
             </item>
             <item row="3" column="0">
              <widget class="QLabel" name="label_6">
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string>4、输入桩ID，扫码枪扫入，或手动输入</string>
               </property>
              </widget>
             </item>
             <item row="4" column="0">
              <widget class="QLabel" name="label_7">
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string>5、开始配置，扫码输入后自动开始配置、或输入完成回车、或点击一键配置按钮</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="page_2">
         <property name="styleSheet">
          <string notr="true">background-color: rgba(0, 0, 0, 50%);
border-radius: 20px;</string>
         </property>
         <layout class="QGridLayout" name="gridLayout_5">
          <item row="0" column="0">
           <layout class="QVBoxLayout" name="verticalLayout_4">
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_7">
              <item>
               <widget class="QComboBox" name="comboBox">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>250</width>
                  <height>40</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>100</width>
                  <height>16777215</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>楷体</family>
                  <pointsize>14</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">background-color: rgb(255, 255, 255);</string>
                </property>
                <item>
                 <property name="text">
                  <string>请选择配置类型</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>生产配置</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>出厂配置</string>
                 </property>
                </item>
                <item>
                 <property name="text">
                  <string>国标配置</string>
                 </property>
                </item>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_14">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="verticalSpacer_7">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_9">
              <item>
               <spacer name="horizontalSpacer_15">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QLabel" name="label_14">
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>楷体</family>
                  <pointsize>20</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:white;
background:transparent;</string>
                </property>
                <property name="text">
                 <string>SN号</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_starchargePileCode">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>680</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>700</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>华文中宋</family>
                  <pointsize>17</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">border:1px solid rgb(0, 0, 0);
border-radius:15px;
color:white;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="cursorPosition">
                 <number>0</number>
                </property>
                <property name="placeholderText">
                 <string>桩SN号</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_18">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="verticalSpacer_5">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_8">
              <item>
               <spacer name="horizontalSpacer_16">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QLabel" name="label_15">
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>楷体</family>
                  <pointsize>20</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:white;
background:transparent;</string>
                </property>
                <property name="text">
                 <string>桩号</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_pileCode">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>680</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>700</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>华文中宋</family>
                  <pointsize>17</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">border:1px solid rgb(0, 0, 0);
border-radius:15px;
color:white;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="cursorPosition">
                 <number>0</number>
                </property>
                <property name="placeholderText">
                 <string>请扫描桩号</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_19">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="verticalSpacer_15">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_17">
              <item>
               <spacer name="horizontalSpacer_28">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QLabel" name="label_22">
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>楷体</family>
                  <pointsize>20</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:white;
background:transparent;</string>
                </property>
                <property name="text">
                 <string>配置文件名</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_configName">
                <property name="enabled">
                 <bool>true</bool>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>680</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>700</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>华文中宋</family>
                  <pointsize>17</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">border:1px solid rgb(0, 0, 0);
border-radius:15px;
color:white;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="cursorPosition">
                 <number>0</number>
                </property>
                <property name="placeholderText">
                 <string>配置文件名称</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_29">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="verticalSpacer_6">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_10">
              <item>
               <spacer name="horizontalSpacer_17">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QLabel" name="label_16">
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>楷体</family>
                  <pointsize>20</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:white;
background:transparent;</string>
                </property>
                <property name="text">
                 <string>客户二维码</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_consumerQRCode">
                <property name="minimumSize">
                 <size>
                  <width>700</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>700</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>华文中宋</family>
                  <pointsize>17</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">border:1px solid rgb(0, 0, 0);
border-radius:15px;
color:white;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="cursorPosition">
                 <number>0</number>
                </property>
                <property name="placeholderText">
                 <string>请扫描客户二维码</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_20">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="verticalSpacer_16">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_23">
              <item>
               <spacer name="horizontalSpacer_30">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QLabel" name="label_27">
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>楷体</family>
                  <pointsize>20</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:white;
background:transparent;</string>
                </property>
                <property name="text">
                 <string>WIFI名称</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_name">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>700</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>700</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>华文中宋</family>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">border:1px solid rgb(0, 0, 0);
border-radius:15px;
color:white;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="cursorPosition">
                 <number>0</number>
                </property>
                <property name="placeholderText">
                 <string>WIFI名称</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_33">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="verticalSpacer_17">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_22">
              <item>
               <spacer name="horizontalSpacer_32">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QLabel" name="label_26">
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>楷体</family>
                  <pointsize>20</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:white;
background:transparent;</string>
                </property>
                <property name="text">
                 <string>WIFI密码</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_pwd">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>700</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>700</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>华文中宋</family>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">border:1px solid rgb(0, 0, 0);
border-radius:15px;
color:white;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="cursorPosition">
                 <number>0</number>
                </property>
                <property name="placeholderText">
                 <string>WIFI密码</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_34">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="verticalSpacer_18">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_11">
              <item>
               <spacer name="horizontalSpacer_36">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="QLabel" name="label_17">
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>楷体</family>
                  <pointsize>20</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:white;
background:transparent;</string>
                </property>
                <property name="text">
                 <string>PIN码</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_pinCode">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>700</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>700</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>华文中宋</family>
                  <pointsize>12</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">border:1px solid rgb(0, 0, 0);
border-radius:15px;
color:white;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="cursorPosition">
                 <number>0</number>
                </property>
                <property name="placeholderText">
                 <string>当前桩的PIN码</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_38">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="verticalSpacer_9">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <spacer name="verticalSpacer_8">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="page_3">
         <layout class="QGridLayout" name="gridLayout_7">
          <item row="0" column="0">
           <widget class="QGroupBox" name="groupBox_3">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>150</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>200</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>等线</family>
              <pointsize>16</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color:white;
background:transparent;</string>
            </property>
            <property name="title">
             <string>使用说明：</string>
            </property>
            <layout class="QGridLayout" name="gridLayout_6">
             <property name="leftMargin">
              <number>45</number>
             </property>
             <property name="topMargin">
              <number>25</number>
             </property>
             <item row="1" column="0">
              <widget class="QLabel" name="label_33">
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>16</pointsize>
                 <weight>50</weight>
                 <bold>false</bold>
                </font>
               </property>
               <property name="text">
                <string>2、开始配置，扫码输入后自动开始配置、或输入完成回车、或点击一键配置按钮</string>
               </property>
              </widget>
             </item>
             <item row="0" column="0">
              <widget class="QLabel" name="label_31">
               <property name="font">
                <font>
                 <family>等线</family>
                 <pointsize>16</pointsize>
                 <weight>50</weight>
                 <bold>false</bold>
                </font>
               </property>
               <property name="text">
                <string>1、输入SN码，扫码枪扫入，或手动输入</string>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item row="1" column="0">
           <layout class="QVBoxLayout" name="verticalLayout_5">
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_19">
              <item>
               <widget class="QLabel" name="label_23">
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Times New Roman</family>
                  <pointsize>20</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:white;
background:transparent;</string>
                </property>
                <property name="text">
                 <string>SN号</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_bmw_sn">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>550</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>700</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>华文中宋</family>
                  <pointsize>17</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">border:1px solid rgb(0, 0, 0);
border-radius:15px;
color:white;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="cursorPosition">
                 <number>0</number>
                </property>
                <property name="placeholderText">
                 <string>桩SN号</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_31">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="verticalSpacer_13">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_26">
              <item>
               <widget class="QLabel" name="label_30">
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Times New Roman</family>
                  <pointsize>20</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:white;
background:transparent;</string>
                </property>
                <property name="text">
                 <string>EVSEID</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_bmw_evseid">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>550</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>700</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>华文中宋</family>
                  <pointsize>17</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">border:1px solid rgb(0, 0, 0);
border-radius:15px;
color:white;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="cursorPosition">
                 <number>0</number>
                </property>
                <property name="placeholderText">
                 <string>EVSEID</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_37">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="verticalSpacer_14">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>40</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_21">
              <item>
               <widget class="QLabel" name="label_25">
                <property name="minimumSize">
                 <size>
                  <width>200</width>
                  <height>50</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Times New Roman</family>
                  <pointsize>20</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">color:white;
background:transparent;</string>
                </property>
                <property name="text">
                 <string>MACADDR</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignCenter</set>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLineEdit" name="lineEdit_bmw_mac">
                <property name="enabled">
                 <bool>false</bool>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>550</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>700</width>
                  <height>60</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>华文中宋</family>
                  <pointsize>17</pointsize>
                 </font>
                </property>
                <property name="styleSheet">
                 <string notr="true">border:1px solid rgb(0, 0, 0);
border-radius:15px;
color:white;</string>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="cursorPosition">
                 <number>0</number>
                </property>
                <property name="placeholderText">
                 <string>MACADDR</string>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_35">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="page_4">
         <layout class="QGridLayout" name="gridLayout_8">
          <item row="0" column="0">
           <layout class="QHBoxLayout" name="horizontalLayout_14">
            <item>
             <widget class="QTextEdit" name="textEdit"/>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_6">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_13">
                <item>
                 <widget class="QLabel" name="label_9">
                  <property name="font">
                   <font>
                    <family>等线 Light</family>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color: rgb(255, 255, 255);</string>
                  </property>
                  <property name="text">
                   <string>ISO15118使能</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QRadioButton" name="radioButton">
                  <property name="text">
                   <string/>
                  </property>
                  <property name="checked">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_12">
                <item>
                 <widget class="QLabel" name="label_18">
                  <property name="text">
                   <string>ISO15118使能</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QTextEdit" name="textEdit_2"/>
                </item>
               </layout>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="page_5">
         <widget class="QWidget" name="layoutWidget">
          <property name="geometry">
           <rect>
            <x>130</x>
            <y>300</y>
            <width>708</width>
            <height>52</height>
           </rect>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_15">
           <item>
            <widget class="QLabel" name="label_10">
             <property name="minimumSize">
              <size>
               <width>120</width>
               <height>0</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>黑体</family>
               <pointsize>14</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">color:white;
background:transparent;</string>
             </property>
             <property name="text">
              <string>设备二维码</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="lineEdit">
             <property name="minimumSize">
              <size>
               <width>550</width>
               <height>50</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>Times New Roman</family>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">color:white;
background:transparent;</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="placeholderText">
              <string>请扫描客户二维码</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QGroupBox" name="groupBox_5">
          <property name="geometry">
           <rect>
            <x>10</x>
            <y>20</y>
            <width>982</width>
            <height>191</height>
           </rect>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>150</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>200</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>等线</family>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">color:white;
background:transparent;</string>
          </property>
          <property name="title">
           <string>使用说明：</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_10">
           <property name="leftMargin">
            <number>45</number>
           </property>
           <property name="topMargin">
            <number>25</number>
           </property>
           <item row="0" column="0">
            <widget class="QLabel" name="label_35">
             <property name="font">
              <font>
               <family>等线</family>
               <pointsize>14</pointsize>
               <weight>50</weight>
               <bold>false</bold>
              </font>
             </property>
             <property name="text">
              <string>1、标准产品请按提示直接开始测试，施耐德系列产品请参考说明2、3进行操作</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="label_36">
             <property name="font">
              <font>
               <family>等线</family>
               <pointsize>14</pointsize>
               <weight>50</weight>
               <bold>false</bold>
              </font>
             </property>
             <property name="text">
              <string>2、测试前，请先在web界面打开SSH服务</string>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="label_37">
             <property name="font">
              <font>
               <family>等线</family>
               <pointsize>14</pointsize>
               <weight>50</weight>
               <bold>false</bold>
              </font>
             </property>
             <property name="text">
              <string>3、若设备在配置期间重启，则需重新在web界面打开SSH服务</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QLabel" name="label_19">
          <property name="geometry">
           <rect>
            <x>50</x>
            <y>230</y>
            <width>851</width>
            <height>41</height>
           </rect>
          </property>
          <property name="font">
           <font>
            <family>Microsoft YaHei</family>
            <pointsize>-1</pointsize>
            <weight>75</weight>
            <italic>false</italic>
            <bold>true</bold>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">color: #4DFFB8;
background: transparent;
font: bold 25px 'Microsoft YaHei';
padding: 4px;</string>
          </property>
          <property name="text">
           <string>请在右上角框内输入SN号或料号！</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </widget>
        <widget class="QWidget" name="page_6">
         <widget class="QWidget" name="layoutWidget_2">
          <property name="geometry">
           <rect>
            <x>60</x>
            <y>320</y>
            <width>864</width>
            <height>52</height>
           </rect>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_16">
           <item>
            <widget class="QLabel" name="label_11">
             <property name="minimumSize">
              <size>
               <width>120</width>
               <height>0</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>黑体</family>
               <pointsize>16</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">color:white;
background:transparent;</string>
             </property>
             <property name="text">
              <string>工装号：</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="lineEdit_2">
             <property name="minimumSize">
              <size>
               <width>550</width>
               <height>50</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>黑体</family>
               <pointsize>16</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">color:white;
background:transparent;</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="placeholderText">
              <string>请扫描或输入工装号</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_22">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>10</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="btn_clear">
             <property name="minimumSize">
              <size>
               <width>150</width>
               <height>40</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>等线</family>
               <pointsize>14</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">#btn_clear{
	color:white;
	background:rgba(129, 152, 162, 50%);
	border:1px solid rgb(0, 0, 0);
	border-radius:10px;
}
#btn_clear:hover{
	border:1px solid rgb(193, 232, 255);
}</string>
             </property>
             <property name="text">
              <string>清除工装号</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_21">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
         <widget class="QGroupBox" name="groupBox_4">
          <property name="geometry">
           <rect>
            <x>60</x>
            <y>100</y>
            <width>901</width>
            <height>150</height>
           </rect>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>150</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>200</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>等线</family>
            <pointsize>16</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">color:white;
background:transparent;</string>
          </property>
          <property name="title">
           <string>使用说明：</string>
          </property>
          <layout class="QGridLayout" name="gridLayout_9">
           <property name="leftMargin">
            <number>45</number>
           </property>
           <property name="topMargin">
            <number>25</number>
           </property>
           <item row="1" column="0">
            <widget class="QLabel" name="label_34">
             <property name="font">
              <font>
               <family>等线</family>
               <pointsize>16</pointsize>
               <weight>50</weight>
               <bold>false</bold>
              </font>
             </property>
             <property name="text">
              <string>2、扫描SN号后自动开始上传</string>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="label_32">
             <property name="font">
              <font>
               <family>等线</family>
               <pointsize>16</pointsize>
               <weight>50</weight>
               <bold>false</bold>
              </font>
             </property>
             <property name="text">
              <string>1、输入工装号，扫码枪扫入或手动输入并回车</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
         <widget class="QLabel" name="label_12">
          <property name="geometry">
           <rect>
            <x>320</x>
            <y>260</y>
            <width>321</width>
            <height>51</height>
           </rect>
          </property>
          <property name="styleSheet">
           <string notr="true">color: #4DFFB8;
background: transparent;
font: bold 25px 'Microsoft YaHei';
padding: 4px;</string>
          </property>
          <property name="text">
           <string>请先扫描或输入工装号！</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
         <widget class="QLabel" name="label_13">
          <property name="geometry">
           <rect>
            <x>170</x>
            <y>10</y>
            <width>661</width>
            <height>71</height>
           </rect>
          </property>
          <property name="font">
           <font>
            <family>Microsoft YaHei</family>
            <pointsize>-1</pointsize>
            <weight>75</weight>
            <italic>false</italic>
            <bold>true</bold>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">color: #FFFF99;
background: transparent;
font: bold 36px 'Microsoft YaHei';
padding: 4px;</string>
          </property>
          <property name="text">
           <string>FCT测试工具（简易工装）</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
         </widget>
        </widget>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_8">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
