#include "single_board_check_window.h"
#include "ui_single_board_check_window.h"

SingleBoardCheckWindow::SingleBoardCheckWindow(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::SingleBoardCheckWindow),currentTestName(""),curentTestStatus(0),testPassNum(0),seriallinkSta(false),
    mesLoginSta(false),tipWindowStatus(false),isPassWithSn(false),mesUploadStatus(false),isTesting(false)
{
    ui->setupUi(this);

    ui->tableWidget->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    ui->tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    ui->tableWidget->setShowGrid(false);

    ui->btn_choice->show();
    ui->btn_retest->hide();
    tipWindowClear();

    initWindow();
}

SingleBoardCheckWindow::~SingleBoardCheckWindow()
{
    delete ui;
}

void SingleBoardCheckWindow::updateDisplaySlot(TipContext &testCtx)
{
    updateTipWindow(testCtx.objectNameList);
    if(isAddTableItems(testCtx.curOperateIndex, testCtx.testStatus))
    {
        addTestObject(testCtx.currentTextName, testCtx.curObjIndex);
    }
    if(isUpdateTableDisplay(testCtx.curOperateIndex, testCtx.operateCnt, testCtx.testStatus))
    {
        updateTestObjectProgress(testCtx.objectCount, testCtx.curObjIndex);
        updateTestObjectResult(testCtx.currentTextName, testCtx.curObjIndex, testCtx.testStatus);
    }
    updateTestNumber(testCtx.objectCount, testCtx.curObjIndex);
    updateTestOperateProgress(testCtx.operateCnt, testCtx.curOperateIndex, testCtx.testStatus);
}

bool SingleBoardCheckWindow::isAddTableItems(int operateIndex, int testStatus)
{
    if((operateIndex == 0) && (testStatus == TESTING))
    {
            return true;
    }
    return false;
}

bool SingleBoardCheckWindow::isUpdateTableDisplay(int operateIndex, int operateCnt, int result)
{
    if(result != TESTING && (operateIndex+1 == operateCnt || result == TEST_FAILED))
    {
            return true;
    }
    return false;
}

void SingleBoardCheckWindow::updateTestObjectProgress(int count, int index)
{
    if(count != 0)
    {
        ui->progressBar->setValue((index + 1) * 100 / count);
    }
}

void SingleBoardCheckWindow::updateTestNumber(int countNum, int testedNum)
{
    ui->lab_caseNum->setNum(countNum);
    ui->lab_testedNum->setNum(testedNum + 1);
    ui->lab_testPassNum->setNum(testPassNum);
}

void SingleBoardCheckWindow::updateTestOperateProgress(int count, int index, int status)
{
    if(count == 0)
    {
        return;
    }
    if(status == TESTING)
    {
        isTesting = true;
        ui->btn_retest->show();
        ui->lab_testStatus->setText("正在测试");
    }
    else if(status == TEST_OK)
    {
        isTesting = false;
        index++;
        ui->lab_testStatus->setText("测试成功");
    }
    else if(status == TEST_FAILED)
    {
        isTesting = false;
        index++;
        ui->lab_testStatus->setText("测试失败");
    }
    ui->progressBar_2->setValue(index * 100 / count);
}

void SingleBoardCheckWindow::addTestObject(QString &name, int index)
{
    currentTestName = name;
    curentTestStatus = TESTING;
    ui->lab_testObj->setText(currentTestName);
    QColor color;
    color.setRgb(240, 131, 0);

    ui->tableWidget->setRowCount(index + 1);
    QTableWidgetItem *testName =  new QTableWidgetItem(currentTestName);
    testName->setTextAlignment(Qt::AlignLeft);
    testName->setTextAlignment(Qt::AlignVCenter);
    testName->setForeground(color);
    ui->tableWidget->setItem(index, 0,testName);

    QTableWidgetItem *flagItem = new QTableWidgetItem(QString::fromUtf8("正在检测"));
    flagItem->setTextAlignment(Qt::AlignLeft);
    flagItem->setTextAlignment(Qt::AlignVCenter);
    flagItem->setForeground(color);
    ui->tableWidget->setItem(index,1,flagItem);

    QTableWidgetItem *resultItem = new QTableWidgetItem(QString::fromUtf8("测试中"));
    resultItem->setTextAlignment(Qt::AlignLeft);
    resultItem->setTextAlignment(Qt::AlignVCenter);
    resultItem->setForeground(color);
    QIcon icon;
    icon.addFile(QString::fromUtf8(":/img/single_board_check/q_wait.png"), QSize(), QIcon::Normal, QIcon::Off);
    resultItem->setIcon(icon);
    ui->tableWidget->setItem(index,2,resultItem);

    QString startTime = QDateTime::currentDateTime().toString("hh:mm:ss");
    QTableWidgetItem *startTimeItem = new QTableWidgetItem(startTime);
    flagItem->setTextAlignment(Qt::AlignLeft);
    flagItem->setTextAlignment(Qt::AlignVCenter);
    startTimeItem->setForeground(color);
    ui->tableWidget->setItem(index,3,startTimeItem);
}

void SingleBoardCheckWindow::updateTestObjectResult(QString &name, int index, int status)
{
    curentTestStatus = status;

    QString tableCurName = ui->tableWidget->item(index,0)->text();
    if(tableCurName == name)
    {
        QColor color;
        QIcon icon;
        QString type;
        QString time;
        for(int i = 0; i < ui->tableWidget->columnCount(); i++)
        {
            QTableWidgetItem* itemColor = ui->tableWidget->item(index, i);
            if(status == 1)
            {
                color.setRgb(82, 196, 27);
                if(i == 1)
                {
                    itemColor->setText(QString::fromUtf8("测试完成"));
                }
                else if(i == 2)
                {
                    bool isLastCase = false;
                    if(ui->lab_caseNum->text().toInt() == index + 1)
                    {
                        isLastCase = true;
                    }
                    type = "pass";
                    time = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
                    setTipWindow(index+2, type, isLastCase, time);
                    itemColor->setText(QString::fromUtf8("通过"));
                    icon.addFile(QString::fromUtf8(":/img/single_board_check/q_pass.png"), QSize(), QIcon::Normal, QIcon::Off);
                    icon.addFile(QString::fromUtf8(":/img/single_board_check/q_pass.png"), QSize(), QIcon::Active, QIcon::On);
                    itemColor->setIcon(icon);
                }
            }
            else if(status == 2)
            {
                color.setRgb(227, 41, 40);
                if(i == 1)
                {
                    itemColor->setText(QString::fromUtf8("测试失败"));
                }
                else if(i == 2)
                {
                    type = "error";
                    time = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
                    setTipWindow(index+2, type, true, time);
                    itemColor->setText(QString::fromUtf8("不通过"));
                    icon.addFile(QString::fromUtf8(":/img/single_board_check/q_error.png"), QSize(), QIcon::Normal, QIcon::Off);
                    icon.addFile(QString::fromUtf8(":/img/single_board_check/q_error.png"), QSize(), QIcon::Active, QIcon::On);
                    itemColor->setIcon(icon);
                }
            }
            itemColor->setForeground(color);

        }
        if(status == 1)
        {
            testPassNum++;
        }
    }
}

// 函数重载，仅显示失败的测试结果，屏蔽右侧测试流程
void SingleBoardCheckWindow::updateTestObjectResult(QString &name, int index)
{
    QString tableCurName = ui->tableWidget->item(index,0)->text();

    if(tableCurName == name)
    {
        QColor color;
        QIcon icon;
        QString type;
        QString time;
        for(int i = 0; i < ui->tableWidget->columnCount(); i++)
        {
            QTableWidgetItem* itemColor = ui->tableWidget->item(index, i);

            color.setRgb(227, 41, 40);
            if(i == 1)
            {
                itemColor->setText(QString::fromUtf8("测试失败"));
            }
            else if(i == 2)
            {
                type = "error";
                time = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
                // setTipWindow(index+2, type, true, time);
                itemColor->setText(QString::fromUtf8("不通过"));
                icon.addFile(QString::fromUtf8(":/img/single_board_check/q_error.png"), QSize(), QIcon::Normal, QIcon::Off);
                icon.addFile(QString::fromUtf8(":/img/single_board_check/q_error.png"), QSize(), QIcon::Active, QIcon::On);
                itemColor->setIcon(icon);
            }

            itemColor->setForeground(color);

        }
    }
}

void SingleBoardCheckWindow::updateTestResultSlot(QMap<int, QString>& additionalTestInfo)
{
    qDebug() << "更新表格";

    // 遍历失败的测试任务
    QMapIterator<int, QString> iterator(additionalTestInfo);
    while (iterator.hasNext())
    {
        iterator.next();

        // 获取当前行数
        int currentRowCount = ui->tableWidget->rowCount();
        QString taskName = iterator.value();

        // 添加测试对象
        addTestObject(taskName, currentRowCount);

        // 仅显示测试失败的结果
        updateTestObjectResult(taskName, currentRowCount);
    }
}

void SingleBoardCheckWindow::updateTipWindow(QList<QString> &list)
{
    if(tipWindowStatus)
    {
        return;
    }
    int size = list.size();
    for(int i = 0; i < size; i++)
    {
        QString type = "init";
        setTipWindow(i+2, type, list[i]);
    }
    tipWindowStatus = true;
}

void SingleBoardCheckWindow::tipWindowClear()
{
    QString type = "init";
    QString name = "开始测试";
    setTipWindow(1, type, false, name);
    for(int i = 2; i <= 50; i++)
    {
        QLabel *label_q = ui->scrollAreaWidgetContents_7->findChild<QLabel *>("label_q_"+QString::number(i));
        label_q->clear();
        QLabel *label_tip = ui->scrollAreaWidgetContents_7->findChild<QLabel *>("label_tip_"+QString::number(i));
        label_tip->clear();
        QLabel *label_time = ui->scrollAreaWidgetContents_7->findChild<QLabel *>("label_tip_"+QString::number(i) + "_time");
        label_time->clear();
        if(i <= 49)
        {
            QLabel *label_line = ui->scrollAreaWidgetContents_7->findChild<QLabel *>("label_line_"+QString::number(i));
            label_line->close();
        }
    }
}

void SingleBoardCheckWindow::setTipWindow(int Number, QString &Icon, QString &tips)
{
    if(Number > 50)
    {
        return;
    }

    // 设置图标
    if(Icon == "error")
    {
        Icon = QString::fromUtf8(":/img/single_board_check/q_error.png");
    }else if(Icon == "init")
    {
        Icon = QString::fromUtf8(":/img/single_board_check/q_init.png");
    }else if(Icon == "pass")
    {
        Icon = QString::fromUtf8(":/img/single_board_check/q_pass.png");
    }else if(Icon == "wait")
    {
        Icon = QString::fromUtf8(":/img/single_board_check/q_wait.png");
    }else
    {
        return;
    }

    // 设置内容
    QLabel *label_q = ui->scrollAreaWidgetContents_7->findChild<QLabel *>("label_q_"+QString::number(Number));
    QLabel *label_tip = ui->scrollAreaWidgetContents_7->findChild<QLabel *>("label_tip_"+QString::number(Number));
    label_q->setPixmap(QPixmap(Icon));
    label_tip->setText(tips);
}

void SingleBoardCheckWindow::setTipWindow(int Number, QString &Icon, bool lastStatus, QString &Time)
{
    if(Number > 50)
    {
        return;
    }
    // 设置图标
    if(Icon == "error")
    {
        Icon = QString::fromUtf8(":/img/single_board_check/q_error.png");
    }else if(Icon == "init")
    {
        Icon = QString::fromUtf8(":/img/single_board_check/q_init.png");
    }else if(Icon == "pass")
    {
        Icon = QString::fromUtf8(":/img/single_board_check/q_pass.png");
    }else if(Icon == "wait")
    {
        Icon = QString::fromUtf8(":/img/single_board_check/q_wait.png");
    }else
    {
        return;
    }

    // 设置内容
    QLabel *label_q1 = ui->scrollAreaWidgetContents_7->findChild<QLabel *>("label_q_" + QString::number(Number));
    QLabel *label_time = ui->scrollAreaWidgetContents_7->findChild<QLabel *>("label_tip_" + QString::number(Number) + "_time");
    label_q1->setPixmap(QPixmap(Icon));
    if(!lastStatus && Number < 50)
    {
        QLabel *label_q2 = ui->scrollAreaWidgetContents_7->findChild<QLabel *>("label_q_" + QString::number(Number + 1));
        QLabel *label_line = ui->scrollAreaWidgetContents_7->findChild<QLabel *>("label_line_"+QString::number(Number));
        label_line->show();
        label_q2->setPixmap(QPixmap(QString::fromUtf8(":/img/single_board_check/q_wait.png")));
    }
    label_time->setText(Time);
}

void SingleBoardCheckWindow::setTipWindowProgressValue(int value)
{
    ui->scrollArea_7->verticalScrollBar()->setValue(value);
}


void SingleBoardCheckWindow::on_btn_startTest_clicked()
{
    getMesLoginStatus(mesLoginSta);

//    if(!mesLoginSta)
//    {
//        tipWindow("请先登录MES！", MODELESS, OK, WARNING);
//        return;
//    }
//    if(!isPassWithSn)
//    {
//        tipWindow("当前输入的SN码错误！", MODAL, OK, TIPS);
//        return;
//    }

    ui->btn_choice->hide();
    emit startTestSignal();
    QString type = "pass";
    QString time = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    setTipWindow(1, type, false, time);
}

void SingleBoardCheckWindow::getSerialPortLinkStatus(bool &sta)
{
    LinkManager *linkManager = LinkManager::get();
    sta = linkManager->getSerialLinkedStatus();
}

void SingleBoardCheckWindow::getMesLoginStatus(bool &sta)
{
    MesManager *mesManager = MesManager::get();
    sta = mesManager->isMesLogined();
}

void SingleBoardCheckWindow::updateSnCodeResultSlot(bool status)
{
    //这里!status位测试用
    if(status)
    {
        isPassWithSn = true;
    }
    else
    {
        isPassWithSn = false;
    }
}

void SingleBoardCheckWindow::updateMesUploadResultSlot(bool status, QString ctx)
{
    //这里!status位测试用
    mesUploadStatus = status;
    if(status)
    {
        initWindow();
    }
    else
    {
        ui->btn_uploadMES->show();
        tipWindow(ctx, MODAL, OK, TIPS);
    }
}

void SingleBoardCheckWindow::on_btn_retest_clicked()
{
    if(isTesting)
    {
        tipWindow("正在测试中，请等待测试完成！", MODELESS, OK, TIPS);
        return;
    }
    initWindow();
    emit retestSignal();
}

void SingleBoardCheckWindow::initWindow()
{
    tipWindowStatus = false;
    tipWindowClear();
    ui->btn_retest->hide();
    ui->btn_uploadMES->hide();
    ui->tableWidget->clearContents();
    ui->progressBar->setValue(0);
    ui->progressBar_2->setValue(0);
    ui->lab_caseNum->setText("0");
    ui->lab_testedNum->setText("0");
    ui->lab_testPassNum->setText("0");
    testPassNum = 0;
}

void SingleBoardCheckWindow::tipWindow(QString ctx, MSG_MODE mode, MSG_TYPE msgType, MSG_TIP_TYPE tipType)
{
    TipWindow tipWindow(ctx, this, mode, msgType, tipType);
    tipWindow.exec();
}

void SingleBoardCheckWindow::on_btn_uploadMES_clicked()
{
    emit uploadMESByHumanSignal();
}

void SingleBoardCheckWindow::on_btn_choice_clicked()
{
    emit showChoiceWindowSignal();
}

void SingleBoardCheckWindow::updateTestWindowDisplay(BoardType type)
{
    switch(type)
    {
        case AM62:
        {
            ui->btn_choice->hide();
        }
        break;
        case AC_RELAY_BOARD:
        {
            ui->btn_choice->show();
        }
        break;
    }
}
