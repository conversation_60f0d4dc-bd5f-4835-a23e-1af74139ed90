#ifndef SINGLE_BOARD_CHECK_WINDOW_H
#define SINGLE_BOARD_CHECK_WINDOW_H

#include <QMainWindow>
#include "communication/link_manager.h"
#include "mes/mes_manager.h"
#include "gui/tip_window.h"
#include "workers/flow_test_worker.h"
#include <QTimer>
#include <QScrollBar>

typedef enum
{
    AM62 = 1,
    AC_RELAY_BOARD
}BoardType;

namespace Ui {
class SingleBoardCheckWindow;
}

class SingleBoardCheckWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit
    SingleBoardCheckWindow(QWidget *parent = nullptr);
    ~SingleBoardCheckWindow();

public slots:
    void updateDisplaySlot(TipContext &);

    void updateSnCodeResultSlot(bool);
    void updateMesUploadResultSlot(bool,QString);
    void updateTestWindowDisplay(BoardType type);
    void updateTestResultSlot(QMap<int, QString>& additionalTestInfo);
private slots:
    void on_btn_startTest_clicked();
    void getSerialPortLinkStatus(bool &);
    void getMesLoginStatus(bool &);

    void addTestObject(QString &, int);
    void updateTestObjectResult(QString &, int, int);
    void updateTestObjectResult(QString &, int);                           // 重载函数，固定失败状态的界面显示
    void updateTestObjectProgress(int count, int index);
    void updateTestOperateProgress(int count, int index, int status);
    void updateTestNumber(int, int);
    void updateTipWindow(QList<QString> &);
    void tipWindowClear();
    void setTipWindow(int Number, QString &Icon, QString &tips);
    void setTipWindow(int Number, QString &Icon, bool lastStatus, QString &Time);
    void setTipWindowProgressValue(int value);

    bool isAddTableItems(int, int);
    bool isUpdateTableDisplay(int, int, int);
    void on_btn_retest_clicked();

    void on_btn_uploadMES_clicked();

    void on_btn_choice_clicked();

private:
    void tipWindow(QString, MSG_MODE, MSG_TYPE, MSG_TIP_TYPE);
    void initWindow();

signals:
    void startTestSignal();
    void retestSignal();
    void uploadMESByHumanSignal();
    void showChoiceWindowSignal();

private:
    Ui::SingleBoardCheckWindow *ui;

    QString currentTestName;
    int curentTestStatus;
    int testPassNum;
    bool seriallinkSta;
    bool mesLoginSta;
    bool tipWindowStatus;

    bool isPassWithSn;
    bool mesUploadStatus;
    bool isTesting;
};

#endif // SINGLE_BOARD_CHECK_WINDOW_H
