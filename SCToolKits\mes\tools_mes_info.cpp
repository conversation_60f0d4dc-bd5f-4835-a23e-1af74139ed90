#include "tools_mes_info.h"

ToolsMesInfo::ToolsMesInfo()
{
    // Test Env
    mesTestInfo =
    {
        {UNIFIED_FLASH_WRITE_E,{"https://test-mes-local-service.wbtz.cloud", "WB-S-Z-BURN-0001", MES_API_TYPE2}},
        {SCHNEIDER_PRINTER_E,{"https://test-mes-local-service.wbtz.cloud", "AC-SND-0003", MES_API_TYPE1}},
        {SCHNEIDER_NETWORK_CHECK_E, {"http://test-mes-local-service.wbtz.cloud",  "WB-S-J-SND-0006", MES_API_TYPE2}},
        {VIN_FAST_NETWORK_CHECK_E, {"http://test-mes-local-service.wbtz.cloud",  "WB-S-J-VFAST-0001", MES_API_TYPE2}},
        {HUIHONG_NETWORK_CHECK_E, {"http://test-mes-local-service.wbtz.cloud",  "WB-S-J-VFAST-0002", MES_API_TYPE2}},
        {MID_NETWORK_CHECK_E, {"http://test-mes-local-service.wbtz.cloud",  "WB-S-J-MID-0001", MES_API_TYPE2}},
        {EUROPEAN_MENISCUS_NETWORK_CHECK_E, {"https://test-mes-local-service.wbtz.cloud", "WB-S-J-VFAST-0002", MES_API_TYPE2}},
        {INDIAN_MENISCUS_NETWORK_CHECK_E, {"https://test-mes-local-service.wbtz.cloud", "WB-S-Z-REL-0002", MES_API_TYPE2}},
        {HUIHONG_USA_NETWORK_CHECK_E, {"https://test-mes-local-service.wbtz.cloud", "WB-S-J-VFAST-0002", MES_API_TYPE2}},
        {EUROPEAN_SAFE_NETWORK_CHECK_E, {"https://test-mes-local-service.wbtz.cloud", "WB-S-J-VFAST-0002", MES_API_TYPE2}},
        {SCHNEIDER_PRINTER_E, {"https://test-mes-local-service.wbtz.cloud", "AC-SND-0003", MES_API_TYPE2}},
        {PIN_PRINTER_E, {"https://test-mes-local-service.wbtz.cloud", "WB-S-J-PIN-0002", MES_API_TYPE2}},
        {PIN_CODE_GENNRATOR_E, {"https://test-mes-local-service.wbtz.cloud", "WB-S-J-PIN-0002", MES_API_TYPE2}},
        {ATLANTE_NETWORK_CONFIG_E, {"https://test-mes-local-service.wbtz.cloud", "WB-S-Z-0003", MES_API_TYPE2}},
        {METER_LOSS_SET_E, {"https://test-mes-local-service.wbtz.cloud", "WB-S-Z-QS-0001", MES_API_TYPE2}},
        {INTERNAL_DC_CONFIGE_E, {"https://test-mes-local-service.wbtz.cloud", "WB-S-Z-0004", MES_API_TYPE2}},
        {APN_SWITCH_OVER_E, {"http://test-mes-local-service.wbtz.cloud", "WB-S-J-0204", MES_API_TYPE2}},
        {ECC_MULTI_FUNCTIONAL_E, {"http://test-mes-local-service.wbtz.cloud", "WB-S-J-0204", MES_API_TYPE2}},
        {BMW_CONFIG_E, {"http://test-mes-local-service.wbtz.cloud", "WB-S-C-TT-00013", MES_API_TYPE2}},
        {EVCC_CHARGE_FCT_E, {"http://test-mes-local-service.wbtz.cloud", "TEST-CountingMachine", MES_API_TYPE2}},
        {DC_NETWORK_CHECK_E, {"https://test-mes-local-service.wbtz.cloud", "WB-S-Z-TX-0001", MES_API_TYPE2}},
        {UNIFY_COMMUNICATION_CHECK_E, {"https://test-mes-local-service.wbtz.cloud", "WB-S-J-VFAST-0002", MES_API_TYPE2}},
        {VIN_FAST_DC_CONFIG_E, {"https://test-mes-local-service.wbtz.cloud", "WB-S-Z-0004", MES_API_TYPE2}},
        {RKN_SECC_CHARGE_E, {"https://test-mes-local-service.wbtz.cloud", "WB-S-X-S-0100", MES_API_TYPE2}},
        {BESS_SECURE_CORRESPONDENCE_E, {"https://yc-test-mes-local-service.wbstar.com", "YC-S-J-GKJM", MES_API_TYPE2}}
    };

    // Release Env
    mesReleaseInfo =
    {
        {UNIFIED_FLASH_WRITE_E,{"https://mes-local-service.wbtz.cloud", "WB-S-Z-BURN-0001", MES_API_TYPE2}},
        {SCHNEIDER_FLASH_WRITE_E,{"http://172.16.1.20:9001", "TEST-SchneiderBurn", MES_API_TYPE1}},
        {SCHNEIDER_PRINTER_E,{"https://mes-local-service.wbtz.cloud", "AC-SND-0003", MES_API_TYPE1}},
        {SCHNEIDER_NETWORK_CHECK_E, {"http://mes-local-service.wbtz.cloud",  "WB-S-J-SND-0006", MES_API_TYPE2}},
        {VIN_FAST_NETWORK_CHECK_E, {"http://mes-local-service.wbtz.cloud",  "WB-S-J-VFAST-0001", MES_API_TYPE2}},
        {HUIHONG_NETWORK_CHECK_E, {"http://mes-local-service.wbtz.cloud",  "WB-S-J-HH-0002", MES_API_TYPE2}},
        {MID_NETWORK_CHECK_E, {"http://mes-local-service.wbtz.cloud",  "WB-S-J-MID-0001", MES_API_TYPE2}},
        {EUROPEAN_MENISCUS_NETWORK_CHECK_E, {"https://mes-local-service.wbtz.cloud", "WB-S-J-HH-0002", MES_API_TYPE2}},
        {INDIAN_MENISCUS_NETWORK_CHECK_E, {"https://mes-local-service.wbtz.cloud", "WB-S-Z-REL-0002", MES_API_TYPE2}},
        {US_WAN_YUE_CORE_BOARD_E, {"https://mes-local-service.wbtz.cloud", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {SCHNEIDER_CONFIG_E,{"http://172.16.1.20", "GC-AE-0174-C", MES_API_TYPE1}},
        {SCHNEIDER_FANCTORY_CONFIG_E,{"https://mes-local-service.wbtz.cloud", "AC-SND-0004", MES_API_TYPE1}},
        {HUIHONG_USA_NETWORK_CHECK_E, {"https://mes-local-service.wbtz.cloud", "WB-S-J-HH-0002", MES_API_TYPE2}},
        {EUROPEAN_SAFE_NETWORK_CHECK_E, {"https://mes-local-service.wbtz.cloud", "WB-S-J-HH-0002", MES_API_TYPE2}},
        {GB_ONE_KEY_CONFIGURE_E,{"https://mes-local-service.wbtz.cloud", "WB-S-Z-0002", MES_API_TYPE2}},
        {SCHNEIDER_PRINTER_E, {"https://mes-local-service.wbtz.cloud", "AC-SND-0003", MES_API_TYPE2}},
        {PIN_PRINTER_E, {"https://mes-local-service.wbstar.com", "WB-S-J-PIN-0002", MES_API_TYPE2}},
        {PIN_CODE_GENNRATOR_E, {"https://mes-local-service.wbstar.com", "WB-S-J-PIN-0002", MES_API_TYPE2}},
        {ENVIRONMENTAL_BOARD_E, {"https://mes-local-service.wbtz.cloud", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {LIQUID_COOL_BOARD_E, {"https://mes-local-service.wbtz.cloud", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {AM62_CORE_BOARD_E, {"https://mes-local-service.wbtz.cloud", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {ATLANTE_NETWORK_CONFIG_E, {"https://mes-local-service.wbtz.cloud", "WB-S-Z-0003", MES_API_TYPE2}},
        {METER_LOSS_SET_E, {"https://mes-local-service.wbtz.cloud", "WB-S-Z-QS-0001", MES_API_TYPE2}},
        {DC_CONTROL_BOTTOM_BOARD_E, {"https://mes-local-service.wbtz.cloud", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {DC_TOP_MAIN_BOARD_E, {"https://mes-local-service.wbtz.cloud", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {AC_METER_BOARD_VERIFY_E, {"https://mes-local-service.wbtz.cloud", "WB-S-Z-JB-0001", MES_API_TYPE2}},
        {SCHNEIDER_POWER_BOARD_E, {"https://mes-local-service.wbtz.cloud", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {SCHNEIDER_CTRL_BOARD_E, {"https://mes-local-service.wbtz.cloud", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {INTERNAL_DC_CONFIGE_E, {"https://mes-local-service.wbtz.cloud", "WB-S-Z-0004", MES_API_TYPE2}},
        {BLUETOOTH_BOARD_E, {"https://mes-local-service.wbtz.cloud", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {COMMON_IC_WRITER_E,{"https://mes-local-service.wbtz.cloud", "WB-S-J-0453", MES_API_TYPE2}},
        {APN_SWITCH_OVER_E, {"https://mes-local-service.wbstar.com", "WB-S-J-0204", MES_API_TYPE2}},
        {DC_LIGHT_BOARD_E, {"https://mes-local-service.wbstar.com", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {PDU_CONTROL_BOARD_E, {"https://mes-local-service.wbstar.com", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {SECC_BOARD_E, {"https://mes-local-service.wbtz.cloud", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {BMW_CONFIG_E, {"https://mes-local-service.wbtz.cloud", "WB-S-C-TT-00014", MES_API_TYPE2}},
        {EVCC_CHARGE_FCT_E, {"https://mes-local-service.wbtz.cloud", "WB-S-J-0509", MES_API_TYPE2}},
        {DC_NETWORK_CHECK_E, {"https://mes-local-service.wbstar.com", "WB-S-Z-TX-0001", MES_API_TYPE2}},
        {IONCHI_CCU_BOARD_E, {"https://mes-local-service.wbtz.cloud", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {XGB_CCU_BOARD_E, {"https://mes-local-service.wbtz.cloud", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {UNIFY_COMMUNICATION_CHECK_E, {"https://mes-local-service.wbtz.cloud", "WB-S-J-HH-0002", MES_API_TYPE2}},
        {TEST_TOOL_BARCODE_CONFIG_E, {"https://mes-local-service.wbstar.com", "WB-S-P-FCT-0003", MES_API_TYPE2}},
        {VIN_FAST_DC_CONFIG_E, {"https://mes-local-service.wbtz.cloud", "WB-S-Z-0004", MES_API_TYPE2}},
        {DPAU_CONTROL_BOARD_E, {"https://mes-local-service.wbtz.cloud", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {RKN_SECC_CHARGE_E, {"https://mes-local-service.wbtz.cloud", "WB-S-X-S-0100", MES_API_TYPE2}},
        {DC_PRE_CHARGE_BOARD_E, {"https://mes-local-service.wbtz.cloud", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {SCHNEIDER_NEW_RESI_CTRL_BOARD_E, {"https://mes-local-service.wbtz.cloud", "WB-S-P-FCT-0001", MES_API_TYPE2}},
        {BESS_SECURE_CORRESPONDENCE_E, {"http://yc-mes-local-service.wbstar.com", "YC-S-J-GKJM", MES_API_TYPE2}}
	};

}

void ToolsMesInfo::init()
{
    // 根据配置文件决定是什么环境
    QString env = APPConfig::get()->getStringValue("MESSERVER/env");
    if(env == "TEST")
    {
        mesInfo = mesTestInfo;
    }
    else
    {
        mesInfo = mesReleaseInfo;
    }
    mesWorkstions[MESWorkstionType::EVCC_CHARGE_TEST_1_E] =QString("EVCCGNCS1");
    mesWorkstions[MESWorkstionType::EVCC_CHARGE_TEST_2_E] =QString("EVCCGNCS2");
}
bool ToolsMesInfo::isSupportMesWorkstion(MESWorkstionType type)const
{
   auto iter =  mesWorkstions.find(type);
   if(iter == mesWorkstions.end())
   {
       return false;
   }
   return true;
}
QString ToolsMesInfo::getMesWorkstionName(MESWorkstionType type)const
{
    auto iter =  mesWorkstions.find(type);
    if(iter == mesWorkstions.end())
    {
        return "";
    }
    return iter.value();
}
void ToolsMesInfo::getCurrToolMesUrl(QString & url)
{
    ToolFunctionType type = ToolKitsManager::get()->getCurrentToolFunction();

    auto iter = mesInfo.find(type);
    if(iter != mesInfo.end())
    {
        url = iter.value().url;
    }
}
#include <QDebug>
void ToolsMesInfo::getCurrToolMesDeviceId(QString & deviceId)
{
    ToolFunctionType type = ToolKitsManager::get()->getCurrentToolFunction();

    auto iter = mesInfo.find(type);
    if(iter != mesInfo.end())
    {
        deviceId = iter.value().deviceId;
    }
}

void ToolsMesInfo::getCurrToolMesApiType(APIType & apiType)
{
    ToolFunctionType type = ToolKitsManager::get()->getCurrentToolFunction();

    auto iter = mesInfo.find(type);
    if(iter != mesInfo.end())
    {
        apiType = iter.value().type;
    }
}
