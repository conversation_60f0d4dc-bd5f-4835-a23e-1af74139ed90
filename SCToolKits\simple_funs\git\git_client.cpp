#include "git_client.h"
#include <QFile>

//xml配置用的路径
//#define PRODUCT_SOURCE_URL  "http://10.9.35.124/IntlProdDev/AppMarketMappingList.git"
static char PRODUCT_SOURCE_URL[512];
/*质保中心----提测用的配置url*/
//#define QAC_CONFIG_URL          "http://10.9.35.124/IntlProdDev/Fxxx_ParmConfig/"
static char CONFIG_URL[512];

//lcd执行路径
//#define LCD_URL             "http://10.9.35.124/IntlProdDev/Fxxx_QTLCD/"
static char LCD_URL[512];
//#define MES_LCD_URL             "http://10.9.35.124/IntlProdDev/Fxxx_QTLCD_Mes/"
//static char MES_LCD_URL[512];

static char USER[128];

static char PASS[128];

void initAppGitConfig()
{
//    QByteArray product_source_url = QByteArray(myApp::gitUrl.toUtf8()) + "gjyt/IntlProdDev/AppMarketMappingList.git";
    QByteArray product_source_url = + "gjyt/IntlProdDev/AppMarketMappingList.git";
    //QByteArray product_source_url = "http://10.9.35.124/gjyt/IntlProdDev/Fxxx_PersonalRepository/pengyu/AppMarketMappingList.git";
    snprintf(PRODUCT_SOURCE_URL, sizeof(PRODUCT_SOURCE_URL), "%s", product_source_url.data());

//    QByteArray config_url = QByteArray(myApp::gitUrl.toUtf8()) + "IntlProdDev/Fxxx_ParmConfig/";
    QByteArray config_url =  + "IntlProdDev/Fxxx_ParmConfig/";
    snprintf(CONFIG_URL, sizeof(CONFIG_URL), "%s", config_url.data());

//    QByteArray lcd_url = QByteArray(myApp::gitUrl.toUtf8()) + "IntlProdDev/Fxxx_QTLCD/";
    QByteArray lcd_url = + "IntlProdDev/Fxxx_QTLCD/";
    snprintf(LCD_URL, sizeof(LCD_URL), "%s", lcd_url.data());

//    snprintf(USER, sizeof(USER), "%s", myApp::gitUser.toLatin1().data());
//    snprintf(PASS, sizeof(PASS), "%s", myApp::gitPasswd.toLatin1().data());

}

int credentials_callback(git_cred **out, const char *url, const char *username_from_url, unsigned int allowed_types, void *payload)
{
    Q_UNUSED(url)
    Q_UNUSED(username_from_url)
    Q_UNUSED(allowed_types)
    Q_UNUSED(payload)
    return git_cred_userpass_plaintext_new(out,USER ,PASS);
}

static int transfer_progress_callback(const git_transfer_progress *stats, void *payload)
{
    Q_UNUSED(payload)

    if (stats->total_objects > 0) {
//        int percent = (100 * stats->received_objects) / stats->total_objects;
        //qInfo() << "Download progress: " << percent << "%";
//        myApp::m_progressBar->setValue(percent);
        //
        //m_progressBar->setValue(percent);
    }

    return 0;
}

static void checkout_progress_callback(const char *path, size_t completed_steps, size_t total_steps, void *payload)
{
    Q_UNUSED(path)
    Q_UNUSED(payload)

//    int progress = (completed_steps * 100) / total_steps;

//    myApp::m_progressBar->setValue(progress);
    //qDebug("Checkout progress: %d%%\n", progress);
}

GitClient::GitClient(const QString &account,const QString &pwd,const QString &path,const QString & url,QObject *parent )
                    : QObject(parent),
                      gitAccount(account),
                      gitPassword(pwd),
                      gitPath(path),
                      gitURL(url)
{
    snprintf(USER, sizeof(USER), "%s", gitAccount.toLatin1().data());
    snprintf(PASS, sizeof(PASS), "%s", gitPassword.toLatin1().data());
    //initAppGitConfig();
}

GitClient::~GitClient()
{

}

void GitClient::setGitURL(const QString & url)
{
    gitURL = url;
    return;
}
void GitClient::setGitPath(const QString &path)
{
    gitPath = path;
    return;
}
void GitClient::setGitUserInfo(const QString & account,const QString & pwd)
{
    gitAccount = account;
    gitPassword = pwd;
    return;
}

int GitClient::gitClone(const char* url, const char* local_path,const QString & checkoutBranch)
{
    git_libgit2_init();
    int ret = 0;
    git_repository* repo = nullptr;
    int error = git_repository_open(&repo, local_path);
    if (error < 0)
    {
        // 如果本地仓库不存在，则克隆整个存储库
        git_clone_options clone_opts = GIT_CLONE_OPTIONS_INIT;
        clone_opts.fetch_opts.callbacks.credentials = credentials_callback; // 设置身份验证回调函数
        clone_opts.fetch_opts.callbacks.transfer_progress = transfer_progress_callback; // 设置下载进度回调函数
        if(!checkoutBranch.isEmpty())
        {
            clone_opts.checkout_branch = checkoutBranch.toStdString().c_str();
        }
        error = git_clone(&repo, url, local_path, &clone_opts);
        if (error < 0)
        {
            qDebug() << "Failed to git_clone: " << giterr_last()->message << "url:" << url << " local_path:" << local_path;
            giterr_last();
            git_libgit2_shutdown();
            return 1;
        }
    }
    else
    {
        // 如果本地仓库已存在，则拉取最新的
        git_remote* remote = nullptr;
        error = git_remote_lookup(&remote, repo, "origin");
        if (error < 0)
        {
            qDebug() << "git_remote_lookup: " << giterr_last()->message;
            giterr_last();
            git_repository_free(repo);
            git_libgit2_shutdown();
            return 1;
        }

        git_fetch_options fetch_opts = GIT_FETCH_OPTIONS_INIT;
        git_fetch_options_init(&fetch_opts,GIT_FETCH_OPTIONS_VERSION);
        fetch_opts.callbacks.credentials = credentials_callback;
        fetch_opts.callbacks.transfer_progress = transfer_progress_callback;
        fetch_opts.prune = GIT_FETCH_PRUNE;
//        fetch_opts.prune = GIT_FETCH_PRUNE;

        QString remoteCheckBr("origin/"+checkoutBranch);
//        if (git_branch_lookup(&local_branch, repo, checkoutBranch.toStdString().c_str(), GIT_BRANCH_LOCAL) == 0 &&
//            git_branch_lookup(&remote_branch, repo, remoteCheckBr.toStdString().c_str(), GIT_BRANCH_REMOTE) == 0)
//        {
//            git_branch_set_upstream(local_branch, git_reference_name(remote_branch));
//        }

        error = git_remote_fetch(remote, nullptr, &fetch_opts, nullptr);
        if (error < 0)
        {
            qDebug() << "git_remote_fetch: " << giterr_last()->message;
            giterr_last();
            git_remote_free(remote);
            git_repository_free(repo);
            git_libgit2_shutdown();
            return 1;
        }

        // 必须使用reset处理，否则无法更新。
        git_annotated_commit *remote_head = NULL;
        if (git_revparse_single((git_object **)&remote_head, repo, remoteCheckBr.toStdString().c_str()) == 0)
        {
             ret = git_reset(repo, (git_object *)remote_head, GIT_RESET_HARD, NULL);
             if(ret !=0 )
             {
                 qDebug()<< "update clone failed";
             }
              git_object_free((git_object *)remote_head);
        }


        git_remote_free(remote);
    }

    git_repository_free(repo);
    git_libgit2_shutdown();

    return ret;
}

int GitClient::gitGetBranchesName(const char* local_path, QStringList& branch_names)
{
    git_libgit2_init();
    git_repository* repo = nullptr;
    // 打开本地存储库
    int error = git_repository_open(&repo, local_path);
    if (error < 0) {
        qDebug() << "git_repository_open: " << giterr_last()->message;
        giterr_last();
        git_libgit2_shutdown();
        return 1;
    }

    // 获取分支列表
    git_reference_iterator* iter = nullptr;
    error = git_reference_iterator_new(&iter, repo);
    if (error < 0) {
        qDebug() << "git_reference_iterator_new: " << giterr_last()->message;
        giterr_last();
        git_repository_free(repo);
        git_libgit2_shutdown();
        return 1;
    }

    git_reference* ref = nullptr;
    // 遍历引用列表，找到指定分支并记录它们的名称
    while (git_reference_next(&ref, iter) == 0) {
        const char* ref_name = git_reference_name(ref);
        git_reference_t ref_type = git_reference_type(ref);
        if (!strncmp(ref_name, "refs/remotes/origin/", strlen("refs/remotes/origin/"))) {
            // 找到远程引用，从ref_name中提取分支名称
            if (ref_type == GIT_REFERENCE_SYMBOLIC && !strcmp(ref_name, "refs/remotes/origin/HEAD")) {
                // 跳过 REMOTE HEAD 引用
                continue;
            }
            const char* branch_name = ref_name + strlen("refs/remotes/origin/");
            branch_names << QString::fromUtf8(branch_name);
        }
    }

    // 释放资源
    git_reference_iterator_free(iter);
    git_repository_free(repo);
    git_libgit2_shutdown();

    return 0;
}

int GitClient::gitCheckoutBranch(const char* local_path, const char* branch_name)
{
    int error = 0;
    git_remote* remote = nullptr;
    git_reference* ref = nullptr;
    git_object* target_obj = nullptr;
    git_repository* local_repo = nullptr;
    git_commit* target_commit = nullptr;
    git_reference* new_branch_ref = nullptr;
    git_reference_iterator* iter = nullptr;
    git_reference* head_ref = nullptr;
    // 获取配置
    git_config *cfg = NULL;

    git_libgit2_init();
    // 打开本地仓库
    error = git_repository_open(&local_repo, local_path);
    if (error < 0) {
        qCritical() << "Failed to open local repository: " << giterr_last()->message;
        goto cleanup;
    }

    // 获取配置文件路径
    char config_path[PATH_MAX];
    snprintf(config_path, PATH_MAX, "%s/config", git_repository_path(local_repo));

    error = git_config_open_ondisk(&cfg, config_path);
    if (error != 0) {
       git_repository_free(local_repo);
       git_libgit2_shutdown();
       return 1;
    }

    // 设置换行符配置为LF
    error = git_config_set_bool(cfg, "core.autocrlf", 0);
    if (error != 0) {
        git_config_free(cfg);
        git_repository_free(local_repo);
        git_libgit2_shutdown();
        return 1;
    }

    qDebug() << "local_path:" << local_path <<  "branch_name:" << branch_name;
    // 获取远程名称和URL
    error = git_remote_lookup(&remote, local_repo, "origin");
    if (error < 0) {
        qCritical() << "Failed to lookup remote repository: " << giterr_last()->message;
        goto cleanup;
    }

    // 获取HEAD引用
    error = git_repository_head(&head_ref, local_repo);
    if (error != 0) {
        qCritical() << "Failed to get HEAD reference: " << giterr_last()->message;
        goto cleanup;  // 错误处理
    }

    // 检查HEAD引用类型
    if (git_reference_is_branch(head_ref)) {
        const char* branch_short_name = git_reference_shorthand(head_ref);

        if (0 == strcmp(branch_name, branch_short_name)){
            git_fetch_options fetch_opts = GIT_FETCH_OPTIONS_INIT;
            fetch_opts.callbacks.credentials = credentials_callback;
            fetch_opts.callbacks.transfer_progress = transfer_progress_callback;
            fetch_opts.prune = GIT_FETCH_PRUNE;

            error = git_remote_fetch(remote, nullptr, &fetch_opts, nullptr);
            if (error < 0) {
                qDebug() << "git_remote_fetch: " << giterr_last()->message;
                giterr_last();
                goto cleanup;  // 错误处理
            } else {
                qDebug() << "Current short branch name: " << branch_short_name << " update ";
                goto cleanup;  // 错误处理
            }
        }
    }

    error = git_reference_iterator_new(&iter, local_repo);
    if (error < 0) {
        qCritical() << "Failed to create reference iterator: " << giterr_last()->message;
        goto cleanup;
    }

    const char* ref_name;
    while (git_reference_next(&ref, iter) == 0) {
        if (ref == nullptr) {
            qCritical() << "Failed to git reference next";
            goto cleanup;
        }

        ref_name = git_reference_name(ref);
        int ref_type = git_reference_type(ref);

        if (ref_name == nullptr || (strlen(ref_name) < strlen("refs/remotes/origin/"))) {
            qCritical() << "Failed to get reference name";
            goto cleanup;
        }

        if (ref_type == GIT_REFERENCE_SYMBOLIC && strcmp(ref_name, "refs/remotes/origin/HEAD") == 0) {
            // 跳过 REMOTE HEAD 引用
            continue;
        }

        if (strncmp(ref_name, "refs/remotes/origin/", strlen("refs/remotes/origin/")) != 0) {
            continue;  // 非远程引用
        }

        const char* remote_branch_name = ref_name + strlen("refs/remotes/origin/");
        if (strcmp(remote_branch_name, branch_name) != 0) {
            continue;  // 不是目标分支
        }

        // 下载指定分支
        git_checkout_options checkout_opts = GIT_CHECKOUT_OPTIONS_INIT;

        checkout_opts.progress_cb = checkout_progress_callback; // 设置进度回调函数
        checkout_opts.checkout_strategy = GIT_CHECKOUT_SAFE | GIT_CHECKOUT_RECREATE_MISSING | GIT_CHECKOUT_ALLOW_CONFLICTS;

        if (git_revparse_single(&target_obj, local_repo, ref_name) != 0) {
            qCritical() << "Failed to resolve reference: " << giterr_last()->message;
            goto cleanup;
        }

        error = git_checkout_tree(local_repo, target_obj, &checkout_opts);
        if (error < 0) {
            qCritical() << "Failed to checkout tree: " << giterr_last()->message;
            goto cleanup;
        } else {
            // 切换到指定分支
            qDebug() << "git_checkout_tree successful";

            // 创建新分支
            if (git_object_type(target_obj) == GIT_OBJECT_COMMIT) {
                target_commit = (git_commit*)target_obj;
            }
            else {  // 如果目标对象不是提交类型，则通过其引用解决目标提交
               error = git_commit_lookup(&target_commit, local_repo, git_object_id(target_obj));
               if (error < 0) {
                   qCritical() << "Failed to lookup target commit: " << giterr_last()->message;
                   goto cleanup;
               }
           }

            error = git_branch_create(&new_branch_ref, local_repo, branch_name, target_commit, 0);
            if ((error < 0) && (error != GIT_EEXISTS))  {
                qCritical() << "Failed to create new branch: " << error << "error:" << giterr_last()->message;
                goto cleanup;
            } else {
                qDebug() << "New branch created successfully.";
            }

            // 获取commit的id
            const git_oid *commit_id = git_commit_id(target_commit);
            // 将commit id转换为字符串格式
            git_oid_tostr(m_ConfigCommitID, sizeof(m_ConfigCommitID), commit_id);
            qDebug() << "m_ConfigCommitID " << m_ConfigCommitID;

            // 切换到新分支
            error = git_branch_lookup(&new_branch_ref, local_repo, branch_name, GIT_BRANCH_LOCAL);
            if (error < 0) {
                qCritical() << "Failed to lookup branch: " << giterr_last()->message;
                goto cleanup;
            }
            error = git_repository_set_head(local_repo, git_reference_name(new_branch_ref));
            if (error < 0) {
                qCritical() << "Failed to set repository HEAD: " << giterr_last()->message;
                goto cleanup;  // 错误处理
            } else {
                qDebug() << "Switched to new branch successfully.";
            }
        }
    }

cleanup:
    git_config_free(cfg);
    git_object_free(target_obj);
    git_reference_iterator_free(iter);
    git_reference_free(ref);
    git_reference_free(head_ref);
    git_remote_free(remote);
    git_commit_free(target_commit);
    git_reference_free(new_branch_ref);
    git_config_free(cfg);
    git_repository_free(local_repo);
    git_libgit2_shutdown();
    return error;
}

int GitClient::gitGetConifgLocalBranchesName()
{
    char url[1024] = {0};
    char local_path[256] = {0};
    int ret = 0;

    snprintf(url, sizeof(url), "%s%s.git", CONFIG_URL, m_ConfigProductName.toStdString().c_str());
    snprintf(local_path, sizeof(local_path), "local_%s", m_ConfigProductName.toStdString().c_str());
//    myApp::m_progressBar->setFormat("Config " + m_ConfigProductName);
    ret = gitClone(url, local_path);
    if (ret != 0) {
        qCritical() << "Failed to clone repository url" << url << "local_path " << local_path;
        return 1;
    }

    ret = gitGetBranchesName(local_path, m_ConfigBranchName);
    if (ret != 0) {
        qCritical() << "Failed to get branches";
        return 1;
    }
    return 0;
}

//根据上传的分支信息下载最新的
int GitClient::gitDownloadConifgBranch(QString branch_name)
{
    char check_branch[1024] = {0};
    char local_path[256] = {0};
    int ret = 0;

    snprintf(check_branch, sizeof(check_branch), "%s", branch_name.toStdString().c_str());
    snprintf(local_path, sizeof(local_path), "local_%s", m_ConfigProductName.toStdString().c_str());

//    myApp::m_progressBar->setFormat("Config Branch " + branch_name);
    ret = gitCheckoutBranch(local_path, check_branch);
    if (ret != 0) {
        qCritical() << "Failed to clone Branch repository local_path " << local_path << " branch_name " << branch_name.toStdString().c_str();
        return 1;
    }else{
        qDebug() << "gitDownloadConifgBranch " << branch_name << " successful";
    }
    return ret;
}

int GitClient::gitDownloadConifgBranchCommit(QString branch_name, QString commitID)
{
    char check_branch[1024] = {0};
    char commid_str[1024] = {0};
    char local_path[256] = {0};
    int ret = 0;

    snprintf(check_branch, sizeof(check_branch), "%s", branch_name.toStdString().c_str());
    snprintf(local_path, sizeof(local_path), "local_%s", m_ConfigProductName.toStdString().c_str());
    snprintf(commid_str, sizeof(commid_str), "%s", commitID.toStdString().c_str());

//    myHelper::RemoveFile(output_file);
//    myApp::m_progressBar->setFormat("Config Branch" + branch_name);
    ret = gitCheckoutBranch(local_path, check_branch);
    if (ret != 0) {
        qCritical() << "Failed to clone Branch repository local_path " << local_path << " branch_name " << branch_name.toStdString().c_str();
        return 1;
    }else{
        qDebug() << "gitDownloadConifgBranchCommit " << branch_name << " successful";
    }
    return 1;
//    return git_targz_archive(local_path, commid_str, output_file);
}

int GitClient::gitGetLcdLocalBranchesName()
{
    char url[1024] = {0};
    char local_path[256] = {0};
    int ret = 0;

    snprintf(url, sizeof(url), "%s%s.git", LCD_URL, m_LcdProductName.toStdString().c_str());
    snprintf(local_path, sizeof(local_path), "local_%s_lcd", m_LcdProductName.toStdString().c_str());

//    myApp::m_progressBar->setFormat("Lcd " + m_LcdProductName);
    ret = gitClone(url, local_path);
    if (ret != 0) {
        qCritical() << "Failed to clone repository url" << url << "local_path " << local_path;;
        return 1;
    }

    ret = gitGetBranchesName(local_path, m_LcdBranchName);
    if (ret != 0) {
        qCritical() << "Failed to get branches";
        return 1;
    }

    return 0;
}

void print_branches(git_repository *repo)
{
    git_reference_iterator *iter = nullptr;
    int error = git_reference_iterator_new(&iter, repo);
    if (error != 0) {
        qCritical() << "Failed to create reference iterator: " << giterr_last()->message;
        return;
    }

    git_reference *ref = nullptr;
    while (git_reference_next(&ref, iter) == 0) {
        const char *branch_name = git_reference_name(ref);
        qDebug()<< "print_branches:" << branch_name;
        git_reference_free(ref);
    }

    git_reference_iterator_free(iter);
}

//下载归档分支数据，以Targz格式下载
int GitClient::gitDownloadBranch(const char *local_path, const char *branch_name)
{
    int error = 0;
    git_remote* remote = nullptr;
    git_reference* ref = nullptr;
    git_object* target_obj = nullptr;
    git_repository* local_repo = nullptr;
    git_commit* target_commit = nullptr;
    git_reference* new_branch_ref = nullptr;
    git_reference_iterator* iter = nullptr;
    git_reference* head_ref = nullptr;

    git_libgit2_init();
    // 打开本地仓库
    error = git_repository_open(&local_repo, local_path);
    if (error < 0) {
        qCritical() << "Failed to open local repository: " << giterr_last()->message;
        git_libgit2_shutdown(); // 关闭 libgit2 库
        return 1;
    }

    // 获取远程名称和URL
    error = git_remote_lookup(&remote, local_repo, "origin");
    if (error < 0) {
        qCritical() << "Failed to lookup remote repository: " << giterr_last()->message;
        git_repository_free(local_repo);
        git_libgit2_shutdown(); // 关闭 libgit2 库
        return 1;
    }

    // 获取HEAD引用
    error = git_repository_head(&head_ref, local_repo);
    if (error != 0) {
        qCritical() << "Failed to get HEAD reference: " << giterr_last()->message;
        goto cleanup;  // 错误处理
    }

    // 检查HEAD引用类型
    if (git_reference_is_branch(head_ref)) {
        const char* branch_short_name = git_reference_shorthand(head_ref);

        if (0 == strcmp(branch_name, branch_short_name)){
            git_fetch_options fetch_opts = GIT_FETCH_OPTIONS_INIT;
            fetch_opts.callbacks.credentials = credentials_callback;
            fetch_opts.callbacks.transfer_progress = transfer_progress_callback;
            fetch_opts.prune = GIT_FETCH_PRUNE;

            error = git_remote_fetch(remote, nullptr, &fetch_opts, nullptr);
            if (error < 0) {
                qDebug() << "git_remote_fetch: " << giterr_last()->message;
                giterr_last();
                goto cleanup;  // 错误处理
            } else {
                qDebug() << "Current short branch name: " << branch_short_name << " update ";
                goto cleanup;  // 错误处理
            }
        }
    }

    error = git_reference_iterator_new(&iter, local_repo);
    if (error < 0) {
        qCritical() << "Failed to create reference iterator: " << giterr_last()->message;
        git_remote_free(remote);
        git_repository_free(local_repo);
        git_libgit2_shutdown(); // 关闭 libgit2 库
        return 1;
    }

    error = -1;
    while (git_reference_next(&ref, iter) == 0) {
        git_reference_t ref_type = git_reference_type(ref);
        const char* ref_name = git_reference_name(ref);
        if (!strncmp(ref_name, "refs/remotes/origin/", strlen("refs/remotes/origin/"))) {
            if (ref_type == GIT_REFERENCE_SYMBOLIC && !strcmp(ref_name, "refs/remotes/origin/HEAD")) {
                // 跳过 REMOTE HEAD 引用
                continue;
            }
            const char* remote_branch_name = ref_name + strlen("refs/remotes/origin/");
            if (strcmp(remote_branch_name, branch_name) == 0) {
                //Info() << "Branch name: " << branch_name;

                // 下载指定分支
                git_checkout_options checkout_opts = GIT_CHECKOUT_OPTIONS_INIT;
                checkout_opts.progress_cb = checkout_progress_callback; // 设置进度回调函数
                checkout_opts.checkout_strategy = GIT_CHECKOUT_FORCE;

                if (git_revparse_single(&target_obj, local_repo, ref_name) != 0) {
                    qCritical() << "Failed to resolve reference: " << giterr_last()->message;
                    goto cleanup;
                }

                //qCritical() << "Failed to set repository HEAD: " << git_branch_is_checked_out(ref);

                error = git_checkout_tree(local_repo, target_obj, &checkout_opts);
                if (error < 0) {
                    qCritical() << "Failed to checkout tree: " << giterr_last()->message;
                    goto cleanup;
                } else {
                    // 切换到指定分支
                    qDebug() << "git_checkout_tree successful";

                    // 创建新分支
                    if (git_object_type(target_obj) == GIT_OBJECT_COMMIT) {
                        target_commit = (git_commit*)target_obj;
                    }
                    else {  // 如果目标对象不是提交类型，则通过其引用解决目标提交
                       error = git_commit_lookup(&target_commit, local_repo, git_object_id(target_obj));
                       if (error < 0) {
                           qCritical() << "Failed to lookup target commit: " << giterr_last()->message;
                           goto cleanup;
                       }
                    }

                    error = git_branch_create(&new_branch_ref, local_repo, branch_name, target_commit, 0);
                    if ((error < 0) && (error != GIT_EEXISTS))  {
                        qCritical() << "Failed to create new branch: " << error << "error:"<< giterr_last()->message;
                        goto cleanup;
                    } else {
                        qDebug() << "New branch created successfully.";
                    }

                    // 获取commit的id
                    const git_oid *commit_id = git_commit_id(target_commit);
                    // 将commit id转换为字符串格式
                    git_oid_tostr(m_LcdCommitID, sizeof(m_LcdCommitID), commit_id);
                    qDebug() << "m_LcdCommitID " << m_LcdCommitID;

                    // 切换到新分支
                    error = git_branch_lookup(&new_branch_ref, local_repo, branch_name, GIT_BRANCH_LOCAL);
                    if (error < 0) {
                        qCritical() << "Failed to lookup branch: " << giterr_last()->message;
                        goto cleanup;
                    }

                    error = git_repository_set_head(local_repo, git_reference_name(new_branch_ref));
                    if (error < 0) {
                        qCritical() << "Failed to set repository HEAD: " << giterr_last()->message;
                        goto cleanup;
                    } else {
                        qDebug() << "Switched to new branch successfully.";
                    }
                }
                break;
            }
        }
    }

cleanup:
    git_object_free(target_obj);
    git_reference_iterator_free(iter);
    git_reference_free(ref);
    git_remote_free(remote);
    git_commit_free(target_commit);
    git_reference_free(new_branch_ref);
    git_reference_free(head_ref); // 释放head_ref指针
    git_repository_free(local_repo);
    git_libgit2_shutdown();

    return error;
}

int GitClient::gitDownloadLCDBranch(QString branch_name)
{
    char check_branch[1024] = {0};
    char local_path[256] = {0};
    char url[1024] = {0};
    int ret = 0;

    snprintf(url, sizeof(url), "%s%s.git", LCD_URL, m_LcdProductName.toStdString().c_str());
    snprintf(check_branch, sizeof(check_branch), "%s", branch_name.toStdString().c_str());
    snprintf(local_path, sizeof(local_path), "local_%s_lcd", m_LcdProductName.toStdString().c_str());

//    myApp::m_progressBar->setFormat("LCD Branch " + branch_name);
    ret = gitDownloadBranch(local_path, check_branch);
    if (ret != 0) {
        qCritical() << "Failed to clone Branch repository local_path " << local_path << " branch_name " << check_branch;
        return 1;
    }
    return ret;
}


int GitClient::gitDownloadLCDBranchCommitID(QString branch_name, QString commitID)
{
    char check_branch[1024] = {0};
    char commid_str[1024] = {0};
    char local_path[256] = {0};
    char url[1024] = {0};
    int ret = 0;

    snprintf(url, sizeof(url), "%s%s.git", LCD_URL, m_LcdProductName.toStdString().c_str());
    snprintf(commid_str, sizeof(commid_str), "%s", commitID.toStdString().c_str());
    snprintf(check_branch, sizeof(check_branch), "%s", branch_name.toStdString().c_str());
    snprintf(local_path, sizeof(local_path), "local_%s_lcd", m_LcdProductName.toStdString().c_str());

//    myApp::m_progressBar->setFormat("LCD Branch " + branch_name);
    ret = gitDownloadBranch(local_path, check_branch);
    if (ret != 0) {
        qCritical() << "Failed to clone Branch repository local_path " << local_path << " branch_name " << check_branch;
        return 1;
    }
//    myHelper::RemoveFile(output_file);
    return 1;
//    return git_targz_archive(local_path, commid_str, output_file);
}

//已经存在的情况下，重新调用，更新分支无效。
int GitClient::cloneRepo(const QString & saveLoclPath,const QString & branch)
{
    QString repoURL(gitURL + gitPath);
    qDebug()<<"git url"<<repoURL;
    int ret = gitClone(repoURL.toStdString().c_str(), saveLoclPath.toStdString().c_str(),branch);
    if (ret != 0)
    {
        qCritical() << "Failed to clone "<<branch;
        return 1;
    }
    return ret;
}
int GitClient::gitCloneProductSourceXml()
{
    char url[1024] = {0};
    char local_path[256] = {0};
    int ret = 0;

    snprintf(url, sizeof(url), "%s", PRODUCT_SOURCE_URL);
    snprintf(local_path, sizeof(local_path), "%s", "AppMarketMappingList");
/*    myApp::m_progressBar->setFormat("AppMarketMappingList");
    myHelper::removeDirectory(local_path)*/;
    ret = gitClone(url, local_path);
    if (ret != 0) {
        qCritical() << "Failed to clone ProductSourceXml ";
        return 1;
    }
    return ret;
}

int GitClient::gitGetDefaultConifgLocal(QString branch_name)
{
    int ret = 0;
    char url[1024] = {0};
    char check_branch[1024] = {0};
    char local_path[256] = {0};

    snprintf(url, sizeof(url), "%s%s.git", CONFIG_URL, m_ConfigProductName.toStdString().c_str());
    snprintf(local_path, sizeof(local_path), "local_%s", m_ConfigProductName.toStdString().c_str());
//    myHelper::RemoveFile(output_file);
//    myHelper::removeDirectory(local_path);

    ret = gitClone(url, local_path);
//    myApp::m_progressBar->setFormat("Config Branch " + branch_name);
    ret = gitCheckoutBranch(local_path, check_branch);
    if (ret != 0) {
        qCritical() << "Failed to clone gitGetDefaultConifgLocal local_path " << local_path << " branch_name " << branch_name.toStdString().c_str();
        return 1;
    }else{
        qDebug() << "gitGetDefaultConifgLocal " << branch_name << " successful";
    }
    return 1;
//    return git_targz_archive(local_path, check_branch, output_file);
}

int GitClient::checkForUpdates(const char* localPath, const char* remote)
{
    int ret = 0;

    git_libgit2_init();

    git_repository* repo = nullptr;
    git_remote* rmt = nullptr;

    // 打开本地仓库
    ret = git_repository_open(&repo, localPath);
    if (ret != GIT_OK) {
        qCritical() << "Failed to open repository";
        git_libgit2_shutdown();
        return -1;
    }

    // 获取远程信息
    ret = git_remote_lookup(&rmt, repo, remote);
    if (ret != GIT_OK) {
        qCritical() << "Failed rmt to lookup remote" << remote;
        git_repository_free(repo);
        git_libgit2_shutdown();
        return -1;
    }

    // 检查是否有更新
    git_fetch_options fetch_opts = GIT_FETCH_OPTIONS_INIT;
    fetch_opts.callbacks.credentials = credentials_callback;
    fetch_opts.callbacks.transfer_progress = transfer_progress_callback;
    ret = git_remote_fetch(rmt, nullptr, &fetch_opts, nullptr);

    if (ret == GIT_OK) {
        git_oid local_oid, remote_oid;
        ret = git_reference_name_to_id(&local_oid, repo, "HEAD");
        if (ret != GIT_OK) {
            qCritical() << "Failed to get local reference";
            ret = -1;
        } else {
            ret = git_reference_name_to_id(&remote_oid, repo, "FETCH_HEAD");
            if (ret != GIT_OK) {
                qCritical() << "Failed to get remote reference";
                ret = -1;
            } else {
                if (git_oid_cmp(&local_oid, &remote_oid) == 0) {
                    qDebug() << "Repository is up to date.";
                    ret = 0;
                } else {
                    qDebug() << "Repository has updates.";
                    ret = 1;
                }
            }
        }
    } else {
        qCritical() << "Failed to fetch updates from" << remote;
        ret = -1;
    }

    git_remote_free(rmt);
    git_repository_free(repo);
    git_libgit2_shutdown();

    return ret;
}

int GitClient::gitCheckProductSourceXmlForUpdates()
{
    char local_path[256] = {0};

    snprintf(local_path, sizeof(local_path), "%s", "AppMarketMappingList");
    return checkForUpdates(local_path, "origin");
}
